{"name": "perfect-piste", "type": "module", "version": "0.0.1", "scripts": {"dev": "astro dev", "dev:port": "NODE_OPTIONS='--no-warnings --no-node-snapshot' ASTRO_NODE_AUTODETECT=false ASTRO_DISABLE_NODE_VERSION_CHECK=true NODE_VERSION_CHECK=false astro dev --port 3000 --host", "build": "astro build", "preview": "astro preview", "astro": "astro"}, "dependencies": {"@astrojs/react": "^4.2.5", "@astrojs/tailwind": "^6.0.2", "@astrojs/vercel": "^8.1.4", "@copilotkit/react-core": "^1.8.9", "@copilotkit/react-ui": "^1.8.9", "@copilotkit/runtime-client-gql": "^1.8.9", "@radix-ui/react-dialog": "^1.1.10", "@radix-ui/react-label": "^2.1.4", "@radix-ui/react-popover": "^1.1.10", "@radix-ui/react-switch": "^1.2.2", "@stripe/react-stripe-js": "^3.6.0", "@stripe/stripe-js": "^7.2.0", "@types/react": "^18.2.48", "@types/react-dom": "^18.2.18", "@vitejs/plugin-basic-ssl": "^2.0.0", "astro": "^5.7.5", "autoprefixer": "^10.4.16", "boring-avatars": "^1.11.2", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.503.0", "motion": "^12.9.4", "react": "^18.2.0", "react-day-picker": "^9.6.7", "react-dom": "^18.2.0", "react-joyride": "^2.9.3", "tailwind-merge": "^3.2.0", "tailwindcss": "^3.4.1"}, "packageManager": "yarn@1.22.22+sha512.a6b2f7906b721bba3d67d4aff083df04dad64c399707841b7acf00f6b133b7ac24255f2652fa22ae3534329dc6180534e98d17432037ff6fd140556e2bb3137e"}