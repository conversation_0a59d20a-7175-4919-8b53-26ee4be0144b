---
import { useTranslations } from "../../i18n/utils";
import type { <PERSON> } from "../../i18n/ui";

// Props for the component
interface Props {
  imageUrl: string;
  lang: Lang;
}

const {
  imageUrl = "https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80",
  lang,
} = Astro.props;

const t = useTranslations(lang);
---

<!-- Our Story -->
<section class="py-20 container-custom">
  <div class="mx-auto">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        <h2 class="text-3xl md:text-4xl font-baskervville mb-6">
          {t("about.story.title")}
        </h2>
        <p class="text-lg mb-6">
          {t("about.story.paragraph1")}
        </p>
        <p class="mb-6">
          {t("about.story.paragraph2")}
        </p>
        <p>
          {t("about.story.paragraph3")}
        </p>
      </div>
      <div class="order-first lg:order-last">
        <div class="relative h-96 rounded-lg overflow-hidden">
          <img
            src={imageUrl}
            alt="Luxury Ski Resort"
            class="w-full h-full object-cover"
          />
        </div>
      </div>
    </div>
  </div>
</section>
