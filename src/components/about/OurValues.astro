---
import { getLangFromUrl, useTranslations } from "../../i18n/utils";

const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<!-- Our Values -->
<section class="py-20 bg-white container-custom">
  <div class="mx-auto">
    <div class="max-w-3xl mx-auto text-center mb-16">
      <h2 class="text-3xl md:text-4xl font-baskervville mb-6">
        {t("about.values.title")}
      </h2>
      <p class="text-lg">
        {t("about.values.subtitle")}
      </p>
    </div>

    <div class="grid grid-cols-1 md:grid-cols-3 gap-8">
      <div class="text-center p-6">
        <div
          class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-[#285DA6] text-white"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><path
              d="M4 5a2 2 0 0 1 2-2h12a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2V5Z"
            ></path><path d="M12 18h.01"></path><path d="M8 18h.01"></path><path
              d="M16 18h.01"></path><path d="M12 14h.01"></path><path
              d="M8 14h.01"></path><path d="M16 14h.01"></path><path
              d="M12 10h.01"></path><path d="M8 10h.01"></path><path
              d="M16 10h.01"></path></svg
          >
        </div>
        <h3 class="text-xl font-baskervville mb-3">
          {t("about.values.curation.title")}
        </h3>
        <p>
          {t("about.values.curation.description")}
        </p>
      </div>

      <div class="text-center p-6">
        <div
          class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-[#285DA6] text-white"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><path d="M19 9V6a2 2 0 0 0-2-2H7a2 2 0 0 0-2 2v3"></path><path
              d="M3 11v5a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-5a2 2 0 0 0-4 0v2H7v-2a2 2 0 0 0-4 0Z"
            ></path><path d="M5 18v2"></path><path d="M19 18v2"></path></svg
          >
        </div>
        <h3 class="text-xl font-baskervville mb-3">
          {t("about.values.service.title")}
        </h3>
        <p>
          {t("about.values.service.description")}
        </p>
      </div>

      <div class="text-center p-6">
        <div
          class="w-16 h-16 mx-auto mb-4 flex items-center justify-center rounded-full bg-[#285DA6] text-white"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
            ><circle cx="12" cy="12" r="10"></circle><path d="m16 12-4-4-4 4"
            ></path><path d="m16 12-4 4-4-4"></path></svg
          >
        </div>
        <h3 class="text-xl font-baskervville mb-3">
          {t("about.values.immersion.title")}
        </h3>
        <p>
          {t("about.values.immersion.description")}
        </p>
      </div>
    </div>
  </div>
</section>
