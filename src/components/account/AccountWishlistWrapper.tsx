import React from 'react';
import { useWishlist } from '../wishlist/useWishlist';
import WishlistItem from '../wishlist/WishlistItem';

const AccountWishlistWrapper: React.FC = () => {
  const { wishlist, removeFromWishlist, clearWishlist } = useWishlist();

  // Empty state component for account context
  const WishlistEmptyState = () => (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-12">
      <div className="flex flex-col items-center justify-center text-center">
        <div className="w-16 h-16 mb-6 text-gray-300">
          <svg 
            xmlns="http://www.w3.org/2000/svg" 
            width="64" 
            height="64" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            strokeWidth="1" 
            strokeLinecap="round" 
            strokeLinejoin="round"
          >
            <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
          </svg>
        </div>
        
        <h3 className="text-xl font-semibold text-gray-900 mb-3">Your wishlist is empty</h3>
        
        <p className="text-gray-600 max-w-md mb-8">
          Save your favorite properties and experiences by clicking the heart icon while browsing our collection.
        </p>
        
        <a 
          href="/stays" 
          className="inline-flex items-center justify-center px-6 py-3 bg-[#285DA6] text-white rounded-xl font-medium text-sm hover:bg-[#285DA6]/90 transition-colors"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
            <polyline points="9 22 9 12 15 12 15 22"></polyline>
          </svg>
          Explore Properties
        </a>
      </div>
    </div>
  );

  return (
    <div className="space-y-6">
      {/* Header Section */}
      <div className="bg-white rounded-2xl p-8 border border-gray-100 shadow-sm">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-2xl font-semibold text-gray-900 mb-1">
              My Wishlist
            </h1>
            <p className="text-gray-600">
              Your saved properties and experiences
            </p>
          </div>
          <div className="hidden md:block">
            <div className="w-16 h-16 bg-[#285DA6]/5 rounded-2xl flex items-center justify-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="32"
                height="32"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      {/* Wishlist Content */}
      {wishlist.length === 0 ? (
        <WishlistEmptyState />
      ) : (
        <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
          {/* Wishlist Header with Count and Clear Button */}
          <div className="px-6 py-4 border-b border-gray-100">
            <div className="flex justify-between items-center">
              <div>
                <p className="text-sm text-gray-600 mt-1">
                  {wishlist.length} {wishlist.length === 1 ? 'item' : 'items'} saved
                </p>
              </div>
              <button
                onClick={clearWishlist}
                className="px-4 py-2 text-sm font-medium text-red-600 hover:text-red-700 hover:bg-red-50 rounded-lg transition-colors"
              >
                Clear All
              </button>
            </div>
          </div>

          {/* Wishlist Grid */}
          <div className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {wishlist.map((item) => (
                <div key={item.id} className="transform transition-transform hover:scale-[1.02]">
                  <WishlistItem 
                    item={item} 
                    onRemove={() => removeFromWishlist(item.id)} 
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default AccountWishlistWrapper;
