import React, { useState, useEffect, useRef } from "react";
import type { Booking, PaginationState } from "../../types/booking";

interface MyTripsTableProps {
  bookings: Booking[];
  loading: boolean;
  onBookingClick: (booking: Booking) => void;
  pagination: PaginationState;
  onPageChange: (page: number) => void;
  sortBy: "date" | "amount" | "status" | "created_at";
  sortOrder: "asc" | "desc";
  onSortChange: (
    sortBy: "date" | "amount" | "status" | "created_at",
    sortOrder: "asc" | "desc"
  ) => void;
  activeTab: "ongoing" | "upcoming" | "completed" | "cancelled";
  onTabChange: (tab: "ongoing" | "upcoming" | "completed" | "cancelled") => void;
  upcomingTripsCount?: number;
  ongoingTripsCount?: number;
  completedTripsCount?: number;
  cancelledTripsCount?: number;
}

// Reusable Tabs Component
const TabsComponent: React.FC<{
  activeTab: "ongoing" | "upcoming" | "completed" | "cancelled";
  onTabChange: (tab: "ongoing" | "upcoming" | "completed" | "cancelled") => void;
  upcomingTripsCount?: number;
  ongoingTripsCount?: number;
  completedTripsCount?: number;
  cancelledTripsCount?: number;
}> = ({ activeTab, onTabChange, upcomingTripsCount, ongoingTripsCount, completedTripsCount, cancelledTripsCount }) => (
  <div className="px-6 pt-6 pb-0">
    <div className="grid grid-cols-4 gap-1 bg-gray-100 p-1 rounded-xl">
      <button
        onClick={() => onTabChange("ongoing")}
        className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          activeTab === "ongoing"
            ? "bg-white text-[#285DA6] shadow-sm"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
        }`}
      >
        <span className="flex items-center justify-center gap-2">
          Ongoing
          {ongoingTripsCount !== undefined && (
            <span className={`inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium rounded-full ${
              activeTab === "ongoing"
                ? "bg-[#285DA6] text-white"
                : "bg-gray-200 text-gray-600"
            }`}>
              {ongoingTripsCount}
            </span>
          )}
        </span>
      </button>
      <button
        onClick={() => onTabChange("upcoming")}
        className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          activeTab === "upcoming"
            ? "bg-white text-[#285DA6] shadow-sm"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
        }`}
      >
        <span className="flex items-center justify-center gap-2">
          Upcoming
          {upcomingTripsCount !== undefined && (
            <span className={`inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium rounded-full ${
              activeTab === "upcoming"
                ? "bg-[#285DA6] text-white"
                : "bg-gray-200 text-gray-600"
            }`}>
              {upcomingTripsCount}
            </span>
          )}
        </span>
      </button>
      <button
        onClick={() => onTabChange("completed")}
        className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          activeTab === "completed"
            ? "bg-white text-[#285DA6] shadow-sm"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
        }`}
      >
        <span className="flex items-center justify-center gap-2">
          Completed
          {completedTripsCount !== undefined && (
            <span className={`inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium rounded-full ${
              activeTab === "completed"
                ? "bg-[#285DA6] text-white"
                : "bg-gray-200 text-gray-600"
            }`}>
              {completedTripsCount}
            </span>
          )}
        </span>
      </button>
      <button
        onClick={() => onTabChange("cancelled")}
        className={`px-3 py-2 text-sm font-medium rounded-lg transition-all duration-200 ${
          activeTab === "cancelled"
            ? "bg-white text-[#285DA6] shadow-sm"
            : "text-gray-600 hover:text-gray-900 hover:bg-gray-50"
        }`}
      >
        <span className="flex items-center justify-center gap-2">
          Cancelled
          {cancelledTripsCount !== undefined && (
            <span className={`inline-flex items-center justify-center px-2 py-0.5 text-xs font-medium rounded-full ${
              activeTab === "cancelled"
                ? "bg-[#285DA6] text-white"
                : "bg-gray-200 text-gray-600"
            }`}>
              {cancelledTripsCount}
            </span>
          )}
        </span>
      </button>
    </div>
  </div>
);

const MyTripsTable: React.FC<MyTripsTableProps> = ({
  bookings,
  loading,
  onBookingClick,
  pagination,
  onPageChange,
  sortBy,
  sortOrder,
  onSortChange,
  activeTab,
  onTabChange,
  upcomingTripsCount,
  ongoingTripsCount,
  completedTripsCount,
  cancelledTripsCount,
}) => {
  const [showSortDropdown, setShowSortDropdown] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setShowSortDropdown(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const formatDisplayDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      return date.toLocaleDateString("en-US", {
        weekday: "short",
        month: "short",
        day: "numeric",
        year: "numeric",
      });
    } catch (error) {
      return dateString;
    }
  };

  const getTripStatus = (checkInDate: string, checkOutDate: string): "Upcoming" | "Ongoing" | "Completed" => {
    const today = new Date();
    const checkIn = new Date(checkInDate);
    const checkOut = new Date(checkOutDate);

    // Set time to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    checkIn.setHours(0, 0, 0, 0);
    checkOut.setHours(0, 0, 0, 0);

    if (today < checkIn) {
      return "Upcoming";
    } else if (today >= checkIn && today <= checkOut) {
      return "Ongoing";
    } else {
      return "Completed";
    }
  };

  const StatusBadge: React.FC<{ status: "Upcoming" | "Ongoing" | "Completed" }> = ({
    status,
  }) => {
    const getStatusStyles = () => {
      switch (status) {
        case "Upcoming":
          return {
            badge: "bg-blue-100 text-blue-800",
            dot: "bg-blue-400"
          };
        case "Ongoing":
          return {
            badge: "bg-green-100 text-green-800",
            dot: "bg-green-400"
          };
        case "Completed":
          return {
            badge: "bg-gray-100 text-gray-800",
            dot: "bg-gray-400"
          };
        default:
          return {
            badge: "bg-gray-100 text-gray-800",
            dot: "bg-gray-400"
          };
      }
    };

    const styles = getStatusStyles();

    return (
      <span
        className={`inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${styles.badge}`}
      >
        <span
          className={`w-1.5 h-1.5 rounded-full mr-1.5 ${styles.dot}`}
        />
        {status}
      </span>
    );
  };

  // Bookings are already sorted by parent component, no need to sort again

  if (loading) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100 p-8">
        <div className="flex items-center justify-center">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-[#285DA6]"></div>
          <span className="ml-3 text-gray-600">Loading your trips...</span>
        </div>
      </div>
    );
  }

  // Show "Coming Soon" message for cancelled tab
  if (activeTab === "cancelled") {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        <TabsComponent
          activeTab={activeTab}
          onTabChange={onTabChange}
          upcomingTripsCount={upcomingTripsCount}
          ongoingTripsCount={ongoingTripsCount}
          completedTripsCount={completedTripsCount}
          cancelledTripsCount={cancelledTripsCount}
        />

        {/* Coming Soon Message */}
        <div className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-orange-100 rounded-2xl flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-orange-600"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <polyline points="12 6 12 12 16 14"></polyline>
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">Coming Soon</h3>
          <p className="text-gray-500">
            Cancelled bookings functionality is currently under development.
          </p>
        </div>
      </div>
    );
  }

  if (bookings.length === 0) {
    return (
      <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
        <TabsComponent
          activeTab={activeTab}
          onTabChange={onTabChange}
          upcomingTripsCount={upcomingTripsCount}
          ongoingTripsCount={ongoingTripsCount}
          completedTripsCount={completedTripsCount}
          cancelledTripsCount={cancelledTripsCount}
        />

        <div className="p-12 text-center">
          <div className="w-16 h-16 mx-auto mb-4 bg-[#285DA6]/10 rounded-2xl flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#285DA6]"
            >
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
          </div>
          <h3 className="text-lg font-medium text-gray-900 mb-2">
            {activeTab === "upcoming" && "No upcoming trips"}
            {activeTab === "ongoing" && "No ongoing trips"}
            {activeTab === "completed" && "No completed trips"}
            {activeTab === "cancelled" && "No cancelled trips"}
          </h3>
          <p className="text-gray-500 mb-6">
            {activeTab === "upcoming" && "Start planning your perfect ski holiday today!"}
            {activeTab === "ongoing" && "Your active trips will appear here."}
            {activeTab === "completed" && "Your completed trips will appear here."}
            {activeTab === "cancelled" && "Your cancelled trips will appear here."}
          </p>
          {(activeTab === "upcoming" || activeTab === "ongoing") && (
            <a
              href="/search"
              className="inline-flex items-center px-6 py-3 bg-[#285DA6] text-white rounded-xl hover:bg-[#1A3A6E] hover:shadow-md transition-all duration-200 font-medium"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                <polyline points="9 22 9 12 15 12 15 22"></polyline>
              </svg>
              Browse Hotels
            </a>
          )}
        </div>
      </div>
    );
  }

  return (
    <div className="bg-white rounded-2xl shadow-sm border border-gray-100">
      <TabsComponent
        activeTab={activeTab}
        onTabChange={onTabChange}
        upcomingTripsCount={upcomingTripsCount}
        ongoingTripsCount={ongoingTripsCount}
        completedTripsCount={completedTripsCount}
        cancelledTripsCount={cancelledTripsCount}
      />

      {/* Header with sorting - Modernized */}
      {/* <div className="px-6 py-4 border-b border-gray-100">
        <div className="flex items-center justify-between">
          <h2 className="text-lg font-semibold text-gray-900">My Trips</h2>
          <div className="relative" ref={dropdownRef}>
            <button
              onClick={() => setShowSortDropdown(!showSortDropdown)}
              className="flex items-center space-x-2 px-4 py-2 bg-gray-50 border border-gray-200 rounded-xl hover:bg-gray-100 transition-colors text-sm"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-gray-500"
              >
                <path d="M3 6h18M7 12h10m-7 6h4"></path>
              </svg>
              <span className="text-gray-700">
                {sortBy === "date" && sortOrder === "desc" && "Recently Booked"}
                {sortBy === "date" && sortOrder === "asc" && "Oldest Bookings"}
                {sortBy === "amount" &&
                  sortOrder === "desc" &&
                  "Highest Amount"}
                {sortBy === "amount" && sortOrder === "asc" && "Lowest Amount"}
                {sortBy === "status" &&
                  sortOrder === "desc" &&
                  "Upcoming First"}
                {sortBy === "status" &&
                  sortOrder === "asc" &&
                  "Completed First"}
                {sortBy === "created_at" &&
                  sortOrder === "desc" &&
                  "Recently Created"}
                {sortBy === "created_at" &&
                  sortOrder === "asc" &&
                  "Oldest Created"}
              </span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className={`text-gray-400 transition-transform ${
                  showSortDropdown ? "rotate-180" : ""
                }`}
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            </button>

            {showSortDropdown && (
              <div className="absolute right-0 top-full mt-2 w-48 bg-white border border-gray-200 rounded-xl shadow-lg z-10">
                <div className="py-2">
                  {[
                    { key: "date-desc", label: "Recently Booked", icon: "📅" },
                    { key: "date-asc", label: "Oldest Bookings", icon: "📅" },
                    { key: "amount-desc", label: "Highest Amount", icon: "💰" },
                    { key: "amount-asc", label: "Lowest Amount", icon: "💰" },
                    { key: "status-desc", label: "Upcoming First", icon: "🎯" },
                    { key: "status-asc", label: "Completed First", icon: "✅" },
                    { key: "created_at-desc", label: "Recently Created", icon: "🕒" },
                    { key: "created_at-asc", label: "Oldest Created", icon: "🕒" },
                  ].map((option) => (
                    <button
                      key={option.key}
                      onClick={() => {
                        const [column, order] = option.key.split("-");
                        onSortChange(
                          column as "date" | "amount" | "status" | "created_at",
                          order as "asc" | "desc"
                        );
                        setShowSortDropdown(false);
                      }}
                      className={`w-full flex items-center space-x-3 px-4 py-2 text-left hover:bg-gray-50 transition-colors ${
                        `${sortBy}-${sortOrder}` === option.key
                          ? "bg-[#285DA6]/10 text-[#285DA6]"
                          : "text-gray-700"
                      }`}
                    >
                      <span>{option.icon}</span>
                      <span className="text-sm">{option.label}</span>
                    </button>
                  ))}
                </div>
              </div>
            )}
          </div>
        </div>
      </div> */}

      {/* Desktop Table View */}
      <div className="hidden lg:block mt-4">
        <table className="w-full">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Hotel & Room
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Check-in
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Check-out
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Created At
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Amount
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {bookings.map((booking) => (
              <tr
                key={booking.id}
                className="hover:bg-gray-50 transition-colors cursor-pointer"
                onClick={() => onBookingClick(booking)}
              >
                <td className="px-6 py-4">
                  <div>
                    <div className="font-medium text-gray-900">
                      {booking.hotel_name}
                    </div>
                    <div className="text-sm text-gray-500">
                      {booking.room_config_name || booking.room_type}
                    </div>
                  </div>
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatDisplayDate(booking.check_in_date)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {formatDisplayDate(booking.check_out_date)}
                </td>
                <td className="px-6 py-4 text-sm text-gray-900">
                  {booking.created_at ? formatDisplayDate(booking.created_at) : "N/A"}
                </td>
                <td className="px-6 py-4">
                  <StatusBadge status={getTripStatus(booking.check_in_date, booking.check_out_date)} />
                </td>
                <td className="px-6 py-4 text-sm font-medium text-gray-900">
                  {booking.currency_code.toUpperCase()}{" "}
                  {booking.total_amount.toFixed(2)}
                </td>
                <td className="px-6 py-4">
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      onBookingClick(booking);
                    }}
                    className="text-[#285DA6] hover:text-[#1A3A6E] font-medium text-sm hover:bg-[#285DA6]/10 px-3 py-1 rounded-lg transition-colors"
                  >
                    View Details
                  </button>
                </td>
              </tr>
            ))}
          </tbody>
        </table>
      </div>

      {/* Mobile Card View - Modernized */}
      <div className="lg:hidden divide-y divide-gray-100">
        {bookings.map((booking) => (
          <div
            key={booking.id}
            onClick={() => onBookingClick(booking)}
            className="p-6 hover:bg-gray-50 transition-colors cursor-pointer"
          >
            <div className="flex items-start justify-between mb-3">
              <div className="flex-1">
                <h3 className="font-medium text-gray-900 mb-1">
                  {booking.hotel_name}
                </h3>
                <p className="text-sm text-gray-500">
                  {booking.room_config_name || booking.room_type}
                </p>
              </div>
              <StatusBadge status={getTripStatus(booking.check_in_date, booking.check_out_date)} />
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">
                  Check-in
                </p>
                <p className="font-medium">
                  {formatDisplayDate(booking.check_in_date)}
                </p>
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">
                  Check-out
                </p>
                <p className="font-medium">
                  {formatDisplayDate(booking.check_out_date)}
                </p>
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4 mb-4">
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">
                  Created At
                </p>
                <p className="font-medium">
                  {booking.created_at ? formatDisplayDate(booking.created_at) : "N/A"}
                </p>
              </div>
              <div>
                {/* Empty div for grid alignment */}
              </div>
            </div>

            <div className="flex items-center justify-between pt-3 border-t border-gray-100">
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">
                  Total Amount
                </p>
                <p className="font-medium text-gray-900">
                  {booking.currency_code.toUpperCase()}{" "}
                  {booking.total_amount.toFixed(2)}
                </p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  onBookingClick(booking);
                }}
                className="px-4 py-2 bg-[#285DA6]/10 text-[#285DA6] rounded-xl hover:bg-[#285DA6]/20 hover:shadow-sm transition-all duration-200 text-sm font-medium"
              >
                View Details
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination - Modernized */}
      {pagination.totalPages > 1 && (
        <div className="px-6 py-4 border-t border-gray-100">
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Page {pagination.currentPage} of {pagination.totalPages}
            </div>
            <div className="flex space-x-2">
              <button
                onClick={() => onPageChange(pagination.currentPage - 1)}
                disabled={pagination.currentPage === 1}
                className="px-4 py-2 border border-gray-200 rounded-xl text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 hover:shadow-sm transition-all duration-200"
              >
                Previous
              </button>
              <button
                onClick={() => onPageChange(pagination.currentPage + 1)}
                disabled={pagination.currentPage === pagination.totalPages}
                className="px-4 py-2 border border-gray-200 rounded-xl text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50 hover:shadow-sm transition-all duration-200"
              >
                Next
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default MyTripsTable;
