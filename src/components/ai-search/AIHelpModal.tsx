import React, { useEffect, useRef } from "react";
import "../../styles/ai-help-modal.css";
import { useTranslations } from "../../i18n/utils";

interface AIHelpModalProps {
  isOpen: boolean;
  onClose: () => void;
  lang?: string;
}

const AIHelpModal: React.FC<AIHelpModalProps> = ({
  isOpen,
  onClose,
  lang = "en",
}) => {
  const modalRef = useRef<HTMLDivElement>(null);
  const t = useTranslations(lang as any);
  const defaultExample = t("aiHelp.modal.tryExample");

  // Handle escape key to close modal
  useEffect(() => {
    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === "Escape" && isOpen) {
        onClose();
      }
    };

    window.addEventListener("keydown", handleEscape);
    return () => window.removeEventListener("keydown", handleEscape);
  }, [isOpen, onClose]);

  // Prevent scrolling when modal is open
  useEffect(() => {
    if (isOpen) {
      document.body.style.overflow = "hidden";
    } else {
      document.body.style.overflow = "";
    }
    return () => {
      document.body.style.overflow = "";
    };
  }, [isOpen]);

  // Handle clicking outside to close
  const handleOverlayClick = (e: React.MouseEvent) => {
    if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
      onClose();
    }
  };

  // Handle example prompt click - navigate to ai-search page
  const handleExampleClick = (example: string) => {
    // Close the modal
    onClose();

    // Build the search URL with the example as the query
    const params = new URLSearchParams();
    params.set("query", example);
    params.set("user_message", example);
    params.set("ai_search", "true");

    // Navigate to the ai-search page
    window.location.href = `/ai-search?${params.toString()}`;
  };

  return (
    <div
      className={`ai-help-modal-overlay ${isOpen ? "visible" : ""}`}
      onClick={handleOverlayClick}
    >
      <div className="ai-help-modal-container" ref={modalRef}>
        <button
          className="ai-help-modal-close"
          onClick={onClose}
          aria-label={t("aiHelp.modal.close")}
        >
          ✕
        </button>

        <div className="ai-help-modal-content">
          <div className="ai-help-modal-header">
            <h2 className="ai-help-modal-title">{t("aiHelp.modal.title")}</h2>
          </div>

          <div className="ai-help-modal-section">
            <h3 className="ai-help-modal-section-title">
              {t("aiHelp.modal.whatCanIType")}
            </h3>
            <p className="ai-help-modal-section-content">
              {t("aiHelp.modal.description")}
            </p>
            <p className="ai-help-modal-section-content">
              {t("aiHelp.modal.tryLike")}
            </p>
            <div className="ai-help-example-prompts">
              <div
                className="ai-help-example-prompt"
                onClick={() => handleExampleClick(t("aiHelp.modal.example1"))}
              >
                {t("aiHelp.modal.example1")}
              </div>
              <div
                className="ai-help-example-prompt"
                onClick={() => handleExampleClick(t("aiHelp.modal.example2"))}
              >
                {t("aiHelp.modal.example2")}
              </div>
              <div
                className="ai-help-example-prompt"
                onClick={() => handleExampleClick(t("aiHelp.modal.example3"))}
              >
                {t("aiHelp.modal.example3")}
              </div>
              <div
                className="ai-help-example-prompt"
                onClick={() => handleExampleClick(t("aiHelp.modal.example4"))}
              >
                {t("aiHelp.modal.example4")}
              </div>
            </div>
          </div>

          <div className="ai-help-modal-section">
            <h3 className="ai-help-modal-section-title">
              {t("aiHelp.modal.whatDoesItDo")}
            </h3>
            <p className="ai-help-modal-section-content">
              {t("aiHelp.modal.aiDescription")}
            </p>
            <ul className="ai-help-modal-section-content">
              <li>{t("aiHelp.modal.feature1")}</li>
              <li>{t("aiHelp.modal.feature2")}</li>
              <li>{t("aiHelp.modal.feature3")}</li>
            </ul>
          </div>

          <div className="ai-help-modal-section">
            <div className="ai-help-pro-tips">
              <h3 className="ai-help-pro-tips-title">
                {t("aiHelp.modal.proTips")}
              </h3>
              <ul className="ai-help-pro-tips-list">
                <li>{t("aiHelp.modal.tip1")}</li>
                <li>{t("aiHelp.modal.tip2")}</li>
                <li>{t("aiHelp.modal.tip3")}</li>
              </ul>
            </div>
          </div>

          <button
            className="ai-help-try-example-button"
            onClick={() => handleExampleClick(defaultExample)}
          >
            {defaultExample}
          </button>
        </div>
      </div>
    </div>
  );
};

export default AIHelpModal;
