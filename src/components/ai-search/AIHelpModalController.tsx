import React, { useState, useEffect } from "react";
import AIHelpModal from "./AIHelpModal";
import OnboardingTourController from "./OnboardingTourController";
import { getLangFromUrl } from "../../i18n/utils";

interface AIHelpModalControllerProps {}

const AIHelpModalController: React.FC<AIHelpModalControllerProps> = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [lang, setLang] = useState("en");

  // Get current language from URL safely on client side
  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentLang = getLangFromUrl(new URL(window.location.href));
      setLang(currentLang);
    }
  }, []);

  useEffect(() => {
    // Add the openAIHelpModal function to the window object (for backward compatibility)
    (window as any).openAIHelpModal = () => {
      // Start the onboarding tour instead of opening the modal
      if ((window as any).startOnboardingTour) {
        (window as any).startOnboardingTour();
      } else {
        // Fallback to modal if tour is not available
        setIsOpen(true);
      }
    };

    // Cleanup function
    return () => {
      (window as any).openAIHelpModal = undefined;
    };
  }, []);

  // Find all search input fields and set up a function to update them
  useEffect(() => {
    // Create a global function to set search input value
    (window as any).setAISearchInputValue = (value: string) => {
      // Find all AI search input fields
      const searchInputs = document.querySelectorAll(".ai-search-input");

      // Update each input field found
      searchInputs.forEach((element) => {
        const input = element as HTMLInputElement;
        if (input && input.tagName === "INPUT") {
          input.value = value;

          // Create and dispatch an input event to trigger React's onChange
          const event = new Event("input", { bubbles: true });
          input.dispatchEvent(event);
        }
      });
    };

    // Cleanup
    return () => {
      (window as any).setAISearchInputValue = undefined;
    };
  }, []);

  const handleClose = () => {
    setIsOpen(false);
  };

  return (
    <>
      <OnboardingTourController />
      <AIHelpModal isOpen={isOpen} onClose={handleClose} lang={lang} />
    </>
  );
};

export default AIHelpModalController;
