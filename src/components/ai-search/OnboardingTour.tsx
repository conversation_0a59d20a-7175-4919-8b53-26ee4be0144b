import React, { useState, useEffect } from "react";
import { useTranslations } from "../../i18n/utils";

// Define the callback props type based on the documentation
interface CallBackProps {
  action: string;
  controlled: boolean;
  index: number;
  lifecycle: string;
  origin: string | null;
  size: number;
  status: string;
  step: any;
  type: string;
}

interface OnboardingTourProps {
  isActive: boolean;
  onClose: () => void;
  lang?: string;
}

interface TourStep {
  target: string;
  content: string;
  title?: string;
  placement?: "top" | "bottom" | "left" | "right" | "center" | "auto";
  disableBeacon?: boolean;
}

const OnboardingTour: React.FC<OnboardingTourProps> = ({
  isActive,
  onClose,
  lang = "en",
}) => {
  const [JoyrideComponent, setJoyrideComponent] = useState<any>(null);
  const [run, setRun] = useState(false);
  const [stepIndex, setStepIndex] = useState(0);
  const t = useTranslations(lang as any);

  // Define tour steps with translations
  const tourSteps: TourStep[] = [
    {
      target: ".ai-search-input",
      content: t("tour.step1.content"),
      title: t("tour.step1.title"),
      placement: "bottom",
      disableBeacon: true,
    },
    {
      target: ".suggestions-wrapper",
      content: t("tour.step2.content"),
      title: t("tour.step2.title"),
      placement: "top",
      disableBeacon: true,
    },
    {
      target: ".search-toggle-wrapper",
      content: t("tour.step3.content"),
      title: t("tour.step3.title"),
      placement: "bottom",
      disableBeacon: true,
    },
    {
      target: "form[class*='flex'][class*='items-center'][class*='bg-white']",
      content: t("tour.step4.content"),
      title: t("tour.step4.title"),
      placement: "bottom",
      disableBeacon: true,
    },
  ];

  // Dynamically load Joyride only when needed
  useEffect(() => {
    if (isActive && !JoyrideComponent) {
      import("react-joyride").then((module) => {
        setJoyrideComponent(() => module.default);
        setRun(true);
        setStepIndex(0);
      });
    } else if (isActive && JoyrideComponent) {
      setRun(true);
      setStepIndex(0);
    } else {
      setRun(false);
    }
  }, [isActive, JoyrideComponent]);

  // Handle Joyride callback
  const handleJoyrideCallback = (data: CallBackProps) => {
    const { action, index, status, type } = data;

    if (status === "finished" || status === "skipped" || action === "close") {
      // Tour finished, skipped, or closed via close button
      setRun(false);
      onClose();
    } else if (type === "step:after" || type === "error:target_not_found") {
      // Update state to advance the tour
      setStepIndex(index + (action === "prev" ? -1 : 1));

      // Special handling: if we're moving to the regular search step,
      // ensure regular search mode is active
      const nextIndex = index + (action === "prev" ? -1 : 1);
      if (
        nextIndex < tourSteps.length &&
        nextIndex >= 0 &&
        tourSteps[nextIndex].target ===
          "form[class*='flex'][class*='items-center'][class*='bg-white']"
      ) {
        // Find and click the regular search toggle
        const regularSearchToggle = document.querySelector(
          ".toggle-option:not(.ai-option)"
        ) as HTMLElement;
        if (regularSearchToggle) {
          regularSearchToggle.click();
          // Small delay to let UI update
          setTimeout(() => {
            setStepIndex(nextIndex);
          }, 300);
          return;
        }
      }
    }
  };

  // Only render Joyride when it's loaded and active
  if (!isActive || !JoyrideComponent) {
    return null;
  }

  return (
    <JoyrideComponent
      steps={tourSteps}
      run={run}
      stepIndex={stepIndex}
      callback={handleJoyrideCallback}
      continuous={true}
      showProgress={true}
      showSkipButton={true}
      styles={{
        options: {
          primaryColor: "#3566ab",
          textColor: "#333",
          backgroundColor: "#fff",
          overlayColor: "rgba(0, 0, 0, 0.7)",
          spotlightShadow: "0 0 15px rgba(0, 0, 0, 0.5)",
          zIndex: 9999,
        },
        tooltip: {
          borderRadius: 12,
          fontSize: 14,
          fontFamily: "'Karla', sans-serif",
          maxWidth: 320,
          minWidth: 280,
        },
        tooltipTitle: {
          fontFamily: "'Baskervville', serif",
          fontSize: 20,
          fontWeight: 600,
          color: "#285DA6",
          marginBottom: 8,
        },
        tooltipContent: {
          lineHeight: 1.5,
          color: "#555",
        },
        buttonNext: {
          backgroundColor: "#285DA6",
          borderRadius: 6,
          fontSize: 14,
          fontWeight: 500,
          padding: "8px 16px",
          minWidth: 60,
        },
        buttonSkip: {
          color: "#666",
          fontSize: 13,
          padding: "4px 8px",
        },
        buttonBack: {
          color: "#666",
          fontSize: 13,
          padding: "4px 8px",
        },
      }}
      locale={{
        back: t("tour.navigation.back"),
        close: t("tour.navigation.close"),
        last: t("tour.navigation.last"),
        next: t("tour.navigation.next"),
        skip: t("tour.navigation.skip"),
      }}
      disableOverlayClose={true}
      disableCloseOnEsc={false}
      disableScrolling={true}
      hideCloseButton={false}
      scrollToFirstStep={false}
      scrollOffset={0}
      spotlightClicks={false}
      spotlightPadding={8}
    />
  );
};

export default OnboardingTour;
