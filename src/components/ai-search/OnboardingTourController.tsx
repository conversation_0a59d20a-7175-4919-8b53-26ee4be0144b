import React, { useState, useEffect } from "react";
import OnboardingTour from "./OnboardingTour";
import { getLangFromUrl } from "../../i18n/utils";

interface OnboardingTourControllerProps {}

const OnboardingTourController: React.FC<
  OnboardingTourControllerProps
> = () => {
  const [isActive, setIsActive] = useState(false);
  const [lang, setLang] = useState("en");

  // Get current language from URL safely on client side
  useEffect(() => {
    if (typeof window !== "undefined") {
      const currentLang = getLangFromUrl(new URL(window.location.href));
      setLang(currentLang);
    }
  }, []);

  useEffect(() => {
    // Add the startOnboardingTour function to the window object
    (window as any).startOnboardingTour = () => {
      setIsActive(true);
    };

    // Cleanup function
    return () => {
      (window as any).startOnboardingTour = undefined;
    };
  }, []);

  const handleClose = () => {
    setIsActive(false);
  };

  return (
    <OnboardingTour isActive={isActive} onClose={handleClose} lang={lang} />
  );
};

export default OnboardingTourController;
