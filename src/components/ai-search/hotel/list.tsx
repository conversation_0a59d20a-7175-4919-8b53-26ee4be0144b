"use client";

import { useState } from "react";
import { ChatHotelCard } from "./card";
import type { Hotel } from "./types";
import { Skeleton } from "../../../components/ui/skeleton";
import { Button } from "../../../components/ui/button";
import { Card, CardContent } from "../../../components/ui/card";
import { AlertCircle } from "lucide-react";
import {
  Alert,
  AlertDescription,
  AlertTitle,
} from "../../../components/ui/alert";
import HotelDetailModal from "../../../components/hotels/HotelDetailModal";
import { getHotelDetailsQuick, getRoomAvailability } from "../../../utils/dataService";
import "../../../styles/hotel-modal.css";

interface ChatHotelListProps {
  onSelectHotel?: (hotel: Hotel) => void;
  onViewHotelDetails?: (hotel: Hotel) => void;
  destinationId?: string;
  hotels?: Hotel[]; // Add hotels array from props
}

export function ChatHotelList({
  onSelectHotel,
  onViewHotelDetails,
  hotels = [], // Use hotels from props with empty array as default
}: ChatHotelListProps) {
  // Modal state
  const [selectedHotel, setSelectedHotel] = useState<Hotel | null>(null);
  const [modalOpen, setModalOpen] = useState<boolean>(false);
  const [hotelDetails, setHotelDetails] = useState<any>(null);
  const [roomTypes, setRoomTypes] = useState<any[]>([]);
  const [detailsLoading, setDetailsLoading] = useState<boolean>(false);

  // We no longer need loading state since we're not fetching data
  const [isLoading] = useState(false);
  const [error] = useState<string | null>(null);

  const handleSelectHotel = (hotel: Hotel) => {
    if (onSelectHotel) {
      onSelectHotel(hotel);
    }
  };

  const handleViewHotelDetails = async (hotel: Hotel) => {
    // Do NOT call the parent component's handler
    // This ensures that clicking "View Details" only opens the modal
    // and doesn't affect the chat in any way

    // Set the selected hotel and open modal
    setSelectedHotel(hotel);
    setModalOpen(true);

    // Fetch hotel details quickly first
    try {
      setDetailsLoading(true);
      const result = await getHotelDetailsQuick(hotel.id);
      setHotelDetails(result.hotel);

      // Set basic room types with loading state
      setRoomTypes(result.roomTypes || []);
      setDetailsLoading(false);

      // Then fetch room availability asynchronously
      try {
        const availabilityResult = await getRoomAvailability(
          hotel.id,
          null, // No specific dates for AI search
          null,
          1, // Default guest counts
          0,
          0
        );

        if (availabilityResult && availabilityResult.availabilityMap) {
          // Update room types with availability data
          const updatedRoomTypes = result.roomTypes.map((room: any) => {
            const availabilityInfo = availabilityResult.availabilityMap.get(room.id);
            if (availabilityInfo) {
              return {
                ...room,
                price: availabilityInfo.price?.per_night_amount || room.price,
                available: availabilityInfo.available || false,
                availableRooms: availabilityInfo.available_rooms || 0,
                priceDetails: availabilityInfo.price || null,
                currencyCode: availabilityInfo.price?.currency_code || "USD",
                isLoading: false,
              };
            }
            return { ...room, isLoading: false };
          });

          // Filter to only include available rooms
          const availableRoomTypes = updatedRoomTypes.filter(
            (room) => room.available !== false
          );
          setRoomTypes(availableRoomTypes);
        }
      } catch (availabilityErr) {
        console.error("Error fetching room availability:", availabilityErr);
        // Remove loading state even on error
        const updatedRoomTypes = result.roomTypes.map((room: any) => ({
          ...room,
          isLoading: false,
        }));
        setRoomTypes(updatedRoomTypes);
      }
    } catch (err) {
      console.error("Error fetching hotel details:", err);
      setDetailsLoading(false);
    }
  };

  // Close the modal
  const handleCloseModal = () => {
    setModalOpen(false);
  };

  if (isLoading) {
    return (
      <div className="w-full">
        <div className="mb-6">
          <Skeleton className="h-8 w-48 mb-2" />
          <Skeleton className="h-4 w-full max-w-3xl" />
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
          {[...Array(6)].map((_, index) => (
            <Card key={index} className="overflow-hidden h-full">
              <div className="flex flex-col h-full">
                {/* Image Skeleton */}
                <div className="aspect-[4/3]">
                  <Skeleton className="h-full w-full" />
                </div>

                {/* Content Skeleton */}
                <CardContent className="flex-1 p-4 space-y-4">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-3/4" />
                    <Skeleton className="h-4 w-1/2" />
                  </div>

                  <div className="flex gap-2">
                    <Skeleton className="h-5 w-20" />
                    <Skeleton className="h-5 w-24" />
                    <Skeleton className="h-5 w-16" />
                  </div>

                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />

                  <div className="space-y-2 mt-4">
                    <Skeleton className="h-9 w-full" />
                    <Skeleton className="h-9 w-full" />
                  </div>
                </CardContent>
              </div>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="w-full">
        <Alert variant="destructive" className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error Loading Hotels</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      </div>
    );
  }

  if (hotels.length === 0) {
    return (
      <div className="w-full">
        <Alert className="mb-4">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>No Hotels Found</AlertTitle>
          <AlertDescription>
            No hotels are currently available. Please check back later or try a
            different search.
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  return (
    <div className="w-full chat-hotel-list-container">
      <div className="mb-4 sm:mb-6">
        <h3
          className="text-lg sm:text-xl font-bold mb-2"
          style={{
            fontFamily:
              '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
          }}
        >
          Luxury Hotels
        </h3>
        <p
          className="text-xs sm:text-sm text-muted-foreground max-w-3xl"
          style={{
            fontFamily:
              '"Funktional Grotesk", "Inter", "Helvetica Neue", Arial, sans-serif',
          }}
        >
          Discover these handpicked premium hotels for your perfect stay. Each
          property offers exceptional amenities and service for a memorable
          experience.
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 sm:gap-6 lg:gap-8">
        {hotels.slice(0, 6).map((hotel) => (
          <ChatHotelCard
            key={hotel.id}
            hotel={hotel}
            onSelect={() => handleSelectHotel(hotel)}
            onViewDetails={() => handleViewHotelDetails(hotel)}
          />
        ))}
      </div>

      {hotels.length > 6 && (
        <div className="mt-8 text-center">
          <p className="text-sm text-muted-foreground">
            Showing 6 of {hotels.length} available hotels
          </p>
          <Button
            variant="outline"
            size="sm"
            className="text-sm mt-2 px-4"
            onClick={() => {
              // This is just for UI purposes - in a real app, you might show more hotels
              // or navigate to a full listing page
            }}
          >
            View All Hotels
          </Button>
        </div>
      )}

      {/* Hotel Detail Modal */}
      {selectedHotel && (
        <HotelDetailModal
          isOpen={modalOpen}
          onClose={handleCloseModal}
          hotelDetails={hotelDetails}
          roomTypes={roomTypes}
          hotelId={selectedHotel.id}
          loading={detailsLoading}
        />
      )}
    </div>
  );
}
