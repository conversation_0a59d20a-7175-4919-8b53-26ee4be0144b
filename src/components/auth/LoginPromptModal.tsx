import React, { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>Header, DialogTitle } from "../ui/dialog";
import { useUser } from "../../contexts/UserContext";

interface LoginPromptModalProps {
  isOpen: boolean;
  onClose: () => void;
  guestEmail: string;
  onLoginSuccess?: () => void;
}

const LoginPromptModal: React.FC<LoginPromptModalProps> = ({
  isOpen,
  onClose,
  guestEmail,
  onLoginSuccess,
}) => {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [otpRequested, setOtpRequested] = useState(false);
  const [otpResendTimer, setOtpResendTimer] = useState(0);
  const { requestLoginOtp, verifyOtp } = useUser();

  // Pre-populate email from guest details
  useEffect(() => {
    if (guestEmail) {
      setEmail(guestEmail);
    }
  }, [guestEmail]);

  // Timer for OTP resend
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (otpResendTimer > 0) {
      timer = setTimeout(() => {
        setOtpResendTimer(otpResendTimer - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [otpResendTimer]);

  // Reset state when modal opens/closes
  useEffect(() => {
    if (!isOpen) {
      setOtp("");
      setErrorMessage("");
      setSuccessMessage("");
      setOtpRequested(false);
      setOtpResendTimer(0);
    }
  }, [isOpen]);

  const handleRequestOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccessMessage("");
    setIsLoading(true);

    try {
      const normalizedEmail = email.toLowerCase().trim();
      await requestLoginOtp(normalizedEmail);
      setOtpRequested(true);
      setSuccessMessage(
        `OTP sent to ${normalizedEmail}. Please check your email.`
      );
      setOtpResendTimer(60); // 60 seconds cooldown
    } catch (error: any) {
      console.error("OTP request error:", error);
      setErrorMessage(error.message || "Failed to send OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccessMessage("");
    setIsLoading(true);

    try {
      const normalizedEmail = email.toLowerCase().trim();
      await verifyOtp(normalizedEmail, otp);
      setSuccessMessage("Login successful!");

      // Wait a moment to show success message, then call callback
      setTimeout(() => {
        if (onLoginSuccess) {
          onLoginSuccess();
        }
        onClose();
      }, 1500);
    } catch (error: any) {
      console.error("OTP verification error:", error);
      setErrorMessage(
        error.message || "Invalid OTP. Please check and try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    if (otpResendTimer > 0) return;

    setErrorMessage("");
    setIsLoading(true);

    try {
      const normalizedEmail = email.toLowerCase().trim();
      await requestLoginOtp(normalizedEmail);
      setSuccessMessage("OTP resent successfully!");
      setOtpResendTimer(60);
    } catch (error: any) {
      console.error("OTP resend error:", error);
      setErrorMessage(error.message || "Failed to resend OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90vw] max-w-[520px] max-h-[90vh] z-[1200] bg-white border-0 shadow-2xl rounded-2xl p-8">
        <DialogHeader className="text-center pb-2">
          <div className="flex items-center justify-center mb-6">
            <div className="w-20 h-20 rounded-full bg-[#285DA6] flex items-center justify-center shadow-lg">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="28"
                height="28"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2.5"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-white"
              >
                <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                <polyline points="10 17 15 12 10 7"></polyline>
                <line x1="15" y1="12" x2="3" y2="12"></line>
              </svg>
            </div>
          </div>
          <DialogTitle className="text-2xl text-center font-bold text-gray-900 mb-4 tracking-wide">
            SIGN IN
          </DialogTitle>
          <p className="text-gray-600 text-base leading-relaxed max-w-md text-center ">
            {guestEmail
              ? "Sign in to your account to auto-fill your details and continue with your booking."
              : "Sign in to your account to auto-fill your details and continue with your booking."
            }
          </p>
        </DialogHeader>

        <div className="m-0">
          {/* Error Message */}
          {errorMessage && (
            <div className="bg-red-50 border border-red-100 text-red-700 p-4 rounded-lg mb-4 text-sm">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-red-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                    clipRule="evenodd"
                  />
                </svg>
                {errorMessage}
              </div>
            </div>
          )}

          {/* Success Message */}
          {successMessage && (
            <div className="bg-green-50 border border-green-100 text-green-700 p-4 rounded-lg mb-4 text-sm">
              <div className="flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 mr-2 text-green-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                    clipRule="evenodd"
                  />
                </svg>
                {successMessage}
              </div>
            </div>
          )}

          {!otpRequested ? (
            // Step 1: Request OTP form
            <form onSubmit={handleRequestOtp} className="space-y-8">
              <div>
                <label
                  htmlFor="email"
                  className="block text-sm font-medium text-gray-700 mb-3 uppercase tracking-wider"
                >
                  EMAIL ADDRESS
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                  </div>
                  <input
                    id="email"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    className="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6] transition-all duration-200 bg-white text-gray-900 placeholder-gray-400 text-base"
                    placeholder="Enter your email address"
                    required
                    disabled={!!guestEmail} // Disable if email is pre-filled from guest details
                  />
                </div>
              </div>

              <button
                type="submit"
                disabled={isLoading || !email.trim()}
                className="w-full bg-[#285DA6] hover:bg-[#6B9BD3] text-white py-4 px-6 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-[#285DA6]/50 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-base"
              >
                {isLoading ? (
                  <>
                    <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                    Sending OTP...
                  </>
                ) : (
                  <>
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-2"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                      <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                    </svg>
                    Send Login Code
                  </>
                )}
              </button>
            </form>
          ) : (
            // Step 2: Verify OTP form
            <form onSubmit={handleVerifyOtp} className="space-y-8">
              <div>
                <label
                  htmlFor="otp"
                  className="block text-sm font-medium text-gray-700 mb-3 uppercase tracking-wider"
                >
                  VERIFICATION CODE
                </label>
                <div className="relative">
                  <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-gray-400"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M18 8a6 6 0 01-7.743 5.743L10 14l-1 1-1 1H6v2H2v-4l4.257-4.257A6 6 0 1118 8zm-6-4a1 1 0 100 2 2 2 0 012 2 1 1 0 102 0 4 4 0 00-4-4z"
                        clipRule="evenodd"
                      />
                    </svg>
                  </div>
                  <input
                    id="otp"
                    type="text"
                    value={otp}
                    onChange={(e) => setOtp(e.target.value.replace(/\D/g, ""))}
                    className="w-full pl-12 pr-4 py-4 border border-gray-200 rounded-lg focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6] transition-all duration-200 bg-white text-gray-900 placeholder-gray-400 text-center text-lg tracking-widest"
                    placeholder="Enter 6-digit code"
                    maxLength={6}
                    required
                  />
                </div>
                <p className="text-sm text-gray-500 mt-2 text-center">
                  Enter the 6-digit code sent to {email}
                </p>
              </div>

              <div className="flex flex-col space-y-3">
                <button
                  type="submit"
                  disabled={isLoading || otp.length !== 6}
                  className="w-full bg-[#285DA6] hover:bg-[#6B9BD3] text-white py-4 px-6 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-[#285DA6]/50 focus:ring-offset-2 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center text-base"
                >
                  {isLoading ? (
                    <>
                      <div className="w-5 h-5 border-2 border-white/30 border-t-white rounded-full animate-spin mr-2"></div>
                      Verifying...
                    </>
                  ) : (
                    <>
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5 mr-2"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                          clipRule="evenodd"
                        />
                      </svg>
                      Verify & Sign In
                    </>
                  )}
                </button>

                <button
                  type="button"
                  onClick={handleResendOtp}
                  disabled={otpResendTimer > 0 || isLoading}
                  className="w-full text-[#285DA6] py-2 px-4 rounded-lg font-medium hover:bg-[#285DA6]/5 focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 transition-all duration-200 disabled:opacity-50 disabled:cursor-not-allowed text-sm"
                >
                  {otpResendTimer > 0
                    ? `Resend code in ${otpResendTimer}s`
                    : "Resend verification code"}
                </button>
              </div>
            </form>
          )}

          <div className="mt-6">
            <div className="flex items-center justify-center">
              <span className="text-sm text-gray-500 flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-4 w-4 mr-2 text-green-500"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clipRule="evenodd"
                  />
                </svg>
                Secure login • Your booking details are safe
              </span>
            </div>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default LoginPromptModal;
