import React, { useState, useEffect } from "react";
import { useUser } from "../../contexts/UserContext";

const OtpLoginForm: React.FC = () => {
  const [email, setEmail] = useState("");
  const [otp, setOtp] = useState("");
  const [errorMessage, setErrorMessage] = useState("");
  const [successMessage, setSuccessMessage] = useState("");
  const [isLoading, setIsLoading] = useState(false);
  const [otpRequested, setOtpRequested] = useState(false);
  const [otpResendTimer, setOtpResendTimer] = useState(0);
  const { requestLoginOtp, verifyOtp } = useUser();

  // Timer for OTP resend
  useEffect(() => {
    let timer: NodeJS.Timeout;
    if (otpResendTimer > 0) {
      timer = setTimeout(() => {
        setOtpResendTimer(otpResendTimer - 1);
      }, 1000);
    }
    return () => {
      if (timer) clearTimeout(timer);
    };
  }, [otpResendTimer]);

  const handleRequestOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccessMessage("");
    setIsLoading(true);

    try {
      const normalizedEmail = email.toLowerCase().trim();
      await requestLoginOtp(normalizedEmail);
      setOtpRequested(true);
      setSuccessMessage(
        `OTP sent to ${normalizedEmail}. Please check your email.`
      );
      setOtpResendTimer(60); // 60 seconds cooldown
    } catch (error: any) {
      console.error("OTP request error:", error);
      setErrorMessage(error.message || "Failed to send OTP. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  const handleVerifyOtp = async (e: React.FormEvent) => {
    e.preventDefault();
    setErrorMessage("");
    setSuccessMessage("");
    setIsLoading(true);

    try {
      const normalizedEmail = email.toLowerCase().trim();
      await verifyOtp(normalizedEmail, otp);
      setSuccessMessage("Login successful!");

      // Redirect to home page after successful login
      setTimeout(() => {
        window.location.href = "/";
      }, 1000);
    } catch (error: any) {
      console.error("OTP verification error:", error);
      setErrorMessage(
        error.message || "Invalid OTP. Please check and try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  const handleResendOtp = async () => {
    setErrorMessage("");
    setSuccessMessage("");
    setIsLoading(true);

    try {
      const normalizedEmail = email.toLowerCase().trim();
      await requestLoginOtp(normalizedEmail);
      setSuccessMessage(
        `OTP resent to ${normalizedEmail}. Please check your email.`
      );
      setOtpResendTimer(60); // 60 seconds cooldown
    } catch (error: any) {
      console.error("OTP resend error:", error);
      setErrorMessage(
        error.message || "Failed to resend OTP. Please try again."
      );
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="bg-white rounded-lg">
      {successMessage && (
        <div className="bg-green-50 border border-green-100 text-green-700 p-4 rounded-lg mb-6 text-sm animate-fade-in">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-green-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                clipRule="evenodd"
              />
            </svg>
            {successMessage}
          </div>
        </div>
      )}

      {errorMessage && (
        <div className="bg-red-50 border border-red-100 text-red-700 p-4 rounded-lg mb-6 text-sm animate-fade-in">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            {errorMessage}
          </div>
        </div>
      )}

      {!otpRequested ? (
        // Step 1: Request OTP form
        <form onSubmit={handleRequestOtp} className="space-y-6">
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-karla font-medium text-gray-700 mb-2 uppercase tracking-wider"
            >
              Email Address
            </label>
            <div className="relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-gray-400 group-focus-within:text-[#285DA6] transition-colors"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path d="M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z" />
                  <path d="M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z" />
                </svg>
              </div>
              <input
                id="email"
                type="email"
                value={email}
                onChange={(e) => setEmail(e.target.value)}
                className="w-full pl-12 pr-4 py-3.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6]/30 focus:border-[#285DA6] transition-all"
                placeholder="<EMAIL>"
                required
              />
            </div>
          </div>

          <div className="pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#285DA6] text-white py-3.5 px-4 rounded-lg hover:bg-[#285DA6]/90 transition-all transform hover:translate-y-[-2px] font-karla font-medium text-sm uppercase tracking-wider disabled:opacity-70 shadow-md hover:shadow-lg"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Sending OTP...
                </span>
              ) : (
                "Request OTP"
              )}
            </button>
          </div>
        </form>
      ) : (
        // Step 2: Verify OTP form
        <form onSubmit={handleVerifyOtp} className="space-y-6">
          <div>
            <div className="flex justify-between items-center mb-2">
              <label
                htmlFor="otp"
                className="block text-sm font-karla font-medium text-gray-700 uppercase tracking-wider"
              >
                Enter OTP
              </label>
              <button
                type="button"
                onClick={handleResendOtp}
                disabled={otpResendTimer > 0 || isLoading}
                className="text-xs text-[#285DA6] hover:text-[#285DA6]/80 transition-colors font-medium disabled:text-gray-400"
              >
                {otpResendTimer > 0
                  ? `Resend OTP in ${otpResendTimer}s`
                  : "Resend OTP"}
              </button>
            </div>
            <div className="relative group">
              <div className="absolute inset-y-0 left-0 pl-4 flex items-center pointer-events-none">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className="h-5 w-5 text-gray-400 group-focus-within:text-[#285DA6] transition-colors"
                  viewBox="0 0 20 20"
                  fill="currentColor"
                >
                  <path
                    fillRule="evenodd"
                    d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
              <input
                id="otp"
                type="tel"
                inputMode="numeric"
                value={otp}
                onChange={(e) => {
                  // Only allow numeric characters
                  const numericValue = e.target.value.replace(/[^0-9]/g, "");
                  setOtp(numericValue);
                }}
                className="w-full pl-12 pr-4 py-3.5 bg-gray-50 border border-gray-200 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6]/30 focus:border-[#285DA6] transition-all"
                placeholder="Enter 6-digit OTP"
                required
                maxLength={6}
                pattern="[0-9]{6}"
              />
            </div>
            <p className="text-xs text-gray-500 mt-1">
              We've sent a 6-digit OTP to your email address.
            </p>
          </div>

          <div className="pt-4">
            <button
              type="submit"
              disabled={isLoading}
              className="w-full bg-[#285DA6] text-white py-3.5 px-4 rounded-lg hover:bg-[#285DA6]/90 transition-all transform hover:translate-y-[-2px] font-karla font-medium text-sm uppercase tracking-wider disabled:opacity-70 shadow-md hover:shadow-lg"
            >
              {isLoading ? (
                <span className="flex items-center justify-center">
                  <svg
                    className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                  >
                    <circle
                      className="opacity-25"
                      cx="12"
                      cy="12"
                      r="10"
                      stroke="currentColor"
                      strokeWidth="4"
                    ></circle>
                    <path
                      className="opacity-75"
                      fill="currentColor"
                      d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                    ></path>
                  </svg>
                  Verifying...
                </span>
              ) : (
                "Verify & Sign In"
              )}
            </button>
          </div>
        </form>
      )}

      <div className="mt-10 pt-6 border-t border-gray-100 text-center">
        <p className="text-sm text-gray-600">
          Don't have an account?{" "}
          <a
            href="/register"
            className="text-[#285DA6] font-medium hover:text-[#285DA6]/80 transition-colors"
          >
            Create Account
          </a>
        </p>
      </div>

      <div className="mt-6 flex items-center justify-center">
        <span className="text-xs text-gray-500 flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            className="h-4 w-4 mr-1 text-green-500"
            viewBox="0 0 20 20"
            fill="currentColor"
          >
            <path
              fillRule="evenodd"
              d="M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z"
              clipRule="evenodd"
            />
          </svg>
          Secure login
        </span>
      </div>
    </div>
  );
};

export default OtpLoginForm;
