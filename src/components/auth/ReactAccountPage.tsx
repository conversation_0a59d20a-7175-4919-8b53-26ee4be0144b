import { useEffect, useState, useRef } from "react";
import { useUser } from "../../contexts/UserContext";
import {
  fetchCustomerBookings,
  type BookingQueryParams,
  type BookingApiResponse,
} from "../../utils/store/bookings";
import AccountLayout from "../account/AccountLayout";
import AccountDashboard from "../account/AccountDashboard";
import MyTripsTable from "../account/MyTripsTable";
import AccountWishlistWrapper from "../account/AccountWishlistWrapper";
import BookingDetailsModal from "../booking/BookingDetailsModal";
import { WishlistProvider } from "../wishlist/WishlistContext";
import { dialCodes } from "../../constants";
import type {
  Booking,
  UserData,
  UserContext,
  PaginationState,
} from "../../types/booking";

const ReactAccountPage: React.FC = () => {
  const [error, setError] = useState<string | null>(null);
  const [userData, setUserData] = useState<UserData | null>(null);
  const [isAuthenticated, setIsAuthenticated] = useState<boolean>(false);
  const [loading, setLoading] = useState<boolean>(true);
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [bookingsLoading, setBookingsLoading] = useState<boolean>(true);
  const [upcomingTripsCount, setUpcomingTripsCount] = useState(0);
  const [ongoingTripsCount, setOngoingTripsCount] = useState(0);
  const [completedTripsCount, setCompletedTripsCount] = useState(0);
  const [totalBookingsCount, setTotalBookingsCount] = useState(0);
  const [showEditProfile, setShowEditProfile] = useState<boolean>(false);
  const [selectedBooking, setSelectedBooking] = useState<Booking | null>(null);
  const [showBookingModal, setShowBookingModal] = useState<boolean>(false);
  const [activeSection, setActiveSection] = useState<string>("dashboard");

  const [profileUpdateLoading, setProfileUpdateLoading] =
    useState<boolean>(false);
  const [profileUpdateError, setProfileUpdateError] = useState<string | null>(
    null
  );
  const [profileUpdateSuccess, setProfileUpdateSuccess] =
    useState<boolean>(false);

  // Dial code selector state
  const [selectedDialCode, setSelectedDialCode] = useState("+41");
  const [isDialCodeDropdownOpen, setIsDialCodeDropdownOpen] = useState(false);
  const [dialCodeSearch, setDialCodeSearch] = useState("");
  const dialCodeDropdownRef = useRef<HTMLDivElement>(null);

  // Pagination state
  const [pagination, setPagination] = useState<PaginationState>({
    currentPage: 1,
    totalPages: 1,
    itemsPerPage: 6,
  });

  // Sorting state for trips table
  const [sortBy, setSortBy] = useState<
    "date" | "amount" | "status" | "created_at"
  >("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");

  // Tab state for filtering trips
  const [activeTab, setActiveTab] = useState<
    "ongoing" | "upcoming" | "completed" | "cancelled"
  >("ongoing");

  // Get user context outside of useEffect
  let userContext: UserContext | null = null;
  try {
    userContext = useUser();
  } catch (error) {
    // Context not available, will use token-based auth
  }

  // Initialize dial code from user data
  useEffect(() => {
    if (userData) {
      // First try to use stored country_code, then parse from phone number
      if (userData.country_code) {
        setSelectedDialCode(userData.country_code);
      } else if (userData.phone) {
        const { countryCode } = parsePhoneNumber(userData.phone);
        setSelectedDialCode(countryCode);
      }
    }
  }, [userData]);

  // Close dial code dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dialCodeDropdownRef.current &&
        !dialCodeDropdownRef.current.contains(event.target as Node)
      ) {
        setIsDialCodeDropdownOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Authentication setup
  useEffect(() => {
    const initAuth = async () => {
      try {
        // First try to use UserContext if available
        if (userContext && userContext.user) {
          setUserData(userContext.user);
          setIsAuthenticated(true);
          setLoading(false);
          return;
        }

        // Fallback to token-based auth
        const token = localStorage.getItem("auth_token");
        if (token) {
          await fetchUserData(token);
        } else {
          redirectToLogin();
        }
      } catch (error) {
        setError("Authentication failed");
        setLoading(false);
      }
    };

    initAuth();
  }, []);

  // Load bookings data with server-side pagination
  useEffect(() => {
    const fetchBookings = async () => {
      try {
        setBookingsLoading(true);

        // Build query parameters for enhanced pagination using limit and offset
        const queryParams: BookingQueryParams = {
          limit: pagination.itemsPerPage,
          offset: (pagination.currentPage - 1) * pagination.itemsPerPage,
          // Add sorting if needed
          sort_by:
            sortBy === "date"
              ? "check_in_date"
              : sortBy === "amount"
              ? "total_amount"
              : sortBy === "status"
              ? "status"
              : sortBy === "created_at"
              ? "created_at"
              : "created_at",
          sort_order: sortOrder,
          // Add booking status filter based on active tab
          booking_status:
            activeTab === "cancelled"
              ? undefined
              : activeTab === "ongoing"
              ? "active"
              : activeTab, // Map ongoing to active for API
        };

        console.log({ queryParams });
        // Fetch bookings with pagination from API
        const result: BookingApiResponse = await fetchCustomerBookings(
          queryParams
        );

        // Calculate total pages for server-side pagination
        const totalPages = Math.ceil(result.count / pagination.itemsPerPage);
        setPagination((prev) => ({
          ...prev,
          totalPages: totalPages || 1, // Ensure at least 1 page
        }));

        // Set bookings data (already paginated from server)
        if (Array.isArray(result.bookings)) {
          console.log({ result });
          setBookings(result.bookings);
          setUpcomingTripsCount(result.upcoming_trips_count || 0);
          setOngoingTripsCount(result.active_trips_count || 0);
          setCompletedTripsCount(result.completed_trips_count || 0);
          setTotalBookingsCount(
            result.total_bookings_count || result.count || 0
          ); // Use total_bookings_count from API
        } else {
          console.error("Unexpected bookings data format:", result);
          setBookings([]);
          setUpcomingTripsCount(0);
          setOngoingTripsCount(0);
          setCompletedTripsCount(0);
          setTotalBookingsCount(0);
        }
      } catch (error) {
        console.error("Error fetching bookings:", error);
        // Fallback to empty array if API fails
        setBookings([]);
        setUpcomingTripsCount(0);
        setOngoingTripsCount(0);
        setCompletedTripsCount(0);
        setTotalBookingsCount(0);
        setPagination((prev) => ({
          ...prev,
          totalPages: 1,
        }));
      } finally {
        setBookingsLoading(false);
      }
    };

    if (isAuthenticated) {
      // Check if we have an auth token before trying to fetch bookings
      const authToken = localStorage.getItem("auth_token");
      if (authToken) {
        fetchBookings();
      } else {
        console.warn("No auth token found, skipping bookings fetch");
        setBookingsLoading(false);
      }
    }
  }, [
    isAuthenticated,
    pagination.itemsPerPage,
    pagination.currentPage,
    sortBy,
    sortOrder,
    activeTab,
  ]);

  // Handle page change
  const handlePageChange = (newPage: number) => {
    if (newPage >= 1 && newPage <= pagination.totalPages) {
      setPagination((prev) => ({
        ...prev,
        currentPage: newPage,
      }));
    }
  };

  // Helper function to determine trip status
  const getTripStatus = (checkOutDate: string): "Upcoming" | "Completed" => {
    const today = new Date();
    const checkOut = new Date(checkOutDate);

    // Set time to start of day for accurate comparison
    today.setHours(0, 0, 0, 0);
    checkOut.setHours(0, 0, 0, 0);

    return checkOut >= today ? "Upcoming" : "Completed";
  };

  // Handle sorting change
  const handleSortChange = (
    newSortBy: "date" | "amount" | "status" | "created_at",
    newSortOrder: "asc" | "desc"
  ) => {
    setSortBy(newSortBy);
    setSortOrder(newSortOrder);
    // Reset to first page when sorting changes
    setPagination((prev) => ({
      ...prev,
      currentPage: 1,
    }));
  };

  // Handle tab change
  const handleTabChange = (
    tab: "ongoing" | "upcoming" | "completed" | "cancelled"
  ) => {
    setActiveTab(tab);
    // Reset to first page when tab changes
    setPagination((prev) => ({
      ...prev,
      currentPage: 1,
    }));
  };

  // Fetch user data from API
  const fetchUserData = async (token: string) => {
    try {
      setLoading(true);
      const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";
      const response = await fetch(
        `${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`,
        {
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": apiKey,
          },
          credentials: "include",
        }
      );

      if (!response.ok) {
        throw new Error("Failed to fetch user data");
      }

      const data = await response.json();
      if (data.customer) {
        setUserData(data.customer);
        setIsAuthenticated(true);
      } else {
        redirectToLogin();
      }
    } catch (error) {
      setError("Failed to load account information");
    } finally {
      setLoading(false);
    }
  };

  // Handle logout
  const handleLogout = async () => {
    try {
      if (userContext && userContext.logout) {
        await userContext.logout();
      } else {
        // Fallback to manual token removal if context not available
        localStorage.removeItem("auth_token");
      }
      window.location.href = "/";
    } catch (error) {
      console.error("Logout error:", error);
      // Still redirect even if there's an error
      window.location.href = "/";
    }
  };

  // Redirect to login page
  const redirectToLogin = () => {
    setError("Please log in to access your account");
    setLoading(false);
    setTimeout(() => {
      window.location.href = "/login";
    }, 1500);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[70vh]">
        <div className="text-center">
          <div className="inline-block w-12 h-12 border-4 border-[#285DA6]/30 border-t-[#285DA6] rounded-full animate-spin mb-4"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="bg-red-50 border border-red-100 text-red-700 p-4 rounded-lg max-w-md">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            {error}
          </div>
        </div>
      </div>
    );
  }

  // Not authenticated state
  if (!isAuthenticated || !userData) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="text-center">
          <p className="text-gray-600">Redirecting to login page...</p>
        </div>
      </div>
    );
  }

  // Filter dial codes based on search
  const filteredDialCodes = dialCodes.filter(
    (country) =>
      country.country.toLowerCase().includes(dialCodeSearch.toLowerCase()) ||
      country.code.includes(dialCodeSearch)
  );

  // Handle dial code selection
  const handleDialCodeSelect = (code: string) => {
    setSelectedDialCode(code);
    setIsDialCodeDropdownOpen(false);
    setDialCodeSearch("");
  };

  // Helper function to parse phone number and extract country code
  const parsePhoneNumber = (fullPhone: string) => {
    if (!fullPhone) return { countryCode: "+41", phoneNumber: "" };

    // Find the first space or the first non-digit after the + sign
    const match = fullPhone.match(/^(\+\d+)\s*(.*)$/);
    if (match) {
      return {
        countryCode: match[1],
        phoneNumber: match[2].trim(),
      };
    }

    // If no country code found, assume it's just the phone number
    return {
      countryCode: "+41", // Default
      phoneNumber: fullPhone,
    };
  };

  // Helper function to get display phone number
  const getDisplayPhoneNumber = () => {
    if (!userData?.phone) return "Not provided";

    const { countryCode, phoneNumber } = parsePhoneNumber(userData.phone);
    if (phoneNumber) {
      return `${countryCode} ${phoneNumber}`;
    }
    return userData.phone;
  };

  // Helper function to get phone number without country code for form
  const getPhoneNumberForForm = () => {
    if (!userData?.phone) return "";

    const { phoneNumber } = parsePhoneNumber(userData.phone);
    return phoneNumber;
  };

  // Handle profile update
  const handleProfileUpdate = async (e: React.FormEvent) => {
    e.preventDefault();
    setProfileUpdateLoading(true);
    setProfileUpdateError(null);
    setProfileUpdateSuccess(false);

    try {
      const formData = new FormData(e.target as HTMLFormElement);
      const phoneNumber = formData.get("phone") as string;

      // Combine country code with phone number
      const fullPhoneNumber = phoneNumber
        ? `${selectedDialCode} ${phoneNumber}`
        : "";

      const updateData = {
        first_name: formData.get("first_name") as string,
        last_name: formData.get("last_name") as string,
        phone: fullPhoneNumber,
      };

      // Get auth token and API key
      const token = localStorage.getItem("auth_token");
      const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";

      if (!token) {
        throw new Error("Authentication token not found");
      }

      const response = await fetch(
        `${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
            Authorization: `Bearer ${token}`,
            "x-publishable-api-key": apiKey,
          },
          credentials: "include",
          body: JSON.stringify(updateData),
        }
      );

      if (!response.ok) {
        const errorData = await response.json().catch(() => ({}));
        throw new Error(errorData.message || "Failed to update profile");
      }

      const data = await response.json();

      // Update local user data with the response
      if (data.customer) {
        setUserData(data.customer);
        setProfileUpdateSuccess(true);
        setShowEditProfile(false);

        // Clear success message after 3 seconds
        setTimeout(() => {
          setProfileUpdateSuccess(false);
        }, 3000);
      }
    } catch (error: any) {
      console.error("Profile update error:", error);
      setProfileUpdateError(error.message || "Failed to update profile");
    } finally {
      setProfileUpdateLoading(false);
    }
  };

  // Handle opening booking details modal
  const openBookingModal = (booking: Booking) => {
    setSelectedBooking(booking);
    setShowBookingModal(true);
  };

  // Handle closing booking details modal
  const closeBookingModal = () => {
    setShowBookingModal(false);
    setSelectedBooking(null);
  };

  // Edit Profile Form Component
  const EditProfileForm = () => (
    <form onSubmit={handleProfileUpdate} className="space-y-4">
      {/* Error Message */}
      {profileUpdateError && (
        <div className="p-4 bg-red-50 border border-red-200 rounded-xl">
          <div className="flex items-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-5 w-5 mr-2 text-red-500"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z"
                clipRule="evenodd"
              />
            </svg>
            <span className="text-red-700 text-sm">{profileUpdateError}</span>
          </div>
        </div>
      )}

      <div>
        <label
          htmlFor="first_name"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          First Name
        </label>
        <input
          type="text"
          id="first_name"
          name="first_name"
          defaultValue={userData?.first_name || ""}
          disabled={profileUpdateLoading}
          className="w-full px-4 py-2 border border-gray-200 rounded-xl focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
          required
        />
      </div>

      <div>
        <label
          htmlFor="last_name"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Last Name
        </label>
        <input
          type="text"
          id="last_name"
          name="last_name"
          defaultValue={userData?.last_name || ""}
          disabled={profileUpdateLoading}
          className="w-full px-4 py-2 border border-gray-200 rounded-xl focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
          required
        />
      </div>

      <div>
        <label
          htmlFor="email"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Email Address
        </label>
        <input
          type="email"
          id="email"
          name="email"
          defaultValue={userData?.email || ""}
          className="w-full px-4 py-2 border border-gray-200 rounded-xl focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors bg-gray-50"
          disabled
        />
        <p className="text-xs text-gray-500 mt-1">Email cannot be changed</p>
      </div>

      <div>
        <label
          htmlFor="phone"
          className="block text-sm font-medium text-gray-700 mb-1"
        >
          Phone Number
        </label>
        <div className="flex">
          <div className="relative w-20" ref={dialCodeDropdownRef}>
            <button
              type="button"
              onClick={() => setIsDialCodeDropdownOpen(!isDialCodeDropdownOpen)}
              disabled={profileUpdateLoading}
              className="w-full h-full border border-gray-200 rounded-l-xl px-2 py-2 focus:outline-none focus:ring-[#285DA6] focus:border-[#285DA6] bg-gray-50 text-sm flex items-center justify-between disabled:opacity-50 disabled:cursor-not-allowed"
            >
              <span className="truncate">{selectedDialCode}</span>
              <svg
                className={`w-4 h-4 transition-transform ${
                  isDialCodeDropdownOpen ? "rotate-180" : ""
                }`}
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M19 9l-7 7-7-7"
                />
              </svg>
            </button>

            {isDialCodeDropdownOpen && (
              <div className="absolute top-full left-0 w-80 z-50 bg-white border border-gray-200 rounded-xl shadow-lg max-h-60 overflow-hidden">
                <div className="p-2 border-b border-gray-200">
                  <input
                    type="text"
                    placeholder="Search country or code..."
                    value={dialCodeSearch}
                    onChange={(e) => setDialCodeSearch(e.target.value)}
                    className="w-full px-2 py-1 text-sm border border-gray-200 rounded focus:outline-none focus:ring-[#285DA6] focus:border-[#285DA6]"
                    autoFocus
                  />
                </div>
                <div className="max-h-48 overflow-y-auto">
                  {filteredDialCodes.length > 0 ? (
                    filteredDialCodes.map((country, index) => (
                      <button
                        key={`${country.code}-${index}`}
                        type="button"
                        onClick={() => handleDialCodeSelect(country.code)}
                        className={`w-full text-left px-3 py-2 text-sm hover:bg-gray-100 flex items-center justify-between ${
                          selectedDialCode === country.code
                            ? "bg-blue-50 text-[#285DA6]"
                            : ""
                        }`}
                      >
                        <span className="truncate">{country.country}</span>
                        <span className="text-gray-500 ml-2 flex-shrink-0">
                          {country.code}
                        </span>
                      </button>
                    ))
                  ) : (
                    <div className="px-3 py-2 text-sm text-gray-500">
                      No countries found
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
          <input
            type="tel"
            id="phone"
            name="phone"
            defaultValue={getPhoneNumberForForm()}
            disabled={profileUpdateLoading}
            placeholder="Phone Number"
            className="flex-1 border border-gray-200 border-l-0 rounded-r-xl px-3 py-2 focus:outline-none focus:ring-[#285DA6] focus:border-[#285DA6] transition-colors disabled:bg-gray-50 disabled:cursor-not-allowed"
          />
        </div>
      </div>

      <div className="flex justify-end space-x-3 pt-4">
        <button
          type="button"
          onClick={() => {
            setShowEditProfile(false);
            setProfileUpdateError(null);
            setIsDialCodeDropdownOpen(false);
            setDialCodeSearch("");
          }}
          disabled={profileUpdateLoading}
          className="px-4 py-2 border border-gray-300 rounded-md text-gray-700 hover:bg-gray-50 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
        >
          Cancel
        </button>
        <button
          type="submit"
          disabled={profileUpdateLoading}
          className="px-4 py-2 bg-[#285DA6] text-white rounded-md hover:bg-[#1A3A6E] transition-colors disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
        >
          {profileUpdateLoading ? (
            <>
              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-current mr-2"></div>
              Saving...
            </>
          ) : (
            "Save Changes"
          )}
        </button>
      </div>
    </form>
  );

  // Helper function to render content based on the active section
  const renderContent = () => {
    switch (activeSection) {
      case "dashboard":
        return (
          <AccountDashboard
            userData={userData!}
            bookingsCount={totalBookingsCount}
            upcomingTrips={upcomingTripsCount}
            ongoingTrips={ongoingTripsCount}
            completedTrips={completedTripsCount}
            onEditProfile={() => {
              setShowEditProfile(true);
              setProfileUpdateError(null);
              setProfileUpdateSuccess(false);
              setIsDialCodeDropdownOpen(false);
              setDialCodeSearch("");
            }}
            onViewTrips={() => setActiveSection("trips")}
            showEditProfile={showEditProfile}
            profileUpdateSuccess={profileUpdateSuccess}
            profileUpdateError={profileUpdateError}
            editProfileForm={<EditProfileForm />}
          />
        );
      case "profile":
        return (
          <div className="bg-white rounded-2xl p-6 border border-gray-100 shadow-sm">
            <div className="flex items-center justify-between mb-6">
              <h2 className="text-lg font-semibold text-gray-900">
                Account Information
              </h2>
              {!showEditProfile && (
                <button
                  onClick={() => {
                    setShowEditProfile(true);
                    setProfileUpdateError(null);
                    setProfileUpdateSuccess(false);
                    setIsDialCodeDropdownOpen(false);
                    setDialCodeSearch("");
                  }}
                  className="px-4 py-2 bg-[#285DA6] text-white rounded-xl hover:bg-[#1A3A6E] hover:shadow-md transition-all duration-200 font-medium text-sm"
                >
                  Edit Profile
                </button>
              )}
            </div>
            {/* Success Message */}
            {profileUpdateSuccess && (
              <div className="mb-6 p-4 bg-green-50 border border-green-200 rounded-xl">
                <div className="flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    className="h-5 w-5 mr-2 text-green-500"
                    viewBox="0 0 20 20"
                    fill="currentColor"
                  >
                    <path
                      fillRule="evenodd"
                      d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z"
                      clipRule="evenodd"
                    />
                  </svg>
                  <span className="text-green-700 text-sm">
                    Profile updated successfully!
                  </span>
                </div>
              </div>
            )}
            {showEditProfile ? (
              <EditProfileForm />
            ) : (
              <div className="space-y-4">
                <div className="flex flex-col md:flex-row md:items-center py-3 border-b border-gray-100 last:border-b-0">
                  <div className="md:w-1/3">
                    <label className="text-sm font-medium text-gray-500">
                      First Name
                    </label>
                  </div>
                  <div className="md:w-2/3">
                    <p className="text-gray-900 font-medium">
                      {userData?.first_name || "Not provided"}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row md:items-center py-3 border-b border-gray-100 last:border-b-0">
                  <div className="md:w-1/3">
                    <label className="text-sm font-medium text-gray-500">
                      Last Name
                    </label>
                  </div>
                  <div className="md:w-2/3">
                    <p className="text-gray-900 font-medium">
                      {userData?.last_name || "Not provided"}
                    </p>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row md:items-center py-3 border-b border-gray-100 last:border-b-0">
                  <div className="md:w-1/3">
                    <label className="text-sm font-medium text-gray-500">
                      Email Address
                    </label>
                  </div>
                  <div className="md:w-2/3">
                    <p className="text-gray-900 font-medium">{userData?.email}</p>
                  </div>
                </div>
                <div className="flex flex-col md:flex-row md:items-center py-3">
                  <div className="md:w-1/3">
                    <label className="text-sm font-medium text-gray-500">
                      Phone Number
                    </label>
                  </div>
                  <div className="md:w-2/3">
                    <p className="text-gray-900 font-medium">
                      {userData?.phone || "Not provided"}
                    </p>
                  </div>
                </div>
              </div>
            )}
          </div>
        );
      case "trips":
        // Bookings are already sorted and paginated from the server
        return (
          <MyTripsTable
            bookings={bookings}
            loading={bookingsLoading}
            onBookingClick={openBookingModal}
            pagination={pagination}
            onPageChange={handlePageChange}
            sortBy={sortBy}
            sortOrder={sortOrder}
            onSortChange={handleSortChange}
            activeTab={activeTab}
            onTabChange={handleTabChange}
            upcomingTripsCount={upcomingTripsCount}
            ongoingTripsCount={ongoingTripsCount}
            completedTripsCount={completedTripsCount}
            cancelledTripsCount={0} // We don't have cancelled count from API yet
          />
        );
      case "wishlist":
        return <AccountWishlistWrapper />;

      default:
        return null;
    }
  };

  return (
    <WishlistProvider>
      <AccountLayout
        activeSection={activeSection}
        onSectionChange={setActiveSection}
        userName={userData?.first_name}
        userEmail={userData?.email}
        onLogout={handleLogout}
      >
        {/* Booking Details Modal */}
        <BookingDetailsModal
          booking={selectedBooking}
          isOpen={showBookingModal}
          onClose={closeBookingModal}
        />

        {/* Main Content */}
        <div className="section-transition h-[85vh]">{renderContent()}</div>
      </AccountLayout>
    </WishlistProvider>
  );
};

export default ReactAccountPage;
