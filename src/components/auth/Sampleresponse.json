{"id": "order_01JXPF1QKPP3RBRXTE4C2KH3MN", "order_id": "order_01JXPF1QKPP3RBRXTE4C2KH3MN", "customer_id": "cus_01JT671MY0CT383WYFQT8PEZKG", "guest_email": "<EMAIL>", "guest_name": "<PERSON>esh <PERSON>", "guest_phone": "**********", "hotel_id": "01JWDSWC6RGPD00W1XRW9ER415", "room_config_id": "prod_01JWDT5BNBDPZ7MMNHAGK95TWT", "room_type": "Standard", "check_in_date": "2025-06-15T00:00:00.000Z", "check_out_date": "2025-06-17T00:00:00.000Z", "check_in_time": "14:00", "check_out_time": "11:00", "number_of_guests": 5, "currency_code": "usd", "status": "pending", "payment_status": "captured", "special_requests": "Nothing all good", "total_amount": 13060, "created_at": "2025-06-14T05:36:54.399Z", "updated_at": "2025-06-14T05:37:06.035Z", "metadata": {"cots": 0, "adults": 2, "add_ons": [{"name": "Executive Transfer Service ", "service_id": "prod_01JWZY0NJWFBW8BCV3VAYSWQW1", "adult_price": 120, "child_price": 80, "description": "Complimentary or pre-booked shuttle service between your accommodation and key transport hubs or attractions. Includes scheduled pick-up and drop-off at select locations. Ideal for seamless transfers during your stay.", "guest_usage": [], "total_price": 400, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 2, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Swedish Relaxation Massage", "service_id": "prod_01JX02Z2T82C8E1JD8X28RY3AR", "adult_price": 80, "child_price": 60, "description": "A timeless classic designed to melt away stress and tension. Our Swedish Relaxation Massage uses long, flowing strokes with gentle to medium pressure to improve circulation, ease muscle discomfort, and promote deep relaxation. Ideal for first-time spa-goers or those seeking pure serenity, this soothing treatment leaves your body refreshed and your mind at peace.", "guest_usage": [], "total_price": 280, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 2, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Premium Arrival Hamper: A Warm Introduction to Local Flavors and In-Room Comforts", "service_id": "prod_01JX0CC3NWEQHE5J103E3X3844", "adult_price": 0, "child_price": 0, "description": "Start your stay with a touch of indulgence. Our curated Welcome Hamper includes a selection of local delicacies, fine chocolates, a mini bottle of wine, and handcrafted wellness items. Perfect for unwinding after your journey or as a surprise treat for someone special. Delivered to your room upon arrival.\n", "guest_usage": [], "total_price": 80, "usage_dates": [], "pricing_type": "package", "currency_code": "USD", "package_price": 80, "service_level": "hotel", "adult_quantity": 0, "child_quantity": 0, "number_of_days": 0, "total_occupancy": 5, "package_quantity": 1, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Complete Ski Essentials Bundle: All-in-One Gear Package with Clothing, Safety Equipment, and Accessories for Every Ski Adventure", "service_id": "prod_01JXA3TCV5W2ZNEX8AWQJQXDTG", "adult_price": 0, "child_price": 0, "description": "Prepare for your next ski trip with our Complete Ski Essentials Bundle, thoughtfully curated to keep you safe, comfortable, and stylish on the slopes. This all-in-one package includes everything you need—from high-performance ski clothing designed to keep you warm and dry, to essential safety gear like helmets and avalanche tools, plus must-have accessories such as gloves, goggles, and more. Whether you’re a beginner hitting the groomed trails or an experienced adventurer exploring backcountry terrain, this bundle equips you for every condition and skill level. Simplify your packing and enjoy peace of mind knowing you have all the essentials in one convenient package.", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}], "total_price": 120, "usage_dates": ["2025-06-15", "2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 4, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 20, "per_day_child_price": 10}, {"name": "Snowzone Access Pass", "service_id": "prod_01JXA65E9X56GM2GV732EHZABN", "adult_price": 300, "child_price": 200, "description": "Designated snow-covered area or facility where skiing, snowboarding, and other snow-related activities take place.", "guest_usage": [], "total_price": 1000, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 2, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Gym & Wellness Access", "service_id": "prod_01JXA8K32CQ6P65X2C6BV4Z2A1", "adult_price": 0, "child_price": 0, "description": "24/7 Gym Access,\nState-of-the-art Cardio Equipment (Treadmills, Bikes, Ellipticals),\nStrength Training Machines,\nFree Weights & Resistance Bands,\nSmart Fitness Mirrors / Virtual Workout Stations,\nWellness Towels, Water, and Headphones Provided\n", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-16", "2025-06-15"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}], "total_price": 400, "usage_dates": ["2025-06-15", "2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 4, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 60, "per_day_child_price": 40}, {"name": "Signature Spa Essentials Experience: Indulgent Add-On for Mind and Body Wellness", "service_id": "prod_01JXAA1VDF5QS5FTQ802VS8JRV", "adult_price": 0, "child_price": 0, "description": "Steam, Sauna & Jacuzzi Access\nInfrared Sauna Session\nHerbal Bath Soak\nJet Shower Experience\nCold Plunge Therapy\nSpa Refreshments (detox tea, infused water, etc.)\n", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}], "total_price": 280, "usage_dates": ["2025-06-15", "2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 4, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 50, "per_day_child_price": 20}, {"name": "City sightseeing tour", "service_id": "prod_01JXCN8GNQRFX94JYZ93KS1TJ3", "adult_price": 250, "child_price": 150, "description": "Discover the best of the city with a guided sightseeing tour that highlights its most iconic landmarks, cultural spots, and hidden gems. Travel comfortably by bus or on foot as a knowledgeable local guide shares fascinating stories, historical insights, and insider tips.\n\nIncludes:\nVisits to major attractions (e.g., monuments, museums, plazas)\n\nOptional hotel pickup and drop-off\nAudio guides or multilingual commentary (in select tours)\nSmall group or private options available\nIdeal for: First-time visitors, history lovers, and anyone looking to experience the city beyond the guidebook.\nDuration: 2 to 4 hours (half-day) or full-day options available.\n\n", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}], "total_price": 1200, "usage_dates": ["2025-06-15", "2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "destination", "adult_quantity": 4, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 200, "per_day_child_price": 100}, {"name": "Public Transport Pass", "service_id": "prod_01JXCNRN9S4E62VPK3RDWN9V61", "adult_price": 30, "child_price": 20, "description": "Make getting around easy and affordable with a public transport pass valid throughout your stay. Enjoy unlimited access to local buses, trams, subways, or regional trains — perfect for sightseeing, shopping, and exploring nearby neighborhoods without the hassle of tickets or traffic.\nIdeal for: City explorers, business travelers, or guests planning to visit multiple attractions.\nAvailability: Available for 1-day, 3-day, or week-long durations.", "guest_usage": [], "total_price": 100, "usage_dates": [], "pricing_type": "per_person", "currency_code": "USD", "package_price": 0, "service_level": "destination", "adult_quantity": 2, "child_quantity": 2, "number_of_days": 0, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 0}, {"name": "Kids Club & Playzone: Supervised Games, Toys, and More", "service_id": "prod_01JXF4YGNBYK932DE7TT03ZNK3", "adult_price": 0, "child_price": 0, "description": "Let your little ones enjoy fun-filled moments at our Kid’s Playzone — a safe, colorful, and engaging space designed just for children. With a variety of age-appropriate toys, games, soft play areas, and interactive activities, it’s the perfect spot for playtime while parents relax nearby.", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}], "total_price": 200, "usage_dates": ["2025-06-15", "2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "hotel", "adult_quantity": 4, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 0, "per_day_child_price": 50}, {"name": "Air Travel Services: Convenient Flight Planning for a Hassle-Free Trip", "service_id": "prod_01JXEQMZBG51NB680RG1R5NHKF", "adult_price": 0, "child_price": 0, "description": "Now plan your entire trip in one place! With our new Flight Booking Add-On, you can seamlessly book flights while reserving your hotel — saving time, effort, and ensuring a smoother travel experience. Whether you're planning a quick getaway or a long vacation, enjoy the convenience of bundling your stay and travel together.......\nBest fares, top airlines, and full itinerary control — all in one platform.", "guest_usage": [{"guest_type": "adult", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "adult", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 0, "usage_dates": ["2025-06-15", "2025-06-16"]}, {"guest_type": "child", "guest_index": 1, "usage_dates": ["2025-06-15", "2025-06-16"]}], "total_price": 6400, "usage_dates": ["2025-06-15", "2025-06-16"], "pricing_type": "usage_based", "currency_code": "USD", "package_price": 0, "service_level": "destination", "adult_quantity": 4, "child_quantity": 4, "number_of_days": 2, "total_occupancy": 0, "package_quantity": 0, "per_day_adult_price": 800, "per_day_child_price": 800}], "cart_id": "cart_01JXPEZW8MP5HT3MPYP808QD8P", "infants": 1, "children": 2, "hotel_id": "01JWDSWC6RGPD00W1XRW9ER415", "cot_total": 0, "meal_plan": "fb", "travelers": {"adults": [{"name": "<PERSON>esh <PERSON>"}, {"age": 24, "name": "<PERSON> Adult One"}], "infants": [{"age": 2, "name": "This is Kolandhai Infant 1"}], "children": [{"age": 11, "name": "<PERSON> One"}, {"age": 11, "name": "<PERSON> One"}]}, "extra_beds": 2, "guest_name": "<PERSON>esh <PERSON>", "cot_details": null, "guest_email": "<EMAIL>", "guest_phone": "**********", "cot_quantity": 0, "reservations": [{"id": "room_avail_01JWDTK0FVHXN0DYJHTKJ4582H", "status": "cart_reserved", "room_id": "variant_01JWDTGKEPQRV5R0KKDH9P066Z", "to_date": "2025-06-16T18:30:00.000Z", "from_date": "2025-06-15T18:30:00.000Z"}, {"id": "room_avail_01JWDTK0FVJQ6E96ZF1NCXN01H", "status": "cart_reserved", "room_id": "variant_01JWDTGKEPQRV5R0KKDH9P066Z", "to_date": "2025-06-15T18:30:00.000Z", "from_date": "2025-06-14T18:30:00.000Z"}], "total_amount": 2660, "check_in_date": "2025-06-15T00:00:00.000Z", "check_in_time": "14:00", "currency_code": "usd", "check_out_date": "2025-06-17T00:00:00.000Z", "check_out_time": "11:00", "payment_status": "paid", "room_config_id": "prod_01JWDT5BNBDPZ7MMNHAGK95TWT", "extra_bed_total": 60, "number_of_rooms": 1, "number_of_guests": 5, "room_config_name": "Junior Suite", "special_requests": "Nothing all good", "extra_bed_details": {"nights": 2, "check_in": "2025-06-15", "check_out": "2025-06-17", "room_name": "Junior Suite", "total_price": 60, "currency_code": "USD", "number_of_beds": 2, "room_config_id": "prod_01JWDT5BNBDPZ7MMNHAGK95TWT", "extra_bed_config": {"id": "extra_bed_config_01", "name": "Extra Bed", "type": "EXTRA_BED"}, "guest_assignments": [{"guest_age": 11, "bed_number": 1, "guest_name": "<PERSON> One"}, {"guest_age": 11, "bed_number": 2, "guest_name": "<PERSON> One"}], "price_per_bed_per_night": 15}, "extra_bed_quantity": 2, "add_on_total_amount": 1046000, "payment_completed_at": "2025-06-14T05:37:05.637Z", "stripe_payment_intent_id": "pi_3RZmlbAQUNgZJQ2K1irQ8Qqo", "stripe_checkout_session_id": "cs_test_b1TTp4OSJumEnl9PrWBzn38jcYrqz6Sdvenl3FpiECKERFkHncDiHsSXBz"}, "hotel_name": "Romantik Hotel Schwiezerhof", "room_config_name": "Junior Suite", "guest_info": {"primary_guest": {"name": "<PERSON>esh <PERSON>", "email": "<EMAIL>", "phone": "**********"}, "customer_account": {"first_name": "<PERSON><PERSON>", "last_name": "KK", "email": "<EMAIL>", "phone": "+91 **********", "metadata": {"source": "otp_registration"}, "addresses": []}, "preferred_contact": {"full_name": "<PERSON>esh <PERSON>", "email": "<EMAIL>", "phone": "**********"}, "travelers": {"adults": [{"id": "adult_1", "name": "<PERSON>esh <PERSON>", "age": null, "type": "adult"}, {"id": "adult_2", "name": "<PERSON> Adult One", "age": 24, "type": "adult"}], "children": [{"id": "child_1", "name": "<PERSON> One", "age": 11, "type": "child"}, {"id": "child_2", "name": "<PERSON> One", "age": 11, "type": "child"}], "infants": [{"id": "infant_1", "name": "This is Kolandhai Infant 1", "age": 2, "type": "infant"}], "total_count": 5, "summary": {"adults": 2, "children": 2, "infants": 1}}}}