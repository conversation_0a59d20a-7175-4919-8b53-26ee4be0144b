import React, { useState, useEffect } from "react";
import { type HotelAddOn } from "../../utils/store/hotels";

interface BookingData {
  hotelId: string;
  guestCount: number;
  childrenCount: number;
  infantCount: number;
  currencyCode: string;
  guestDetails?: {
    firstName: string;
    lastName: string;
  };
  travelers?: {
    adults: Array<{ name: string; age?: number }>;
    children: Array<{ name: string; age?: number }>;
    infants: Array<{ name: string; age?: number }>;
  };
}

interface GuestDateSelection {
  guestId: string;
  guestName: string;
  guestType: "adult" | "child";
  selectedDates: string[];
}

interface SelectedAddOn {
  service_id: string;
  pricing_type: "per_person" | "package" | "usage_based";
  selected_guests?: {
    adults: string[];
    children: string[];
  };
  guest_date_selections?: GuestDateSelection[];
  quantities: {
    adult_quantity: number;
    child_quantity: number;
    package_quantity: number;
  };
  total_price: number;
  name: string;
}

interface AddOnSelectionModalProps {
  addOn: HotelAddOn;
  bookingData: BookingData;
  onSave: (selection: SelectedAddOn | null) => void;
  onClose: () => void;
  existingSelection?: SelectedAddOn;
}

const AddOnSelectionModal: React.FC<AddOnSelectionModalProps> = ({
  addOn,
  bookingData,
  onSave,
  onClose,
  existingSelection,
}) => {
  const [selectedAdults, setSelectedAdults] = useState<string[]>([]);
  const [selectedChildren, setSelectedChildren] = useState<string[]>([]);
  const [showFullDescription, setShowFullDescription] = useState(false);

  // Initialize with existing selection if available
  useEffect(() => {
    if (existingSelection && existingSelection.selected_guests) {
      setSelectedAdults(existingSelection.selected_guests.adults || []);
      setSelectedChildren(existingSelection.selected_guests.children || []);
    }
  }, [existingSelection]);

  // Get guest lists
  const getGuestLists = () => {
    const adults: string[] = [];
    const children: string[] = [];

    // Add lead guest
    if (
      bookingData.guestDetails?.firstName &&
      bookingData.guestDetails?.lastName
    ) {
      adults.push(
        `${bookingData.guestDetails.firstName} ${bookingData.guestDetails.lastName}`
      );
    }

    // Add additional travelers
    if (bookingData.travelers) {
      bookingData.travelers.adults?.forEach((adult) => {
        if (adult.name && !adults.includes(adult.name)) {
          adults.push(adult.name);
        }
      });

      bookingData.travelers.children?.forEach((child) => {
        if (child.name) {
          children.push(child.name);
        }
      });
    }

    return { adults, children };
  };

  const { adults, children } = getGuestLists();

  const handleAdultToggle = (guestName: string) => {
    setSelectedAdults((prev) =>
      prev.includes(guestName)
        ? prev.filter((name) => name !== guestName)
        : [...prev, guestName]
    );
  };

  const handleChildToggle = (guestName: string) => {
    setSelectedChildren((prev) =>
      prev.includes(guestName)
        ? prev.filter((name) => name !== guestName)
        : [...prev, guestName]
    );
  };

  const calculateTotal = () => {
    const adultCount = selectedAdults.length;
    const childCount = selectedChildren.length;
    return adultCount * addOn.adult_price + childCount * addOn.child_price;
  };

  const handleSave = () => {
    // If no guests are selected, pass null to indicate removal
    if (selectedAdults.length === 0 && selectedChildren.length === 0) {
      onSave(null);
      return;
    }

    const selection: SelectedAddOn = {
      service_id: addOn.id,
      pricing_type: "per_person",
      selected_guests: {
        adults: selectedAdults,
        children: selectedChildren,
      },
      quantities: {
        adult_quantity: selectedAdults.length,
        child_quantity: selectedChildren.length,
        package_quantity: 0,
      },
      total_price: calculateTotal(),
      name: addOn.name,
    };

    onSave(selection);
  };

  // Helper function to truncate description
  const getTruncatedDescription = (text: string, wordLimit: number = 26) => {
    const words = text.split(" ");
    if (words.length <= wordLimit) {
      return { text, needsReadMore: false };
    }
    return {
      text: words.slice(0, wordLimit).join(" ") + "...",
      needsReadMore: true,
    };
  };

  const { text: descriptionText, needsReadMore } = getTruncatedDescription(
    addOn.description
  );

  return (
    <div className="fixed inset-0 z-[1200] flex items-center justify-center p-4">
      {/* Overlay */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      ></div>

      {/* Modal Content */}
      <div className="relative bg-white rounded-2xl shadow-2xl w-full max-w-4xl h-[85vh] overflow-hidden flex flex-col">
        {/* Header with Image */}
        <div className="relative h-48 overflow-hidden">
          <img
            src={
              addOn.images?.[0] ||
              "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
            }
            alt={addOn.name}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 via-black/20 to-transparent"></div>

          {/* Close Button */}
          <button
            onClick={onClose}
            className="absolute top-4 right-4 w-10 h-10 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center text-white hover:bg-white/30 transition-colors"
          >
            <svg
              className="w-5 h-5"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>

          {/* Title Overlay */}
          <div className="absolute bottom-4 left-6 right-6">
            <h2 className="text-2xl font-bold text-white mb-1">{addOn.name}</h2>
          </div>
        </div>

        {/* Main Content */}
        <div className="flex flex-col lg:flex-row flex-1 overflow-hidden">
          {/* Left Side - Guest Selection */}
          <div className="flex-1 p-6 overflow-y-auto">
            {/* Description */}
            <div className="mb-6">
              <h3 className="text-lg font-semibold text-gray-900 mb-2">
                About this service
              </h3>
              <div className="text-gray-600 text-sm leading-relaxed">
                <p>
                  {showFullDescription ? addOn.description : descriptionText}
                </p>
                {needsReadMore && (
                  <button
                    onClick={() => setShowFullDescription(!showFullDescription)}
                    className="text-[#3566ab] hover:text-[#285DA6] text-sm font-medium mt-1 inline-block"
                  >
                    {showFullDescription ? "Read less" : "Read more"}
                  </button>
                )}
              </div>
            </div>

            {/* Guest Selection */}
            <div className="space-y-6">
              {/* Adults */}
              {adults.length > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg
                      className="w-5 h-5 mr-2 text-[#3566ab]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Adults
                  </h3>
                  <div className="grid gap-3">
                    {adults.map((adult, index) => (
                      <label
                        key={index}
                        className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                          selectedAdults.includes(adult)
                            ? "border-[#3566ab] bg-blue-50"
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={selectedAdults.includes(adult)}
                          onChange={() => handleAdultToggle(adult)}
                          className="w-5 h-5 text-[#3566ab] border-gray-300 rounded focus:ring-[#3566ab] focus:ring-2"
                        />
                        <div className="ml-4 flex-1">
                          <div className="font-semibold text-gray-900">
                            {adult}
                          </div>
                          <div className="text-sm text-gray-500">
                            {bookingData.currencyCode} {addOn.adult_price} per
                            person
                          </div>
                        </div>
                        {selectedAdults.includes(adult) && (
                          <div className="text-[#3566ab]">
                            <svg
                              className="w-5 h-5"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        )}
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* Children */}
              {children.length > 0 && addOn.child_price > 0 && (
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <svg
                      className="w-5 h-5 mr-2 text-[#3566ab]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M10 2a4 4 0 00-4 4v1H5a1 1 0 00-.994.89l-1 9A1 1 0 004 18h12a1 1 0 00.994-1.11l-1-9A1 1 0 0015 7h-1V6a4 4 0 00-4-4zM8 6V5a2 2 0 114 0v1H8z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Children
                  </h3>
                  <div className="grid gap-3">
                    {children.map((child, index) => (
                      <label
                        key={index}
                        className={`flex items-center p-4 border-2 rounded-xl cursor-pointer transition-all duration-200 ${
                          selectedChildren.includes(child)
                            ? "border-[#3566ab] bg-blue-50"
                            : "border-gray-200 hover:border-gray-300 hover:bg-gray-50"
                        }`}
                      >
                        <input
                          type="checkbox"
                          checked={selectedChildren.includes(child)}
                          onChange={() => handleChildToggle(child)}
                          className="w-5 h-5 text-[#3566ab] border-gray-300 rounded focus:ring-[#3566ab] focus:ring-2"
                        />
                        <div className="ml-4 flex-1">
                          <div className="font-semibold text-gray-900">
                            {child}
                          </div>
                          <div className="text-sm text-gray-500">
                            {bookingData.currencyCode} {addOn.child_price} per
                            child
                          </div>
                        </div>
                        {selectedChildren.includes(child) && (
                          <div className="text-[#3566ab]">
                            <svg
                              className="w-5 h-5"
                              fill="currentColor"
                              viewBox="0 0 20 20"
                            >
                              <path
                                fillRule="evenodd"
                                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                                clipRule="evenodd"
                              />
                            </svg>
                          </div>
                        )}
                      </label>
                    ))}
                  </div>
                </div>
              )}

              {/* No guests message */}
              {adults.length === 0 && children.length === 0 && (
                <div className="text-center py-12">
                  <div className="w-16 h-16 bg-gray-100 rounded-full flex items-center justify-center mx-auto mb-4">
                    <svg
                      className="w-8 h-8 text-gray-400"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"
                      />
                    </svg>
                  </div>
                  <p className="text-gray-500 text-lg font-medium">
                    No guest information available
                  </p>
                  <p className="text-gray-400 text-sm mt-1">
                    Please complete guest details first
                  </p>
                </div>
              )}
            </div>
          </div>

          {/* Right Side - Pricing Summary */}
          <div className="lg:w-80 bg-gradient-to-br from-gray-50 to-blue-50/30 border-l border-gray-200 flex flex-col">
            <div className="p-6 flex-1 overflow-y-auto flex flex-col">
              <h3 className="text-lg font-semibold text-gray-900 mb-6">
                Pricing Summary
              </h3>

              {/* Pricing Details */}
              <div className="space-y-4 mb-6 flex-1">
                <div className="bg-white rounded-lg p-4 shadow-sm">
                  <h4 className="font-medium text-gray-900 mb-3">
                    Service Rates
                  </h4>
                  <div className="space-y-2 text-sm">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Adult rate:</span>
                      <span className="font-medium">
                        {bookingData.currencyCode} {addOn.adult_price}
                      </span>
                    </div>
                    {addOn.child_price > 0 && (
                      <div className="flex justify-between">
                        <span className="text-gray-600">Child rate:</span>
                        <span className="font-medium">
                          {bookingData.currencyCode} {addOn.child_price}
                        </span>
                      </div>
                    )}
                  </div>
                </div>

                {(selectedAdults.length > 0 || selectedChildren.length > 0) && (
                  <div className="bg-white rounded-lg p-4 shadow-sm">
                    <h4 className="font-medium text-gray-900 mb-3">
                      Selected Guests
                    </h4>
                    <div className="space-y-2 text-sm">
                      {selectedAdults.length > 0 && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {selectedAdults.length} Adult
                            {selectedAdults.length > 1 ? "s" : ""}
                          </span>
                          <span className="font-medium">
                            {bookingData.currencyCode}{" "}
                            {selectedAdults.length * addOn.adult_price}
                          </span>
                        </div>
                      )}
                      {selectedChildren.length > 0 && (
                        <div className="flex justify-between">
                          <span className="text-gray-600">
                            {selectedChildren.length} Child
                            {selectedChildren.length > 1 ? "ren" : ""}
                          </span>
                          <span className="font-medium">
                            {bookingData.currencyCode}{" "}
                            {selectedChildren.length * addOn.child_price}
                          </span>
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>

              {/* Total */}
              <div className="border-t pt-4">
                <div className="flex justify-between items-center mb-6">
                  <span className="text-lg font-semibold text-gray-900">
                    Total Amount:
                  </span>
                  <span className="text-xl font-bold text-[#3566ab]">
                    {bookingData.currencyCode} {calculateTotal()}
                  </span>
                </div>

                {/* Action Buttons */}
                <div className="space-y-3">
                  <button
                    onClick={handleSave}
                    className="w-full py-3 px-4 rounded-lg font-semibold transition-colors bg-[#3566ab] text-white hover:bg-[#285DA6] shadow-lg hover:shadow-xl"
                  >
                    Save
                  </button>
                  <button
                    onClick={onClose}
                    className="w-full py-2 px-4 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default AddOnSelectionModal;
