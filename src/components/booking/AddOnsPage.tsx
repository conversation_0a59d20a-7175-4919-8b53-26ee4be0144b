import React, { useState, useEffect, useMemo } from "react";
import {
  fetchHotelAddOns,
  type HotelAddOn,
  type AddOnCategory,
} from "../../utils/store/hotels";
import { createHotelCart, createCheckoutSession } from "../../utils/store/cart";
import type { CartAddOn } from "../../utils/store/cart";
import { convertDateToAPIFormat } from "../../utils/dateRangeUtils";
import AddOnSelectionModal from "./AddOnSelectionModal";
import UsageBasedAddOnModal from "./UsageBasedAddOnModal";

interface BookingData {
  hotelId: string;
  roomId: string;
  checkIn: string;
  checkOut: string;
  guestCount: number;
  childrenCount: number;
  infantCount: number;
  currencyCode: string;
  totalAmount: number;
  basePrice: number;
  taxesAndFees: number;
  guestDetails?: any;
  travelers?: {
    adults: Array<{ name: string; age?: number }>;
    children: Array<{ name: string; age?: number }>;
    infants: Array<{ name: string; age?: number }>;
  };
  [key: string]: any;
}

interface GuestDateSelection {
  guestId: string;
  guestName: string;
  guestType: "adult" | "child";
  selectedDates: string[];
}

interface SelectedAddOn {
  service_id: string;
  pricing_type: "per_person" | "package" | "usage_based";
  selected_guests?: {
    adults: string[];
    children: string[];
  };
  guest_date_selections?: GuestDateSelection[];
  quantities: {
    adult_quantity: number;
    child_quantity: number;
    package_quantity: number;
  };
  total_price: number;
  name: string;
}

interface AddOnsPageProps {
  bookingData: BookingData;
}

const AddOnsPage: React.FC<AddOnsPageProps> = ({ bookingData }) => {
  const [addOns, setAddOns] = useState<HotelAddOn[]>([]);
  const [categories, setCategories] = useState<AddOnCategory[]>([]);
  const [selectedCategory, setSelectedCategory] = useState<string>("all");
  const [selectedAddOns, setSelectedAddOns] = useState<SelectedAddOn[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedAddOnForModal, setSelectedAddOnForModal] =
    useState<HotelAddOn | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);
  const [isUsageBasedModalOpen, setIsUsageBasedModalOpen] = useState(false);
  const [selectedUsageBasedAddOn, setSelectedUsageBasedAddOn] =
    useState<HotelAddOn | null>(null);
  const [isProcessingPayment, setIsProcessingPayment] = useState(false);
  const [expandedDescriptions, setExpandedDescriptions] = useState<Set<string>>(
    new Set()
  );

  // Helper function to truncate description
  const getTruncatedDescription = (text: string, wordLimit: number = 15) => {
    const words = text.split(" ");
    if (words.length <= wordLimit) {
      return { text, needsReadMore: false };
    }
    return {
      text: words.slice(0, wordLimit).join(" ") + "...",
      needsReadMore: true,
    };
  };

  const toggleDescription = (addOnId: string) => {
    const uniqueKey = `addons-page-${addOnId}`;
    const newExpanded = new Set(expandedDescriptions);
    if (newExpanded.has(uniqueKey)) {
      newExpanded.delete(uniqueKey);
    } else {
      newExpanded.add(uniqueKey);
    }
    setExpandedDescriptions(newExpanded);
  };

  // Fetch add-ons when component mounts
  useEffect(() => {
    const loadAddOns = async () => {
      try {
        setLoading(true);
        const { add_ons, categories } = await fetchHotelAddOns(
          bookingData.hotelId,
          bookingData.currencyCode
        );
        // Filter add-ons that are available for this hotel
        const filteredAddOns = add_ons.filter((addon) => {
          if (addon.hotel_id && Array.isArray(addon.hotel_id)) {
            return addon.hotel_id.includes(bookingData.hotelId);
          }
          return addon.hotel_id === null;
        });
        setAddOns(filteredAddOns);
        setCategories(categories);
      } catch (err) {
        console.error("Error loading add-ons:", err);
        setError("Failed to load add-ons");
      } finally {
        setLoading(false);
      }
    };

    loadAddOns();
  }, [bookingData.hotelId, bookingData.currencyCode]);

  // Load existing selected add-ons from localStorage
  useEffect(() => {
    const storedBookingData = localStorage.getItem("bookingData");
    if (storedBookingData) {
      try {
        const parsed = JSON.parse(storedBookingData);
        if (parsed.selectedAddOns) {
          setSelectedAddOns(parsed.selectedAddOns);
        }
      } catch (error) {
        console.error("Error loading existing add-ons:", error);
      }
    }
  }, []);

  // Sort add-ons by pricing type: per_person -> usage_based -> package
  const sortAddOnsByPricingType = (addOns: any[]) => {
    const pricingTypeOrder = {
      per_person: 1,
      usage_based: 2,
      package: 3,
    };

    return [...addOns].sort((a, b) => {
      const orderA =
        pricingTypeOrder[a.pricing_type as keyof typeof pricingTypeOrder] || 4;
      const orderB =
        pricingTypeOrder[b.pricing_type as keyof typeof pricingTypeOrder] || 4;
      return orderA - orderB;
    });
  };

  // Filter add-ons based on selected category and sort by pricing type
  const filteredAddOns = useMemo(() => {
    let filtered;
    if (selectedCategory === "all") {
      filtered = addOns;
    } else {
      filtered = addOns.filter(
        (addon) => addon.category_id === selectedCategory
      );
    }
    return sortAddOnsByPricingType(filtered);
  }, [addOns, selectedCategory]);

  // Filter categories to only show those that have add-ons
  const availableCategories = useMemo(() => {
    return categories.filter((category) =>
      addOns.some((addon) => addon.category_id === category.id)
    );
  }, [categories, addOns]);

  // Reset to "all" if selected category becomes unavailable
  useEffect(() => {
    if (
      selectedCategory !== "all" &&
      !availableCategories.some((cat) => cat.id === selectedCategory)
    ) {
      setSelectedCategory("all");
    }
  }, [selectedCategory, availableCategories]);

  const handleAddOnClick = (addOn: HotelAddOn) => {
    if (addOn.pricing_type === "per_person") {
      // Open modal for per-person add-ons
      setSelectedAddOnForModal(addOn);
      setIsModalOpen(true);
    } else if (addOn.pricing_type === "usage_based") {
      // Open modal for usage-based add-ons
      setSelectedUsageBasedAddOn(addOn);
      setIsUsageBasedModalOpen(true);
    } else {
      // Handle package add-ons directly
      handlePackageAddOnSelect(addOn);
    }
  };

  const handlePackageAddOnSelect = (addOn: HotelAddOn) => {
    const existingIndex = selectedAddOns.findIndex(
      (selected) => selected.service_id === addOn.id
    );

    if (existingIndex >= 0) {
      // Remove if already selected
      const updated = selectedAddOns.filter(
        (_, index) => index !== existingIndex
      );
      setSelectedAddOns(updated);
    } else {
      // Add new package add-on
      const newSelection: SelectedAddOn = {
        service_id: addOn.id,
        pricing_type: "package",
        quantities: {
          adult_quantity: 0,
          child_quantity: 0,
          package_quantity: 1,
        },
        total_price: addOn.package_price || 0,
        name: addOn.name,
      };
      setSelectedAddOns([...selectedAddOns, newSelection]);
    }
  };

  const handlePerPersonAddOnSave = (selection: SelectedAddOn | null) => {
    const existingIndex = selectedAddOns.findIndex(
      (selected) => selected.service_id === selectedAddOnForModal?.id
    );

    if (selection === null) {
      // Remove the add-on if no guests are selected
      if (existingIndex >= 0) {
        const updated = selectedAddOns.filter(
          (_, index) => index !== existingIndex
        );
        setSelectedAddOns(updated);
      }
    } else {
      if (existingIndex >= 0) {
        // Update existing selection
        const updated = [...selectedAddOns];
        updated[existingIndex] = selection;
        setSelectedAddOns(updated);
      } else {
        // Add new selection
        setSelectedAddOns([...selectedAddOns, selection]);
      }
    }

    setIsModalOpen(false);
    setSelectedAddOnForModal(null);
  };

  const handleUsageBasedAddOnSave = (selection: SelectedAddOn | null) => {
    const existingIndex = selectedAddOns.findIndex(
      (selected) => selected.service_id === selectedUsageBasedAddOn?.id
    );

    if (selection === null) {
      // Remove the add-on if no dates are selected
      if (existingIndex >= 0) {
        const updated = selectedAddOns.filter(
          (_, index) => index !== existingIndex
        );
        setSelectedAddOns(updated);
      }
    } else {
      if (existingIndex >= 0) {
        // Update existing selection
        const updated = [...selectedAddOns];
        updated[existingIndex] = selection;
        setSelectedAddOns(updated);
      } else {
        // Add new selection
        setSelectedAddOns([...selectedAddOns, selection]);
      }
    }

    setIsUsageBasedModalOpen(false);
    setSelectedUsageBasedAddOn(null);
  };

  const isAddOnSelected = (addOnId: string) => {
    return selectedAddOns.some((selected) => selected.service_id === addOnId);
  };

  const getTotalAddOnsPrice = () => {
    return selectedAddOns.reduce(
      (total, addon) => total + addon.total_price,
      0
    );
  };

  const handlePayment = async (includeAddOns: boolean = true) => {
    console.log("Payment process started, includeAddOns:", includeAddOns);
    console.log("Booking data:", bookingData);

    if (!bookingData.guestDetails) {
      console.log("Guest details not found");
      setError(
        "Guest details not found. Please go back and complete your booking details."
      );
      return;
    }

    console.log("Guest details found, starting payment process");
    setIsProcessingPayment(true);

    try {
      let cartAddOns: CartAddOn[] = [];
      let addOnsTotal = 0;
      let finalSelectedAddOns: SelectedAddOn[] = [];

      if (includeAddOns && selectedAddOns.length > 0) {
        console.log("Preparing add-ons for cart creation");
        console.log("Selected add-ons:", selectedAddOns);

        // Prepare add-ons for cart creation
        cartAddOns = selectedAddOns.map((addon) => {
          if (addon.pricing_type === "package") {
            return {
              service_id: addon.service_id,
              pricing_type: "package",
              package_quantity: addon.quantities.package_quantity,
              total_occupancy:
                bookingData.guestCount +
                bookingData.childrenCount +
                bookingData.infantCount,
            };
          } else if (addon.pricing_type === "usage_based") {
            // Transform guest_date_selections to guest_usage format
            const guestUsage =
              addon.guest_date_selections
                ?.map((guest) => {
                  // Extract guest index from guestId (e.g., "adult_0" -> 0)
                  const guestIndex = parseInt(guest.guestId.split("_")[1]) || 0;

                  return {
                    guest_type: guest.guestType,
                    guest_index: guestIndex,
                    usage_dates: guest.selectedDates,
                  };
                })
                .filter((usage) => usage.usage_dates.length > 0) || []; // Only include guests with selected dates

            return {
              service_id: addon.service_id,
              pricing_type: "usage_based",
              adult_quantity: addon.quantities.adult_quantity,
              child_quantity: addon.quantities.child_quantity,
              guest_usage: guestUsage,
            };
          } else {
            return {
              service_id: addon.service_id,
              pricing_type: "per_person",
              adult_quantity: addon.quantities.adult_quantity,
              child_quantity: addon.quantities.child_quantity,
            };
          }
        });

        addOnsTotal = getTotalAddOnsPrice();
        finalSelectedAddOns = selectedAddOns;
        console.log("Cart add-ons prepared:", cartAddOns);
      } else {
        console.log("Skipping add-ons");
      }

      // Calculate total amount including extra beds, cots, and extra adults
      const extraBedTotal = bookingData.extraBedTotal || 0;
      const cotTotal = bookingData.cotTotal || 0;
      const extraAdultTotal = bookingData.extraAdultTotal || 0;
      const totalAmount =
        bookingData.basePrice +
        bookingData.taxesAndFees +
        extraBedTotal +
        cotTotal +
        extraAdultTotal;
      console.log(
        "Total amount calculated:",
        totalAmount,
        "including extra beds:",
        extraBedTotal,
        "cots:",
        cotTotal,
        "and extra adults:",
        extraAdultTotal
      );

      // Prepare extra beds data for cart payload
      const extraBedDetails =
        bookingData.extraBedQuantity > 0
          ? {
              occupancy_config_id: "occ_01JWDT5BNBDPZ7MMNHAGK95TWT",
              pricing_rule_id: "rule_01JWDT5BNBDPZ7MMNHAGK95TWT",
              daily_breakdown: Array.from(
                { length: bookingData.nights },
                (_, index) => {
                  const date = new Date(bookingData.checkIn);
                  date.setDate(date.getDate() + index);
                  const dayNames = [
                    "Sunday",
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                  ];
                  return {
                    date: date.toISOString().split("T")[0], // YYYY-MM-DD format
                    day_of_week: dayNames[date.getDay()],
                    price_per_bed:
                      bookingData.extraBedTotal /
                      (bookingData.extraBedQuantity * bookingData.nights),
                    pricing_source: "base",
                  };
                }
              ),
            }
          : null;

      // Prepare cot details for cart payload
      const cotDetails =
        bookingData.cotQuantity > 0
          ? {
              occupancy_config_id: "occ_02JWDT5BNBDPZ7MMNHAGK95TWT",
              pricing_rule_id: "rule_02JWDT5BNBDPZ7MMNHAGK95TWT",
              daily_breakdown: Array.from(
                { length: bookingData.nights },
                (_, index) => {
                  const date = new Date(bookingData.checkIn);
                  date.setDate(date.getDate() + index);
                  const dayNames = [
                    "Sunday",
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                  ];
                  return {
                    date: date.toISOString().split("T")[0], // YYYY-MM-DD format
                    day_of_week: dayNames[date.getDay()],
                    price_per_cot:
                      bookingData.cotTotal /
                      (bookingData.cotQuantity * bookingData.nights),
                    pricing_source: "base",
                  };
                }
              ),
            }
          : null;

      // Prepare extra adults beyond capacity details for cart payload
      const extraAdultsDetails =
        bookingData.extraAdultQuantity > 0
          ? {
              occupancy_config_id: "occ_03JWDT5BNBDPZ7MMNHAGK95TWT",
              pricing_rule_id: "rule_03JWDT5BNBDPZ7MMNHAGK95TWT",
              daily_breakdown: Array.from(
                { length: bookingData.nights },
                (_, index) => {
                  const date = new Date(bookingData.checkIn);
                  date.setDate(date.getDate() + index);
                  const dayNames = [
                    "Sunday",
                    "Monday",
                    "Tuesday",
                    "Wednesday",
                    "Thursday",
                    "Friday",
                    "Saturday",
                  ];
                  return {
                    date: date.toISOString().split("T")[0], // YYYY-MM-DD format
                    day_of_week: dayNames[date.getDay()],
                    price_per_adult:
                      bookingData.extraAdultTotal /
                      (bookingData.extraAdultQuantity * bookingData.nights),
                    pricing_source: "base",
                  };
                }
              ),
            }
          : null;

      // Create cart in Medusa backend
      console.log("Creating cart with data:", {
        hotel_id: bookingData.hotelId,
        room_config_id: bookingData.roomId,
        guest_name: `${bookingData.guestDetails.firstName} ${bookingData.guestDetails.lastName}`,
        total_amount: totalAmount,
        add_ons: cartAddOns.length > 0 ? cartAddOns : undefined,
      });

      const cartResponse = await createHotelCart({
        hotel_id: bookingData.hotelId,
        room_config_id: bookingData.roomId,
        room_id: `variant_${bookingData.roomId}`,
        check_in_date: convertDateToAPIFormat(bookingData.checkIn),
        check_out_date: convertDateToAPIFormat(bookingData.checkOut),
        check_in_time: bookingData.checkInTime || "15:00",
        check_out_time: bookingData.checkOutTime || "11:00",
        guest_name: `${bookingData.guestDetails.firstName} ${bookingData.guestDetails.lastName}`,
        guest_email: bookingData.guestDetails.email,
        guest_phone: bookingData.guestDetails.phone,
        adults: bookingData.guestCount || 1,
        children: bookingData.childrenCount || 0,
        infants: bookingData.infantCount || 0,
        travelers: bookingData.travelers,
        number_of_rooms: bookingData.roomQuantity || 1,
        total_amount: totalAmount,
        currency_code: bookingData.currencyCode.toLowerCase(),
        region_id: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1",
        special_requests: bookingData.guestDetails.specialRequests || "",
        add_ons: cartAddOns.length > 0 ? cartAddOns : undefined,
        metadata: {
          board_basis: bookingData.mealPlan || "none",
          meal_plan: bookingData.mealPlan || "none",
          room_name: bookingData.roomName || "",
          hotel_name: bookingData.hotelName || "",
          extra_bed_quantity: bookingData.extraBedQuantity || 0,
          extra_bed_total: bookingData.extraBedTotal || 0,
          extra_bed_details: extraBedDetails,
          cot_quantity: bookingData.cotQuantity || 0,
          cot_total: bookingData.cotTotal || 0,
          cot_details: cotDetails,
          extra_adults_beyond_capacity_details: extraAdultsDetails,
          extra_adults_beyond_capacity_total: bookingData.extraAdultTotal || 0,
          special_requests: bookingData.guestDetails?.specialRequests || "",
        },
        extra_beds: bookingData.extraBedQuantity || 0,
        cots: bookingData.cotQuantity || 0,
        extra_adults_beyond_capacity: bookingData.extraAdultQuantity || 0,
        shipping_address: {
          first_name: bookingData.guestDetails.firstName,
          last_name: bookingData.guestDetails.lastName,
          address_1: "",
          city: "",
          country_code: "US",
          postal_code: "",
          phone: bookingData.guestDetails.phone,
        },
      });

      console.log("Cart response received:", cartResponse);

      if (cartResponse && cartResponse.cart?.id) {
        console.log("Cart created successfully with ID:", cartResponse.cart.id);

        // Save cart ID and final booking data
        const finalBookingData = {
          ...bookingData,
          cartId: cartResponse.cart.id,
          selectedAddOns: finalSelectedAddOns,
          addOnsTotal: addOnsTotal,
          totalAmount: totalAmount,
        };
        localStorage.setItem("bookingData", JSON.stringify(finalBookingData));

        // Create Stripe Checkout session
        console.log(
          "Creating Stripe checkout session for cart:",
          cartResponse.cart.id
        );
        const checkoutResponse = await createCheckoutSession(
          cartResponse.cart.id
        );

        console.log("Checkout response received:", checkoutResponse);

        if (checkoutResponse?.url) {
          console.log("Redirecting to Stripe checkout:", checkoutResponse.url);
          // Redirect to Stripe Checkout
          window.location.href = checkoutResponse.url;
        } else {
          console.error("No checkout URL received");
          throw new Error("Failed to create checkout session");
        }
      } else {
        console.error("Cart creation failed or no cart ID received");
        throw new Error("Failed to create cart");
      }
    } catch (err) {
      console.error("Error processing payment:", err);
      setError("Failed to process your booking. Please try again.");
    } finally {
      console.log("Payment process completed, clearing loading state");
      setIsProcessingPayment(false);
    }
  };

  // Wrapper functions for different button actions
  const handleContinueToPayment = () => handlePayment(true);
  const handleSkipAndPay = () => handlePayment(false);

  if (loading) {
    return (
      <div className="mx-auto">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[1, 2, 3, 4, 5, 6].map((i) => (
            <div key={i} className="animate-pulse">
              <div className="bg-gray-200 rounded-lg h-48 mb-4"></div>
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
              <div className="h-4 bg-gray-200 rounded w-1/2"></div>
            </div>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="mx-auto text-center py-12">
        <p className="text-red-600 mb-4">{error}</p>
        <button
          onClick={() => window.location.reload()}
          className="bg-[#3566ab] text-white px-6 py-2 rounded-lg hover:bg-[#285DA6] transition-colors"
        >
          Try Again
        </button>
      </div>
    );
  }

  const handleBackToReview = () => {
    // Save current add-ons selection to localStorage before going back
    const updatedBookingData = {
      ...bookingData,
      selectedAddOns: selectedAddOns,
      addOnsTotal: getTotalAddOnsPrice(),
    };
    localStorage.setItem("bookingData", JSON.stringify(updatedBookingData));

    // Navigate back to review booking page
    window.location.href = "/review-booking";
  };

  return (
    <div className="mx-auto">
      {/* Back Button */}
      <div className="mb-6">
        <button
          onClick={handleBackToReview}
          className="flex items-center p-2 text-sm text-[#3566ab] hover:bg-[#3566ab]/5 border border-[#3566ab]/20 rounded-lg transition-colors duration-300"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <line x1="19" y1="12" x2="5" y2="12"></line>
            <polyline points="12 19 5 12 12 5"></polyline>
          </svg>
          <span className="ml-1">Back</span>
        </button>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
        {/* Left Column - Add-ons Grid */}
        <div className="lg:col-span-2">
          {/* Category Badges - Sticky */}
          {availableCategories.length > 0 && (
            <div className="sticky top-20 z-10 bg-white border-b border-gray-100 -mx-4 px-4 mb-8">
              <div className="flex items-center gap-3 overflow-x-auto pb-4 pt-4 scrollbar-hide">
                <button
                  onClick={() => setSelectedCategory("all")}
                  className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                    selectedCategory === "all"
                      ? "bg-[#3566ab] text-white shadow-md"
                      : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-sm"
                  }`}
                >
                  All
                </button>
                {availableCategories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setSelectedCategory(category.id)}
                    className={`px-4 py-2 rounded-lg text-sm font-medium transition-all duration-200 whitespace-nowrap ${
                      selectedCategory === category.id
                        ? "bg-[#3566ab] text-white shadow-md"
                        : "bg-gray-100 text-gray-700 hover:bg-gray-200 hover:shadow-sm"
                    }`}
                  >
                    {category.name}
                  </button>
                ))}
              </div>
            </div>
          )}

          {/* Add-ons Grid */}
          {filteredAddOns.length > 0 ? (
            <>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
                {filteredAddOns.map((addOn) => (
                  <div
                    key={addOn.id}
                    className={`relative overflow-hidden rounded-xl border-2 transition-all duration-300 flex flex-col ${
                      isAddOnSelected(addOn.id)
                        ? "border-[#3566ab] bg-gradient-to-r from-blue-50 to-indigo-50 shadow-md"
                        : "border-gray-200 bg-white hover:border-gray-300 hover:shadow-sm"
                    }`}
                  >
                    {/* Add-on Image */}
                    <div className="aspect-video overflow-hidden">
                      <img
                        src={
                          addOn.images?.[0] ||
                          "https://images.unsplash.com/photo-1566073771259-6a8506099945?ixlib=rb-4.0.3&auto=format&fit=crop&w=400&q=80"
                        }
                        alt={addOn.name}
                        className="w-full h-full object-cover"
                      />
                    </div>

                    {/* Add-on Content */}
                    <div className="p-5 flex flex-col flex-1">
                      <div className="flex justify-between items-start mb-3">
                        <h3 className="text-lg font-semibold text-gray-900 flex-1">
                          {addOn.name}
                        </h3>
                        <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800 ml-2">
                          {addOn.pricing_type === "per_person"
                            ? "Per Person"
                            : addOn.pricing_type === "usage_based"
                            ? "Usage Based"
                            : "Package"}
                        </span>
                      </div>

                      <div className="text-gray-600 text-sm mb-4">
                        {(() => {
                          const { text: descriptionText, needsReadMore } =
                            getTruncatedDescription(addOn.description);
                          const isExpanded = expandedDescriptions.has(
                            `addons-page-${addOn.id}`
                          );

                          return (
                            <>
                              <p className="leading-relaxed">
                                {isExpanded
                                  ? addOn.description
                                  : descriptionText}
                              </p>
                              {needsReadMore && (
                                <button
                                  onClick={(e) => {
                                    e.stopPropagation();
                                    toggleDescription(addOn.id);
                                  }}
                                  className="text-[#3566ab] hover:text-[#285DA6] text-sm font-medium mt-1 inline-block"
                                >
                                  {isExpanded ? "Show less" : "Show more"}
                                </button>
                              )}
                            </>
                          );
                        })()}
                      </div>

                      {/* Pricing */}
                      <div className="mb-4">
                        {addOn.pricing_type === "per_person" ? (
                          <div className="text-sm">
                            <div className="font-medium text-gray-900">
                              Adult: {bookingData.currencyCode}{" "}
                              {addOn.adult_price}
                            </div>
                            {addOn.child_price > 0 && (
                              <div className="text-gray-600">
                                Child: {bookingData.currencyCode}{" "}
                                {addOn.child_price}
                              </div>
                            )}
                          </div>
                        ) : addOn.pricing_type === "usage_based" ? (
                          <div className="text-sm">
                            <div className="font-medium text-gray-900">
                              Adult: {bookingData.currencyCode}{" "}
                              {addOn.per_day_adult_price}/day
                            </div>
                            {addOn.per_day_child_price > 0 && (
                              <div className="text-gray-600">
                                Child: {bookingData.currencyCode}{" "}
                                {addOn.per_day_child_price}/day
                              </div>
                            )}
                          </div>
                        ) : (
                          <div className="font-medium text-gray-900">
                            {bookingData.currencyCode} {addOn.package_price}
                          </div>
                        )}
                      </div>

                      {/* Select Button */}
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleAddOnClick(addOn);
                        }}
                        className={`w-full py-2 px-4 rounded-lg text-sm font-medium transition-colors mt-auto ${
                          isAddOnSelected(addOn.id)
                            ? "bg-[#3566ab] text-white"
                            : "bg-gray-100 text-gray-700 hover:bg-gray-200"
                        }`}
                      >
                        {isAddOnSelected(addOn.id) ? "Selected" : "Select"}
                      </button>
                    </div>
                  </div>
                ))}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <div className="text-gray-400 mb-4">
                <svg
                  className="mx-auto h-12 w-12"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={1}
                    d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8l-4 4m0 0l-4-4m4 4V3"
                  />
                </svg>
              </div>
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No add-ons available
              </h3>
              <p className="text-gray-600">
                {selectedCategory === "all"
                  ? "There are no add-ons available for this hotel."
                  : `No add-ons found in the ${
                      availableCategories.find(
                        (cat) => cat.id === selectedCategory
                      )?.name || "selected"
                    } category.`}
              </p>
            </div>
          )}

          {/* Action Buttons (Mobile Only) */}
          <div className="lg:hidden flex flex-col sm:flex-row gap-4 justify-center">
            {selectedAddOns.length > 0 ? (
              <>
                <button
                  onClick={handleSkipAndPay}
                  className="px-8 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                >
                  Skip Add-ons
                </button>
                <button
                  onClick={handleContinueToPayment}
                  disabled={isProcessingPayment}
                  className="px-8 py-3 bg-[#3566ab] text-white rounded-lg hover:bg-[#285DA6] transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isProcessingPayment ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    "Pay Now"
                  )}
                </button>
              </>
            ) : (
              <button
                onClick={handleSkipAndPay}
                disabled={isProcessingPayment}
                className="px-8 py-3 bg-[#3566ab] text-white rounded-lg hover:bg-[#285DA6] transition-colors disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
              >
                {isProcessingPayment ? (
                  <>
                    <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                    Processing...
                  </>
                ) : (
                  "Skip & Pay Now"
                )}
              </button>
            )}
          </div>
        </div>

        {/* Right Column - Pricing Summary */}
        <div className="lg:col-span-1">
          <div className="sticky top-24 space-y-6">
            {/* Booking Summary */}
            <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
              <h3 className="text-lg font-bold mb-4 text-[#3566ab]">
                Price Summary
              </h3>

              {/* Room Details */}
              <div className="space-y-3 mb-4 pb-4 border-b border-gray-200">
                <div className="flex justify-between text-sm">
                  <span className="text-gray-600">
                    Room Rate ({bookingData.nights} nights)
                  </span>
                  <span className="font-medium">
                    {bookingData.currencyCode} {bookingData.basePrice}
                  </span>
                </div>
                {bookingData.taxesAndFees > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">Taxes & Fees</span>
                    <span className="font-medium">
                      {bookingData.currencyCode} {bookingData.taxesAndFees}
                    </span>
                  </div>
                )}
                {bookingData.extraBedTotal > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                      Extra Beds ({bookingData.extraBedQuantity})
                    </span>
                    <span className="font-medium">
                      {bookingData.currencyCode} {bookingData.extraBedTotal}
                    </span>
                  </div>
                )}
                {bookingData.cotTotal > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                      Baby Cots ({bookingData.cotQuantity})
                    </span>
                    <span className="font-medium">
                      {bookingData.currencyCode} {bookingData.cotTotal}
                    </span>
                  </div>
                )}
                {bookingData.extraAdultTotal > 0 && (
                  <div className="flex justify-between text-sm">
                    <span className="text-gray-600">
                      Extra Adults ({bookingData.extraAdultQuantity})
                    </span>
                    <span className="font-medium">
                      {bookingData.currencyCode} {bookingData.extraAdultTotal}
                    </span>
                  </div>
                )}
              </div>

              {/* Selected Add-ons */}
              {selectedAddOns.length > 0 && (
                <div className="space-y-3 mb-4 pb-4 border-b border-gray-200">
                  <h4 className="font-medium text-gray-900">
                    Selected Add-ons
                  </h4>
                  {selectedAddOns
                    .sort((a, b) => {
                      const pricingTypeOrder = {
                        per_person: 1,
                        usage_based: 2,
                        package: 3,
                      };
                      const orderA =
                        pricingTypeOrder[
                          a.pricing_type as keyof typeof pricingTypeOrder
                        ] || 4;
                      const orderB =
                        pricingTypeOrder[
                          b.pricing_type as keyof typeof pricingTypeOrder
                        ] || 4;
                      return orderA - orderB;
                    })
                    .map((addon, index) => (
                      <div key={index} className="text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600 flex-1 pr-2">
                            {addon.name}
                          </span>
                          <span className="font-medium">
                            {bookingData.currencyCode} {addon.total_price}
                          </span>
                        </div>
                        {/* Show guest details for per-person add-ons */}
                        {addon.pricing_type === "per_person" &&
                          addon.selected_guests && (
                            <div className="text-xs text-gray-500 mt-1">
                              {[
                                ...(addon.selected_guests.adults || []).map(
                                  (name) => `${name}`
                                ),
                                ...(addon.selected_guests.children || []).map(
                                  (name) => `${name}`
                                ),
                              ].join(", ")}
                            </div>
                          )}
                        {/* Show guest details for usage-based add-ons */}
                        {addon.pricing_type === "usage_based" &&
                          addon.guest_date_selections && (
                            <div className="text-xs text-gray-500 mt-1">
                              {addon.guest_date_selections
                                .filter(
                                  (guest) => guest.selectedDates.length > 0
                                )
                                .map(
                                  (guest) =>
                                    `${guest.guestName}: ${
                                      guest.selectedDates.length
                                    } day${
                                      guest.selectedDates.length !== 1
                                        ? "s"
                                        : ""
                                    }`
                                )
                                .join(", ")}
                            </div>
                          )}
                      </div>
                    ))}
                  {/* <div className="flex justify-between text-sm font-medium pt-2 border-t border-gray-100">
                    <span className="text-gray-700">Add-ons Subtotal</span>
                    <span className="text-[#3566ab]">
                      {bookingData.currencyCode} {getTotalAddOnsPrice()}
                    </span>
                  </div> */}
                </div>
              )}

              {/* Total */}
              <div className="flex justify-between items-center">
                <span className="text-lg font-bold text-gray-900">
                  Total Amount
                </span>
                <span className="text-xl font-bold text-[#3566ab]">
                  {bookingData.currencyCode}{" "}
                  {(
                    bookingData.basePrice +
                    bookingData.taxesAndFees +
                    (bookingData.extraBedTotal || 0) +
                    (bookingData.cotTotal || 0) +
                    (bookingData.extraAdultTotal || 0) +
                    getTotalAddOnsPrice()
                  ).toFixed(0)}
                </span>
              </div>
            </div>

            {/* Action Buttons (Desktop) */}
            <div className="hidden lg:block space-y-3">
              {selectedAddOns.length > 0 ? (
                <>
                  <button
                    onClick={handleContinueToPayment}
                    disabled={isProcessingPayment}
                    className="w-full py-3 px-6 bg-[#3566ab] text-white rounded-lg font-karla uppercase tracking-wider transition-all duration-300 hover:bg-[#285DA6] disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                  >
                    {isProcessingPayment ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                        Processing...
                      </>
                    ) : (
                      "Pay Now"
                    )}
                  </button>
                  {/* <button
                    onClick={handleSkipAndPay}
                    className="w-full py-2 px-6 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    Skip Add-ons
                  </button> */}
                </>
              ) : (
                <button
                  onClick={handleSkipAndPay}
                  disabled={isProcessingPayment}
                  className="w-full py-3 px-6 bg-[#3566ab] text-white rounded-lg font-karla uppercase tracking-wider transition-all duration-300 hover:bg-[#285DA6] disabled:opacity-70 disabled:cursor-not-allowed flex items-center justify-center"
                >
                  {isProcessingPayment ? (
                    <>
                      <div className="animate-spin rounded-full h-5 w-5 border-t-2 border-b-2 border-white mr-2"></div>
                      Processing...
                    </>
                  ) : (
                    "Skip & Pay Now"
                  )}
                </button>
              )}
            </div>
          </div>
        </div>
      </div>

      {/* Per-person Add-on Selection Modal */}
      {isModalOpen && selectedAddOnForModal && (
        <AddOnSelectionModal
          addOn={selectedAddOnForModal}
          bookingData={bookingData}
          onSave={handlePerPersonAddOnSave}
          onClose={() => {
            setIsModalOpen(false);
            setSelectedAddOnForModal(null);
          }}
          existingSelection={selectedAddOns.find(
            (selected) => selected.service_id === selectedAddOnForModal.id
          )}
        />
      )}

      {/* Usage-based Add-on Selection Modal */}
      {isUsageBasedModalOpen && selectedUsageBasedAddOn && (
        <UsageBasedAddOnModal
          addOn={selectedUsageBasedAddOn}
          bookingData={bookingData}
          onSave={handleUsageBasedAddOnSave}
          onClose={() => {
            setIsUsageBasedModalOpen(false);
            setSelectedUsageBasedAddOn(null);
          }}
          existingSelection={selectedAddOns.find(
            (selected) => selected.service_id === selectedUsageBasedAddOn.id
          )}
        />
      )}
    </div>
  );
};

export default AddOnsPage;
