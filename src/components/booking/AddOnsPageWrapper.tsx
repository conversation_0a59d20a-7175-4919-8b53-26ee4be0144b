import React, { useState, useEffect } from "react";
import AddOnsPage from "./AddOnsPage";

interface BookingData {
  hotelId: string;
  roomId: string;
  checkIn: string;
  checkOut: string;
  guestCount: number;
  childrenCount: number;
  infantCount: number;
  currencyCode: string;
  totalAmount: number;
  basePrice: number;
  taxesAndFees: number;
  guestDetails?: any;
  travelers?: {
    adults: Array<{ name: string; age?: number }>;
    children: Array<{ name: string; age?: number }>;
    infants: Array<{ name: string; age?: number }>;
  };
  [key: string]: any;
}

const AddOnsPageWrapper: React.FC = () => {
  const [bookingData, setBookingData] = useState<BookingData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Load booking data from localStorage
    const loadBookingData = () => {
      try {
        const storedData = localStorage.getItem("bookingData");
        if (storedData) {
          const parsed = JSON.parse(storedData);
          setBookingData(parsed);
        } else {
          // Redirect to home if no booking data found
          window.location.href = "/";
        }
      } catch (error) {
        console.error("Error loading booking data:", error);
        window.location.href = "/";
      } finally {
        setLoading(false);
      }
    };

    loadBookingData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-48 mb-4"></div>
          <div className="h-4 bg-gray-200 rounded w-32"></div>
        </div>
      </div>
    );
  }

  if (!bookingData) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <div className="text-center">
          <h2 className="text-xl font-semibold text-gray-900 mb-2">
            No booking data found
          </h2>
          <p className="text-gray-600 mb-4">
            Please start your booking from the beginning.
          </p>
          <button
            onClick={() => (window.location.href = "/")}
            className="bg-[#3566ab] text-white px-6 py-2 rounded-lg hover:bg-[#285DA6] transition-colors"
          >
            Go to Home
          </button>
        </div>
      </div>
    );
  }

  return <AddOnsPage bookingData={bookingData} />;
};

export default AddOnsPageWrapper;
