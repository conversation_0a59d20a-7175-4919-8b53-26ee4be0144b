import React, { useEffect, useState, useCallback } from "react";
import type { Booking, AddOn } from "../../types/booking";
import { downloadBookingInvoice } from "../../utils/store/bookings";

// Separate components for different pricing types
interface PricingDisplayProps {
  addOn: AddOn;
  currencyCode: string;
}

const PerPersonPricingDisplay: React.FC<PricingDisplayProps> = ({
  addOn,
  currencyCode,
}) => {
  const formatCurrency = (amount: number, currency: string) => {
    return `${currency.toUpperCase()} ${amount.toFixed(2)}`;
  };

  return (
    <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 mb-4">
      {addOn.adult_quantity && addOn.adult_quantity > 0 && (
        <div className="flex items-center p-4 bg-blue-50 border border-blue-200 rounded-xl">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          </div>
          <div>
            <p className="text-xs text-blue-700 uppercase tracking-wider font-medium">
              Adults
            </p>
            <p className="font-semibold text-gray-900">
              {addOn.adult_quantity} ×{" "}
              {formatCurrency(
                addOn.adult_price || 0,
                addOn.currency_code || currencyCode
              )}
            </p>
          </div>
        </div>
      )}

      {addOn.child_quantity && addOn.child_quantity > 0 && (
        <div className="flex items-center p-4 bg-green-50 border border-green-200 rounded-xl">
          <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center text-green-600 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div>
            <p className="text-xs text-green-700 uppercase tracking-wider font-medium">
              Children
            </p>
            <p className="font-semibold text-gray-900">
              {addOn.child_quantity} ×{" "}
              {formatCurrency(
                addOn.child_price || 0,
                addOn.currency_code || currencyCode
              )}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

const PackagePricingDisplay: React.FC<PricingDisplayProps> = ({
  addOn,
  currencyCode,
}) => {
  const formatCurrency = (amount: number, currency: string) => {
    return `${currency.toUpperCase()} ${amount.toFixed(2)}`;
  };

  // Calculate total guests covered by the package
  const totalGuestsCovered =
    (addOn.adult_quantity || 0) +
    (addOn.child_quantity || 0) +
    (addOn.infant_quantity || 0);
  const totalOccupancy = addOn.total_occupancy || totalGuestsCovered;

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
      {addOn.package_quantity && addOn.package_quantity > 0 && (
        <div className="flex items-center p-3 bg-purple-50 rounded-xl">
          <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center text-purple-600 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M20 7h-9"></path>
              <path d="M14 17H5"></path>
              <circle cx="17" cy="17" r="3"></circle>
              <circle cx="7" cy="7" r="3"></circle>
            </svg>
          </div>
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wider">
              Package
            </p>
            <p className="font-medium">
              {addOn.package_quantity} ×{" "}
              {formatCurrency(
                addOn.package_price || 0,
                addOn.currency_code || currencyCode
              )}
            </p>
          </div>
        </div>
      )}

      {/* Show total guests covered by package */}
      {totalOccupancy > 0 && (
        <div className="flex items-center p-3 bg-blue-50 rounded-xl">
          <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div>
            <p className="text-xs text-gray-500 uppercase tracking-wider">
              Guests Covered
            </p>
            <p className="font-medium">{totalOccupancy} guests included</p>
          </div>
        </div>
      )}

      {/* Show detailed breakdown only if we have specific quantities and they're meaningful */}
      {((addOn.adult_quantity || 0) > 0 || (addOn.child_quantity || 0) > 0) &&
        totalOccupancy > 1 && (
          <>
            {addOn.adult_quantity && addOn.adult_quantity > 0 && (
              <div className="flex items-center p-3 bg-gray-50 rounded-xl">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                <div>
                  <p className="text-xs text-gray-500 uppercase tracking-wider">
                    Adults
                  </p>
                  <p className="font-medium">{addOn.adult_quantity}</p>
                </div>
              </div>
            )}

            {addOn.child_quantity && addOn.child_quantity > 0 && (
              <div className="flex items-center p-3 bg-gray-50 rounded-xl">
                <div className="w-8 h-8 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
                <div>
                  <p className="text-xs text-gray-500 uppercase tracking-wider">
                    Children
                  </p>
                  <p className="font-medium">{addOn.child_quantity}</p>
                </div>
              </div>
            )}
          </>
        )}
    </div>
  );
};

interface UsageBasedPricingDisplayProps extends PricingDisplayProps {
  booking?: Booking;
}

const UsageBasedPricingDisplay: React.FC<UsageBasedPricingDisplayProps> = ({
  addOn,
  currencyCode,
  booking,
}) => {
  const formatCurrency = (amount: number, currency: string) => {
    return `${currency.toUpperCase()} ${amount.toFixed(2)}`;
  };

  // Calculate total usage days and guests for summary display
  const getTotalUsageDays = () => {
    if (addOn.guest_usage && addOn.guest_usage.length > 0) {
      // Get unique dates across all guests
      const allDates = new Set();
      addOn.guest_usage.forEach((usage: any) => {
        if (usage.usage_dates) {
          usage.usage_dates.forEach((date: string) => allDates.add(date));
        }
      });
      return allDates.size;
    }
    return addOn.usage_dates ? addOn.usage_dates.length : 0;
  };

  const getTotalGuests = () => {
    return addOn.guest_usage ? addOn.guest_usage.length : 0;
  };

  const totalUsageDays = getTotalUsageDays();
  const totalGuests = getTotalGuests();

  return (
    <div className="space-y-4 mb-4">
      {/* Package-style Summary */}
      <div className="bg-blue-50 rounded-xl p-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {/* Usage Summary */}
          <div className="flex items-center">
            <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                <line x1="16" y1="2" x2="16" y2="6"></line>
                <line x1="8" y1="2" x2="8" y2="6"></line>
                <line x1="3" y1="10" x2="21" y2="10"></line>
              </svg>
            </div>
            <div>
              <p className="text-xs text-gray-500 uppercase tracking-wider">
                Usage Days
              </p>
              <p className="font-semibold text-gray-900">
                {totalUsageDays} day{totalUsageDays !== 1 ? "s" : ""} ×{" "}
                {formatCurrency(
                  addOn.total_price || 0,
                  addOn.currency_code || currencyCode
                )}
              </p>
            </div>
          </div>

          {/* Guests Covered */}
          {totalGuests > 0 && (
            <div className="flex items-center">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                  <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                  <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                </svg>
              </div>
              <div>
                <p className="text-xs text-gray-500 uppercase tracking-wider">
                  Guests Covered
                </p>
                <p className="font-semibold text-gray-900">
                  {totalGuests} guest{totalGuests !== 1 ? "s" : ""} included
                </p>
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Guest-Specific Time Slots */}
      {addOn.guest_usage && addOn.guest_usage.length > 0 && (
        <div>
          <h6 className="text-sm font-medium text-gray-700 mb-3">
            Available Dates:
          </h6>
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
            {addOn.guest_usage.map((usage: any, usageIndex: number) => {
              // Get guest name from travelers object based on guest_type and index
              const getGuestName = () => {
                if (!booking?.metadata?.travelers)
                  return usage.guest_name || "Guest";

                const travelers = booking.metadata.travelers;
                const guestType = usage.guest_type;

                // For usage-based pricing, we need to track which guest this is
                // The usageIndex represents the order in guest_usage array
                // We need to map this to the correct guest in travelers
                let guestIndex = 0;

                // Count how many guests of this type we've seen before this index
                for (let i = 0; i < usageIndex; i++) {
                  if (addOn.guest_usage && addOn.guest_usage[i]?.guest_type === guestType) {
                    guestIndex++;
                  }
                }

                if (
                  guestType === "adult" &&
                  travelers.adults &&
                  travelers.adults[guestIndex]
                ) {
                  return travelers.adults[guestIndex].name;
                } else if (
                  guestType === "child" &&
                  travelers.children &&
                  travelers.children[guestIndex]
                ) {
                  return travelers.children[guestIndex].name;
                } else if (
                  guestType === "infant" &&
                  travelers.infants &&
                  travelers.infants[guestIndex]
                ) {
                  return travelers.infants[guestIndex].name;
                }

                return usage.guest_name || `${guestType} ${guestIndex + 1}`;
              };

              // Get consistent styling based on guest type
              const getGuestTypeStyles = (guestType: string) => {
                switch (guestType) {
                  case "adult":
                    return {
                      cardBg: "bg-blue-50",
                      cardBorder: "border-blue-200",
                      badgeBg: "bg-blue-100",
                      badgeText: "text-blue-700",
                      iconBg: "bg-blue-100",
                      iconText: "text-blue-600",
                    };
                  case "child":
                    return {
                      cardBg: "bg-green-50",
                      cardBorder: "border-green-200",
                      badgeBg: "bg-green-100",
                      badgeText: "text-green-700",
                      iconBg: "bg-green-100",
                      iconText: "text-green-600",
                    };
                  case "infant":
                    return {
                      cardBg: "bg-pink-50",
                      cardBorder: "border-pink-200",
                      badgeBg: "bg-pink-100",
                      badgeText: "text-pink-700",
                      iconBg: "bg-pink-100",
                      iconText: "text-pink-600",
                    };
                  default:
                    return {
                      cardBg: "bg-gray-50",
                      cardBorder: "border-gray-200",
                      badgeBg: "bg-gray-100",
                      badgeText: "text-gray-700",
                      iconBg: "bg-gray-100",
                      iconText: "text-gray-600",
                    };
                }
              };

              const styles = getGuestTypeStyles(usage.guest_type);

              return (
                <div
                  key={usageIndex}
                  className={`${styles.cardBg} border ${styles.cardBorder} rounded-lg p-4`}
                >
                  {/* Guest name header */}
                  <div className="flex items-center justify-between mb-3 pb-2 border-b border-gray-200">
                    <div className="flex items-center">
                      <div
                        className={`w-8 h-8 ${styles.iconBg} rounded-full flex items-center justify-center ${styles.iconText} mr-3`}
                      >
                        {usage.guest_type === "adult" && (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                            <circle cx="12" cy="7" r="4"></circle>
                          </svg>
                        )}
                        {usage.guest_type === "child" && (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                            <circle cx="9" cy="7" r="4"></circle>
                            <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                          </svg>
                        )}
                        {usage.guest_type === "infant" && (
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="12"
                            height="12"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <circle cx="12" cy="8" r="7"></circle>
                            <polyline points="8.21 13.89 7 23 12 20 17 23 15.79 13.88"></polyline>
                          </svg>
                        )}
                      </div>
                      <p className="font-semibold text-gray-900 text-sm">
                        {getGuestName()}
                      </p>
                    </div>
                    <span
                      className={`inline-flex items-center px-2.5 py-1 rounded-full text-xs font-medium ${styles.badgeBg} ${styles.badgeText}`}
                    >
                      {usage.guest_type.charAt(0).toUpperCase() +
                        usage.guest_type.slice(1)}
                    </span>
                  </div>

                  {/* Available dates for this guest */}
                  {usage.usage_dates && usage.usage_dates.length > 0 && (
                    <div className="flex flex-wrap gap-2">
                      {usage.usage_dates.map(
                        (date: string, dateIndex: number) => (
                          <div
                            key={dateIndex}
                            className="inline-flex items-center text-xs bg-white rounded-md px-2 py-1 border border-gray-100"
                          >
                            <div className="w-3 h-3 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 mr-1.5">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="8"
                                height="8"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                              >
                                <rect
                                  x="3"
                                  y="4"
                                  width="18"
                                  height="18"
                                  rx="2"
                                  ry="2"
                                ></rect>
                                <line x1="16" y1="2" x2="16" y2="6"></line>
                                <line x1="8" y1="2" x2="8" y2="6"></line>
                                <line x1="3" y1="10" x2="21" y2="10"></line>
                              </svg>
                            </div>
                            <span className="text-gray-700 font-medium">
                              {new Date(date).toLocaleDateString("en-US", {
                                month: "short",
                                day: "numeric",
                              })}
                            </span>
                          </div>
                        )
                      )}
                    </div>
                  )}

                  {/* If no specific dates, show general availability message */}
                  {(!usage.usage_dates || usage.usage_dates.length === 0) && (
                    <div className="inline-flex items-center text-xs bg-white rounded-md px-2 py-1 border border-gray-100">
                      <div className="w-3 h-3 bg-gray-100 rounded-full flex items-center justify-center text-gray-600 mr-1.5">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="8"
                          height="8"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <rect
                            x="3"
                            y="4"
                            width="18"
                            height="18"
                            rx="2"
                            ry="2"
                          ></rect>
                          <line x1="16" y1="2" x2="16" y2="6"></line>
                          <line x1="8" y1="2" x2="8" y2="6"></line>
                          <line x1="3" y1="10" x2="21" y2="10"></line>
                        </svg>
                      </div>
                      <span className="text-gray-700 font-medium">
                        Available for all booking dates
                      </span>
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
};

interface BookingDetailsModalProps {
  booking: Booking | null;
  isOpen: boolean;
  onClose: () => void;
}

const BookingDetailsModal: React.FC<BookingDetailsModalProps> = ({
  booking,
  isOpen,
  onClose,
}) => {
  if (!isOpen || !booking) return null;

  const [expandedDescriptions, setExpandedDescriptions] = useState<Set<string>>(
    new Set()
  );
  const [showAllAddOns, setShowAllAddOns] = useState(false);

  // Debug: Log state changes
  useEffect(() => {
    console.log(
      "expandedDescriptions changed:",
      Array.from(expandedDescriptions)
    );
  }, [expandedDescriptions]);

  useEffect(() => {
    console.log("showAllAddOns changed:", showAllAddOns);
  }, [showAllAddOns]);

  // Helper function to truncate description
  const getTruncatedDescription = (text: string, wordLimit: number = 15) => {
    const words = text.split(" ");
    if (words.length <= wordLimit) {
      return { text, needsReadMore: false };
    }
    return {
      text: words.slice(0, wordLimit).join(" ") + "...",
      needsReadMore: true,
    };
  };

  const toggleDescription = useCallback(
    (addOnId: string, addOnName?: string) => {
      // Use name as fallback if id is not available
      const identifier = addOnId || addOnName || "unknown";
      const uniqueKey = `booking-details-${identifier}`;
      console.log(
        "Toggling description for:",
        uniqueKey,
        "addOnId:",
        addOnId,
        "addOnName:",
        addOnName
      );
      setExpandedDescriptions((prev) => {
        const newExpanded = new Set(prev);
        if (newExpanded.has(uniqueKey)) {
          newExpanded.delete(uniqueKey);
          console.log("Collapsed description for:", uniqueKey);
        } else {
          newExpanded.add(uniqueKey);
          console.log("Expanded description for:", uniqueKey);
        }
        return newExpanded;
      });
    },
    []
  );

  const toggleShowAllAddOns = useCallback(() => {
    console.log("Toggling showAllAddOns");
    setShowAllAddOns((prev) => {
      console.log("showAllAddOns changing from", prev, "to", !prev);
      return !prev;
    });
  }, []);

  // Format date for display in a more user-friendly way
  const formatDisplayDate = (dateString: string) => {
    if (!dateString) return "Not available";

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid date";

    const options: Intl.DateTimeFormatOptions = {
      weekday: "long",
      year: "numeric",
      month: "long",
      day: "numeric",
    };

    return date.toLocaleDateString("en-US", options);
  };

  // Format date and time for display in a more user-friendly way
  const formatDisplayDateTime = (dateString: string) => {
    if (!dateString) return "Not available";

    const date = new Date(dateString);
    if (isNaN(date.getTime())) return "Invalid date";

    // Get the date part
    const formattedDate = formatDisplayDate(dateString);

    // Get the time part
    const hours = date.getHours();
    const minutes = date.getMinutes();
    const ampm = hours >= 12 ? "PM" : "AM";
    const formattedHours = hours % 12 || 12; // Convert 0 to 12 for 12 AM
    const formattedMinutes = minutes < 10 ? `0${minutes}` : minutes;

    // Return formatted date and time: "Monday, 15th January 2024 at 2:30 PM"
    return `${formattedDate} at ${formattedHours}:${formattedMinutes} ${ampm}`;
  };

  // Helper function to get meal plan label
  const getMealPlanLabel = (mealPlan?: string) => {
    const mealPlanLabels: Record<string, string> = {
      none: "No Meals",
      bb: "Bed & Breakfast",
      hb: "Half Board",
      fb: "Full Board",
    };
    return mealPlanLabels[mealPlan || ""] || mealPlan || "Not specified";
  };

  // Helper function to format currency
  const formatCurrency = (amount: number, currencyCode: string) => {
    return `${currencyCode.toUpperCase()} ${amount.toFixed(2)}`;
  };

  // Helper function to get pricing type label
  const getPricingTypeLabel = (pricingType?: string) => {
    const labels: Record<string, string> = {
      per_person: "Per Person",
      package: "Package",
      usage_based: "Usage Based",
    };
    return labels[pricingType || ""] || pricingType || "Unknown";
  };



  const handleDownloadInvoice = async () => {
    try {
      await downloadBookingInvoice(booking.id);
    } catch (error) {
      console.error("Failed to download invoice:", error);
      // You might want to show a toast notification here
    }
  };

  // Handle keyboard events
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === "Escape" && isOpen) {
        onClose();
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleKeyDown);
      // Prevent body scroll when sheet is open
      document.body.style.overflow = "hidden";
    }

    return () => {
      document.removeEventListener("keydown", handleKeyDown);
      document.body.style.overflow = "unset";
    };
  }, [isOpen, onClose]);

  return (
    <>
      {/* Backdrop */}
      <div
        className="fixed inset-0 bg-black bg-opacity-50 z-[9998] transition-opacity"
        onClick={onClose}
      />

      {/* Sheet */}
      <div
        className={`fixed inset-y-0 right-0 z-[9999] w-full sm:max-w-4xl bg-white shadow-xl transform transition-transform duration-300 ease-in-out ${
          isOpen ? "translate-x-0" : "translate-x-full"
        }`}
      >
        {/* Header */}
        <div className="sticky top-0 bg-white border-b border-gray-200 px-6 py-4 flex items-center justify-between">
          <div>
            <h3 className="text-xl font-semibold text-gray-900">
              Booking Details
            </h3>
            <p className="text-sm text-gray-500 mt-1">
              Booking ID: {booking.id}
            </p>
          </div>
          <div className="flex items-center space-x-3">
            <button
              onClick={handleDownloadInvoice}
              className="flex items-center px-3 py-2 bg-[#285DA6] text-white rounded-lg hover:bg-[#1A3A6E] hover:shadow-md transition-all duration-200 text-sm font-medium"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M21 15v4a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2v-4"></path>
                <polyline points="7 10 12 15 17 10"></polyline>
                <line x1="12" y1="15" x2="12" y2="3"></line>
              </svg>
              Download
            </button>
            <button
              onClick={onClose}
              className="w-8 h-8 flex items-center justify-center rounded-full hover:bg-gray-100 transition-colors"
              aria-label="Close sheet"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-gray-500"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>
        </div>

        {/* Content */}
        <div className="overflow-y-auto h-full pb-20 px-6 py-4">
          <div className="space-y-8">
            {/* Hotel & Stay Info - Modernized */}
            <div className="mb-8">
              <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                    <polyline points="9 22 9 12 15 12 15 22"></polyline>
                  </svg>
                </div>
                Hotel & Stay Information
              </h5>
              <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                        <polyline points="9 22 9 12 15 12 15 22"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Hotel Name
                      </p>
                      <p className="font-medium">{booking.hotel_name}</p>
                    </div>
                  </div>
                  {booking.destination_name && (
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                        >
                          <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                          <circle cx="12" cy="10" r="3"></circle>
                        </svg>
                      </div>
                      <div>
                        <p className="text-xs text-gray-500 uppercase tracking-wider">
                          Destination
                        </p>
                        <p className="font-medium">{booking.destination_name}</p>
                      </div>
                    </div>
                  )}
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                        <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Room Type
                      </p>
                      <p className="font-medium">
                        {booking.room_config_name || booking.room_type}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Check-in
                      </p>
                      <p className="font-medium">
                        {formatDisplayDate(booking.check_in_date)}
                        {booking.check_in_time && (
                          <span className="text-sm text-gray-500 block">
                            {booking.check_in_time}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <rect
                          x="3"
                          y="4"
                          width="18"
                          height="18"
                          rx="2"
                          ry="2"
                        ></rect>
                        <line x1="16" y1="2" x2="16" y2="6"></line>
                        <line x1="8" y1="2" x2="8" y2="6"></line>
                        <line x1="3" y1="10" x2="21" y2="10"></line>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Check-out
                      </p>
                      <p className="font-medium">
                        {formatDisplayDate(booking.check_out_date)}
                        {booking.check_out_time && (
                          <span className="text-sm text-gray-500 block">
                            {booking.check_out_time}
                          </span>
                        )}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Hotel Location Details */}
            {(booking.hotel_location || booking.hotel_address || booking.hotel_city) && (
              <div className="mb-8">
                <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#285DA6]"
                    >
                      <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
                      <circle cx="12" cy="10" r="3"></circle>
                    </svg>
                  </div>
                  Hotel Location
                </h5>
                <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                    {/* Location Details */}
                    <div className="space-y-4">
                      {booking.hotel_address && (
                        <div className="flex items-start">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3 mt-1">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                              <polyline points="9 22 9 12 15 12 15 22"></polyline>
                            </svg>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider">
                              Address
                            </p>
                            <p className="font-medium text-gray-900">{booking.hotel_address}</p>
                          </div>
                        </div>
                      )}

                      {booking.hotel_city && (
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                              <polyline points="9 22 9 12 15 12 15 22"></polyline>
                            </svg>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider">
                              City
                            </p>
                            <p className="font-medium">{booking.hotel_city}</p>
                          </div>
                        </div>
                      )}

                      {booking.hotel_country && (
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="10"></circle>
                              <line x1="2" y1="12" x2="22" y2="12"></line>
                              <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"></path>
                            </svg>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider">
                              Country
                            </p>
                            <p className="font-medium">{booking.hotel_country}</p>
                          </div>
                        </div>
                      )}

                      {booking.hotel_postal_code && (
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                              <polyline points="22,6 12,13 2,6"></polyline>
                            </svg>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider">
                              Postal Code
                            </p>
                            <p className="font-medium">{booking.hotel_postal_code}</p>
                          </div>
                        </div>
                      )}

                      {booking.hotel_timezone && (
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <circle cx="12" cy="12" r="10"></circle>
                              <polyline points="12,6 12,12 16,14"></polyline>
                            </svg>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider">
                              Timezone
                            </p>
                            <p className="font-medium">{booking.hotel_timezone}</p>
                          </div>
                        </div>
                      )}
                    </div>

                    {/* Google Maps */}
                    {(() => {
                      // Create location string for map - prefer hotel_location, fallback to address components
                      const locationForMap = booking.hotel_location ||
                        [booking.hotel_address, booking.hotel_city, booking.hotel_country]
                          .filter(Boolean)
                          .join(', ');

                      return locationForMap ? (
                        <div className="space-y-4">
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider mb-3">
                              Location on Map
                            </p>
                            <div className="w-full h-64 rounded-xl overflow-hidden border border-gray-200">
                              <iframe
                                src={`https://maps.google.com/maps?q=${encodeURIComponent(locationForMap)}&t=&z=13&ie=UTF8&iwloc=&output=embed`}
                                width="100%"
                                height="100%"
                                style={{ border: 0 }}
                                allowFullScreen
                                loading="lazy"
                                referrerPolicy="no-referrer-when-downgrade"
                                title="Hotel Location Map"
                              />
                            </div>
                          </div>
                        </div>
                      ) : null;
                    })()}
                  </div>
                </div>
              </div>
            )}

            {/* Guest Info - Modernized */}
            <div className="mb-8">
              <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                    <circle cx="12" cy="7" r="4"></circle>
                  </svg>
                </div>
                Guest Information
              </h5>
              <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                        <circle cx="12" cy="7" r="4"></circle>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Guest Name
                      </p>
                      <p className="font-medium">
                        {booking.guest_name || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"></path>
                        <polyline points="22,6 12,13 2,6"></polyline>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Email
                      </p>
                      <p className="font-medium">
                        {booking.guest_email || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M22 16.92v3a2 2 0 0 1-2.18 2 19.79 19.79 0 0 1-8.63-3.07 19.5 19.5 0 0 1-6-6 19.79 19.79 0 0 1-3.07-8.67A2 2 0 0 1 4.11 2h3a2 2 0 0 1 2 1.72 12.84 12.84 0 0 0 .7 2.81 2 2 0 0 1-.45 2.11L8.09 9.91a16 16 0 0 0 6 6l1.27-1.27a2 2 0 0 1 2.11-.45 12.84 12.84 0 0 0 2.81.7A2 2 0 0 1 22 16.92z"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Phone
                      </p>
                      <p className="font-medium">
                        {booking.guest_phone || "Not provided"}
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center">
                    <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <div>
                      <p className="text-xs text-gray-500 uppercase tracking-wider">
                        Number of Guests
                      </p>
                      <p className="font-medium">
                        {booking.number_of_guests || 1}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Travelers Details */}
            {(booking.guest_info?.travelers || booking.metadata?.travelers) && (
              <div className="mb-8">
                <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#285DA6]"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                  </div>
                  All Travelers
                </h5>
                <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                  {/* Use guest_info.travelers if available, otherwise fallback to metadata.travelers */}
                  {(() => {
                    const travelers =
                      booking.guest_info?.travelers ||
                      booking.metadata?.travelers;
                    if (!travelers) return null;

                    return (
                      <div className="space-y-6">
                        {/* Adults */}
                        {travelers.adults && travelers.adults.length > 0 && (
                          <div>
                            <h6 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="mr-2 text-[#285DA6]"
                              >
                                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                                <circle cx="12" cy="7" r="4"></circle>
                              </svg>
                              Adults ({travelers.adults.length})
                            </h6>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {travelers.adults.map((adult, index) => (
                                <div
                                  key={index}
                                  className="flex items-center p-3 bg-gray-50 rounded-xl"
                                >
                                  <div className="w-8 h-8 bg-[#285DA6]/10 rounded-full flex items-center justify-center text-[#285DA6] mr-3">
                                    <span className="text-sm font-medium">
                                      {index + 1}
                                    </span>
                                  </div>
                                  <div>
                                    <p className="font-medium text-gray-900">
                                      {adult.name}
                                    </p>
                                    {adult.age && (
                                      <p className="text-sm text-gray-500">
                                        Age: {adult.age}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Children */}
                        {travelers.children &&
                          travelers.children.length > 0 && (
                            <div>
                              <h6 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="mr-2 text-[#285DA6]"
                                >
                                  <circle cx="12" cy="8" r="5"></circle>
                                  <path d="M20 21a8 8 0 1 0-16 0"></path>
                                </svg>
                                Children ({travelers.children.length})
                              </h6>
                              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                                {travelers.children.map((child, index) => (
                                  <div
                                    key={index}
                                    className="flex items-center p-3 bg-blue-50 rounded-xl"
                                  >
                                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-blue-600 mr-3">
                                      <span className="text-sm font-medium">
                                        {index + 1}
                                      </span>
                                    </div>
                                    <div>
                                      <p className="font-medium text-gray-900">
                                        {child.name}
                                      </p>
                                      {child.age && (
                                        <p className="text-sm text-gray-500">
                                          Age: {child.age}
                                        </p>
                                      )}
                                    </div>
                                  </div>
                                ))}
                              </div>
                            </div>
                          )}

                        {/* Infants */}
                        {travelers.infants && travelers.infants.length > 0 && (
                          <div>
                            <h6 className="text-sm font-semibold text-gray-700 mb-3 flex items-center">
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                width="16"
                                height="16"
                                viewBox="0 0 24 24"
                                fill="none"
                                stroke="currentColor"
                                strokeWidth="2"
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                className="mr-2 text-[#285DA6]"
                              >
                                <circle cx="12" cy="12" r="3"></circle>
                                <path d="M12 1v6m0 6v6"></path>
                                <path d="M1 12h6m6 0h6"></path>
                              </svg>
                              Infants ({travelers.infants.length})
                            </h6>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                              {travelers.infants.map((infant, index) => (
                                <div
                                  key={index}
                                  className="flex items-center p-3 bg-pink-50 rounded-xl"
                                >
                                  <div className="w-8 h-8 bg-pink-100 rounded-full flex items-center justify-center text-pink-600 mr-3">
                                    <span className="text-sm font-medium">
                                      {index + 1}
                                    </span>
                                  </div>
                                  <div>
                                    <p className="font-medium text-gray-900">
                                      {infant.name}
                                    </p>
                                    {infant.age && (
                                      <p className="text-sm text-gray-500">
                                        Age: {infant.age}
                                      </p>
                                    )}
                                  </div>
                                </div>
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    );
                  })()}
                </div>
              </div>
            )}

            {/* Room & Stay Details */}
            {booking.metadata && (
              <div className="mb-8">
                <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#285DA6]"
                    >
                      <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"></path>
                      <polyline points="9 22 9 12 15 12 15 22"></polyline>
                    </svg>
                  </div>
                  Room & Stay Details
                </h5>
                <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {booking.metadata.meal_plan && (
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                            <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                            <line x1="6" y1="1" x2="6" y2="4"></line>
                            <line x1="10" y1="1" x2="10" y2="4"></line>
                            <line x1="14" y1="1" x2="14" y2="4"></line>
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wider">
                            Meal Plan
                          </p>
                          <p className="font-medium">
                            {getMealPlanLabel(booking.metadata.meal_plan)}
                          </p>
                        </div>
                      </div>
                    )}
                    {booking.metadata.number_of_rooms && (
                      <div className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="16"
                            height="16"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                            <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                          </svg>
                        </div>
                        <div>
                          <p className="text-xs text-gray-500 uppercase tracking-wider">
                            Number of Rooms
                          </p>
                          <p className="font-medium">
                            {booking.metadata.number_of_rooms}
                          </p>
                        </div>
                      </div>
                    )}
                    {booking.metadata.extra_beds &&
                      booking.metadata.extra_beds > 0 && (
                        <div className="flex items-center">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M2 4v16"></path>
                              <path d="M2 8h18a2 2 0 0 1 2 2v10"></path>
                              <path d="M2 17h20"></path>
                              <path d="M6 8V4a2 2 0 0 1 2-2h8a2 2 0 0 1 2 2v4"></path>
                            </svg>
                          </div>
                          <div>
                            <p className="text-xs text-gray-500 uppercase tracking-wider">
                              Extra Beds
                            </p>
                            <p className="font-medium">
                              {booking.metadata.extra_beds} bed
                              {booking.metadata.extra_beds > 1 ? "s" : ""}
                              {booking.metadata.extra_bed_total && (
                                <span className="text-sm text-gray-500 ml-2">
                                  (
                                  {formatCurrency(
                                    booking.metadata.extra_bed_total,
                                    booking.currency_code
                                  )}
                                  )
                                </span>
                              )}
                            </p>
                          </div>
                        </div>
                      )}
                    {booking.special_requests && (
                      <div className="md:col-span-2">
                        <div className="flex items-start">
                          <div className="w-8 h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center text-[#285DA6] mr-3 mt-1">
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="16"
                              height="16"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              <path d="M14 9V5a3 3 0 0 0-6 0v4"></path>
                              <rect
                                x="2"
                                y="9"
                                width="20"
                                height="11"
                                rx="2"
                                ry="2"
                              ></rect>
                            </svg>
                          </div>
                          <div className="flex-1">
                            <p className="text-xs text-gray-500 uppercase tracking-wider mb-1">
                              Special Requests
                            </p>
                            <p className="font-medium text-gray-800">
                              {booking.special_requests}
                            </p>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            )}

            {/* Add-ons & Services Details */}
            {booking.metadata?.add_ons &&
              booking.metadata.add_ons.length > 0 && (
                <div className="mb-8">
                  <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                    <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="18"
                        height="18"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#285DA6]"
                      >
                        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"></path>
                      </svg>
                    </div>
                    Add-ons & Services
                  </h5>
                  <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                    <div className="space-y-6">
                      {booking.metadata.add_ons
                        .sort((a, b) => {
                          const pricingTypeOrder = {
                            per_person: 1,
                            usage_based: 2,
                            package: 3,
                          };
                          const orderA =
                            pricingTypeOrder[
                              a.pricing_type as keyof typeof pricingTypeOrder
                            ] || 4;
                          const orderB =
                            pricingTypeOrder[
                              b.pricing_type as keyof typeof pricingTypeOrder
                            ] || 4;
                          return orderA - orderB;
                        })
                        .slice(0, showAllAddOns ? undefined : 2)
                        .map((addOn, index) => (
                          <div
                            key={index}
                            className="border-b border-gray-100 last:border-b-0 pb-6 last:pb-0"
                          >
                            <div className="flex items-start justify-between mb-4">
                              <div className="flex-1">
                                <h6 className="text-lg font-semibold text-gray-900 mb-2">
                                  {addOn.name}
                                </h6>
                                {addOn.description && (
                                  <div className="text-sm text-gray-600 mb-3">
                                    {(() => {
                                      const {
                                        text: descriptionText,
                                        needsReadMore,
                                      } = getTruncatedDescription(
                                        addOn.description
                                      );
                                      const identifier =
                                        addOn.id || addOn.service_id || addOn.name || "unknown";
                                      const isExpanded =
                                        expandedDescriptions.has(
                                          `booking-details-${identifier}`
                                        );

                                      return (
                                        <>
                                          <p className="leading-relaxed">
                                            {isExpanded
                                              ? addOn.description
                                              : descriptionText}
                                          </p>
                                          {needsReadMore && (
                                            <button
                                              onClick={(e) => {
                                                e.stopPropagation();
                                                e.preventDefault();
                                                toggleDescription(
                                                  addOn.id || addOn.service_id || "",
                                                  addOn.name
                                                );
                                              }}
                                              className="text-[#3566ab] hover:text-[#285DA6] text-sm font-medium mt-1 inline-block"
                                            >
                                              {isExpanded
                                                ? "Show less"
                                                : "Show more"}
                                            </button>
                                          )}
                                        </>
                                      );
                                    })()}
                                  </div>
                                )}
                                <div className="flex items-center space-x-4">
                                  <span className="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                    {getPricingTypeLabel(addOn.pricing_type)}
                                  </span>
                                </div>
                              </div>
                              <div className="text-right">
                                <p className="text-lg font-bold text-[#285DA6]">
                                  {formatCurrency(
                                    addOn.total_price || 0,
                                    addOn.currency_code || booking.currency_code
                                  )}
                                </p>
                              </div>
                            </div>

                            {/* Pricing Details - Using separate components based on pricing type */}
                            {addOn.pricing_type === "per_person" && (
                              <PerPersonPricingDisplay
                                addOn={addOn}
                                currencyCode={booking.currency_code}
                              />
                            )}

                            {addOn.pricing_type === "package" && (
                              <PackagePricingDisplay
                                addOn={addOn}
                                currencyCode={booking.currency_code}
                              />
                            )}

                            {addOn.pricing_type === "usage_based" && (
                              <UsageBasedPricingDisplay
                                addOn={addOn}
                                currencyCode={booking.currency_code}
                                booking={booking}
                              />
                            )}
                          </div>
                        ))}

                      {/* Show More/Show Less Button */}
                      {booking.metadata.add_ons.length > 2 && (
                        <div className="text-center mt-6">
                          <button
                            onClick={(e) => {
                              e.stopPropagation();
                              e.preventDefault();
                              toggleShowAllAddOns();
                            }}
                            className="inline-flex items-center px-6 py-3 border border-[#3566ab] text-[#3566ab] rounded-lg hover:bg-[#3566ab] hover:text-white transition-all duration-300 font-medium"
                          >
                            {showAllAddOns ? (
                              <>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="mr-2"
                                >
                                  <path d="m18 15-6-6-6 6" />
                                </svg>
                                Show Less Add-ons
                              </>
                            ) : (
                              <>
                                <svg
                                  xmlns="http://www.w3.org/2000/svg"
                                  width="16"
                                  height="16"
                                  viewBox="0 0 24 24"
                                  fill="none"
                                  stroke="currentColor"
                                  strokeWidth="2"
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  className="mr-2"
                                >
                                  <path d="m6 9 6 6 6-6" />
                                </svg>
                                Show More Add-ons (
                                {booking.metadata.add_ons.length - 2} more)
                              </>
                            )}
                          </button>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              )}

            {/* Pricing Breakdown */}
            {booking.metadata && (
              <div className="mb-8">
                <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                  <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#285DA6]"
                    >
                      <line x1="12" y1="1" x2="12" y2="23"></line>
                      <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                    </svg>
                  </div>
                  Pricing Breakdown
                </h5>
                <div className="bg-white p-6 rounded-2xl border border-gray-100 shadow-sm">
                  <div className="space-y-3">
                    {/* Room Cost */}
                    <div className="flex justify-between items-center py-2 border-b border-gray-100">
                      <span className="text-gray-600">Room Cost</span>
                      <span className="font-medium">
                        {formatCurrency(
                          (booking.metadata.total_amount ||
                            booking.total_amount) -
                            (booking.metadata.add_on_total_amount
                              ? booking.metadata.add_on_total_amount / 100
                              : 0) -
                            (booking.metadata.extra_bed_total || 0),
                          booking.currency_code
                        )}
                      </span>
                    </div>

                    {/* Add-ons Total */}
                    {booking.metadata.add_on_total_amount &&
                      booking.metadata.add_on_total_amount > 0 && (
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-gray-600">
                            Add-ons & Services
                          </span>
                          <span className="font-medium">
                            {formatCurrency(
                              booking.metadata.add_on_total_amount / 100,
                              booking.currency_code
                            )}
                          </span>
                        </div>
                      )}

                    {/* Extra Beds */}
                    {booking.metadata.extra_bed_total &&
                      booking.metadata.extra_bed_total > 0 && (
                        <div className="flex justify-between items-center py-2 border-b border-gray-100">
                          <span className="text-gray-600">Extra Beds</span>
                          <span className="font-medium">
                            {formatCurrency(
                              booking.metadata.extra_bed_total,
                              booking.currency_code
                            )}
                          </span>
                        </div>
                      )}

                    {/* Total */}
                    <div className="flex justify-between items-center py-3 bg-[#285DA6]/5 rounded-xl px-4 mt-4">
                      <span className="text-lg font-semibold text-gray-900">
                        Total Amount
                      </span>
                      <span className="text-xl font-bold text-[#285DA6]">
                        {formatCurrency(
                          booking.total_amount,
                          booking.currency_code
                        )}
                      </span>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Booking Information - Modernized */}
            <div className="bg-gray-50/50 p-6 rounded-2xl border border-gray-100">
              <h5 className="text-lg font-semibold text-gray-900 mb-4 flex items-center">
                <div className="w-8 h-8 bg-[#285DA6]/10 rounded-xl flex items-center justify-center mr-3">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#285DA6]"
                  >
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="3" y1="9" x2="21" y2="9"></line>
                    <line x1="9" y1="21" x2="9" y2="9"></line>
                  </svg>
                </div>
                Booking Information
              </h5>
              <div className="space-y-2 text-sm">
                <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2 border-b border-gray-100">
                  <span className="text-gray-500">Created</span>
                  <span className="font-medium text-gray-800">
                    {formatDisplayDateTime(booking.created_at)}
                  </span>
                </div>
                {booking.updated_at && (
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-500">Last Updated</span>
                    <span className="font-medium text-gray-800">
                      {formatDisplayDateTime(booking.updated_at)}
                    </span>
                  </div>
                )}
                {booking.payment_status && (
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2 border-b border-gray-100">
                    <span className="text-gray-500">Payment Status</span>
                    <span
                      className={`font-medium px-2 py-1 rounded-full text-xs ${
                        booking.payment_status === "captured" ||
                        booking.payment_status === "paid"
                          ? "bg-green-100 text-green-800"
                          : booking.payment_status === "pending"
                          ? "bg-yellow-100 text-yellow-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {booking.payment_status.charAt(0).toUpperCase() +
                        booking.payment_status.slice(1)}
                    </span>
                  </div>
                )}
                {booking.metadata?.payment_completed_at && (
                  <div className="flex flex-col md:flex-row md:items-center md:justify-between py-2">
                    <span className="text-gray-500">Payment Completed</span>
                    <span className="font-medium text-gray-800">
                      {formatDisplayDateTime(
                        booking.metadata.payment_completed_at
                      )}
                    </span>
                  </div>
                )}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
};

export default BookingDetailsModal;
