import React, { useState, useEffect } from "react";
import { Button } from "../ui/button";
import { downloadBookingInvoice } from "../../utils/store/bookings";
import { useUser } from "../../contexts/UserContext";
import type { ExtraBedDetails, GuestAssignment } from "../../utils/store/cart";

interface GuestDateSelection {
  guestId: string;
  guestName: string;
  guestType: "adult" | "child";
  selectedDates: string[];
}

interface BookingSummaryAddOn {
  service_id: string;
  name: string;
  pricing_type: "per_person" | "package" | "usage_based";
  adult_quantity: number;
  child_quantity: number;
  package_quantity: number;
  total_occupancy: number;
  total_price: number;
  adult_price?: number;
  child_price?: number;
  package_price?: number | null;
  per_day_adult_price?: number;
  per_day_child_price?: number;
  guest_date_selections?: GuestDateSelection[];
  selected_guests?: {
    adults: string[];
    children: string[];
  };
  quantities?: {
    adult_quantity: number;
    child_quantity: number;
    package_quantity: number;
  };
  service_type?: string;
}

interface BookingSummaryData {
  hotelId: string;
  roomId: string;
  checkInDate?: string;
  checkOutDate?: string;
  checkIn?: string; // Alternative field name
  checkOut?: string; // Alternative field name
  checkInTime: string;
  checkOutTime: string;
  nights: number;
  adults: number;
  guestCount: number;
  infantCount: number;
  childrenCount: number;
  children: number;
  infants: number;
  mealPlan: string;
  totalAmount: number;
  basePrice: number;
  taxesAndFees: number;
  currencyCode: string;
  selectedAddOns?: BookingSummaryAddOn[];
  addOnsTotal?: number;
  extraBedQuantity?: number;
  extraBedTotal?: number;
  extraBedDetails?: ExtraBedDetails;
  cotQuantity?: number;
  cotTotal?: number;
  cotDetails?: any;
  extraAdultQuantity?: number;
  extraAdultTotal?: number;
  extraAdultTotalPricePerAdult?: number;
  hotel: {
    name: string;
    location: string;
    imageUrl: string;
    check_in_time: string;
    check_out_time: string;
  };
  room: {
    name: string;
    images: string[];
    thumbnail: string;
    description: string;
    amenities: string[];
    size: string;
    bedType: string;
    maxAdults: number;
    maxChildren: number;
    maxInfants: number;
    mealPlanPrices: any;
  };
  guestDetails: {
    title: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    age?: number;
    specialRequests: string;
  };
  travelers: {
    adults: Array<{ name: string; age?: number }>;
    children: Array<{ name: string; age?: number }>;
    infants: Array<{ name: string; age?: number }>;
  };
  orderId: string;
  bookingReference: string;
  confirmedAt: string;
}

const BookingSummary: React.FC = () => {
  const { isAuthenticated } = useUser();
  const [summaryData, setSummaryData] = useState<BookingSummaryData | null>(
    null
  );
  const [hasData, setHasData] = useState(false);
  const [loading, setLoading] = useState(true);
  const [downloadingInvoice, setDownloadingInvoice] = useState(false);
  const [downloadError, setDownloadError] = useState<string | null>(null);
  const [expandedAddOns, setExpandedAddOns] = useState<Set<number>>(new Set());

  useEffect(() => {
    const loadSummaryData = () => {
      try {
        const storedData = localStorage.getItem("bookingSummaryData");
        if (storedData) {
          const data = JSON.parse(storedData);
          setSummaryData(data);
          setHasData(true);
        } else {
          setHasData(false);
        }
      } catch (error) {
        console.error("Error parsing booking summary data:", error);
        setHasData(false);
      } finally {
        setLoading(false);
      }
    };

    loadSummaryData();
  }, []);

  const handleReturnHome = () => {
    localStorage.removeItem("bookingSummaryData");
    window.location.href = "/";
  };

  const handleViewBookings = () => {
    localStorage.removeItem("bookingSummaryData");
    window.location.href = "/account";
  };

  const handleDownloadInvoice = async () => {
    if (!summaryData?.orderId) {
      setDownloadError("Booking ID not found");
      return;
    }

    setDownloadingInvoice(true);
    setDownloadError(null);

    try {
      await downloadBookingInvoice(summaryData.orderId);
    } catch (error: any) {
      console.error("Error downloading invoice:", error);
      setDownloadError(error.message || "Failed to download invoice");
    } finally {
      setDownloadingInvoice(false);
    }
  };

  const handleLoginRedirect = () => {
    window.location.href = "/login";
  };

  const toggleAddOnExpansion = (index: number) => {
    setExpandedAddOns((prev) => {
      const newSet = new Set(prev);
      if (newSet.has(index)) {
        newSet.delete(index);
      } else {
        newSet.add(index);
      }
      return newSet;
    });
  };

  const formatDateUserFriendly = (dateString: string, timeString?: string) => {
    const date = new Date(dateString);
    const dayName = date.toLocaleDateString("en-US", { weekday: "long" });
    const monthName = date.toLocaleDateString("en-US", { month: "long" });
    const day = date.getDate();
    const year = date.getFullYear();

    if (timeString) {
      // Convert 24-hour time to 12-hour format
      const [hours, minutes] = timeString.split(":");
      const hour = parseInt(hours);
      const ampm = hour >= 12 ? "PM" : "AM";
      const displayHour = hour % 12 || 12;
      return `${dayName}, ${monthName} ${day}, ${year} at ${displayHour}:${minutes} ${ampm}`;
    }

    return `${dayName}, ${monthName} ${day}, ${year}`;
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency.toUpperCase(),
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  const getMealPlanLabel = (mealPlan: string) => {
    try {
      // First check if we have meal plan prices with labels in the summary data
      if (summaryData?.room?.mealPlanPrices) {
        const mealPlanData = summaryData.room.mealPlanPrices[mealPlan];
        if (mealPlanData && typeof mealPlanData === "object" && mealPlanData.label) {
          return mealPlanData.label;
        }
      }

      // Fallback to hardcoded labels
      const fallbackLabels: Record<string, string> = {
        none: "No Meals",
        bb: "Bed & Breakfast",
        hb: "Half Board",
        fb: "Full Board",
      };

      return fallbackLabels[mealPlan] || mealPlan;
    } catch (error) {
      console.error("Error getting meal plan label:", error);
      return mealPlan;
    }
  };

  // Helper function to get guest names for per-person add-ons
  const getPerPersonGuestNames = (addOn: BookingSummaryAddOn) => {
    // If we have selected_guests data, use it directly (it's already in the correct format)
    if (addOn.selected_guests) {
      return {
        adults: addOn.selected_guests.adults || [],
        children: addOn.selected_guests.children || [],
      };
    }

    // Fallback: generate guest list from available travelers and quantities
    const selectedGuests = {
      adults: [] as string[],
      children: [] as string[],
    };

    // Get all available guests
    const allAdults = [
      // Include primary guest if they exist
      ...(summaryData?.guestDetails?.firstName
        ? [
            `${summaryData.guestDetails.firstName} ${
              summaryData.guestDetails.lastName || ""
            }`.trim(),
          ]
        : []),
      // Include other adult travelers
      ...(summaryData?.travelers?.adults?.map((adult) => adult.name) || []),
    ];

    const allChildren =
      summaryData?.travelers?.children?.map((child) => child.name) || [];

    // Select the first N guests based on quantities
    const adultQuantity =
      addOn.quantities?.adult_quantity || addOn.adult_quantity || 0;
    const childQuantity =
      addOn.quantities?.child_quantity || addOn.child_quantity || 0;

    selectedGuests.adults = allAdults.slice(0, adultQuantity);
    selectedGuests.children = allChildren.slice(0, childQuantity);

    return selectedGuests;
  };

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#285DA6] mx-auto mb-4"></div>
          <p className="text-gray-600">Loading your booking summary...</p>
        </div>
      </div>
    );
  }

  if (!hasData || !summaryData) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="max-w-md mx-auto text-center p-8">
          <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-8 w-8 text-green-600"
              fill="none"
              viewBox="0 0 24 24"
              stroke="currentColor"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M5 13l4 4L19 7"
              />
            </svg>
          </div>
          <h1 className="font-baskervville text-3xl mb-4">
            Booking Confirmed!
          </h1>
          <p className="text-gray-600 mb-6">
            Your booking has been confirmed. A confirmation email has been sent
            to you.
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button
              onClick={handleReturnHome}
              className="bg-[#285DA6] hover:bg-[#285DA6]/90"
            >
              Return to Home
            </Button>
            <Button
              onClick={handleViewBookings}
              variant="outline"
              className="border-[#285DA6] text-[#285DA6]"
            >
              View My Bookings
            </Button>
          </div>
        </div>
      </div>
    );
  }

  const totalGuests =
    (summaryData?.guestCount || 0) +
    (summaryData?.childrenCount || 0) +
    (summaryData?.infantCount || 0);
  const allTravelers = [
    ...(summaryData.travelers?.adults || []),
    ...(summaryData.travelers?.children || []),
    ...(summaryData.travelers?.infants || []),
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50/30 to-indigo-50/20">
      <div className="container-custom py-6 md:py-12">
        {/* Stylish Header */}
        <div className="text-center mb-8 md:mb-16 relative px-4">
          {/* Background decoration */}
          <div className="absolute inset-0 -top-8 bg-gradient-to-r from-transparent via-blue-50/50 to-transparent rounded-3xl"></div>

          <div className="relative">
            <div className="w-16 h-16 md:w-20 md:h-20 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-2xl flex items-center justify-center mx-auto mb-6 md:mb-8 shadow-xl shadow-blue-500/25 transform rotate-3 hover:rotate-0 transition-transform duration-300">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                className="h-8 w-8 md:h-10 md:w-10 text-white"
                fill="none"
                viewBox="0 0 24 24"
                stroke="currentColor"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2.5}
                  d="M5 13l4 4L19 7"
                />
              </svg>
            </div>

            <h1 className="font-baskervville text-2xl md:text-4xl mb-4 md:mb-5 bg-gradient-to-r from-gray-800 via-[#285DA6] to-gray-800 bg-clip-text text-transparent px-4">
              Booking Confirmed!
            </h1>
            <p className="text-base md:text-lg text-gray-600 mb-6 md:mb-7 max-w-2xl mx-auto leading-relaxed px-4">
              Thank you for choosing Perfect Piste. We've sent a confirmation
              email with all the details.
            </p>

            <div className="inline-flex flex-col sm:flex-row items-center gap-2 sm:gap-4 bg-white/80 backdrop-blur-sm px-4 sm:px-6 py-3 rounded-2xl border border-white/60 shadow-xl shadow-blue-500/10 mx-4">
              <div className="flex items-center gap-2">
                <div className="w-2 h-2 bg-[#285DA6] rounded-full animate-pulse"></div>
                <span className="text-sm font-medium text-gray-600">
                  Booking Reference
                </span>
              </div>
              <div className="hidden sm:block w-px h-6 bg-gray-300"></div>
              <span className="text-xs md:text-base sm:text-lg font-bold bg-gradient-to-r from-[#285DA6] to-blue-500 bg-clip-text text-transparent">
                {summaryData.orderId || "N/A"}
              </span>
            </div>
          </div>
        </div>

        <div className="max-w-6xl mx-auto space-y-4 md:space-y-8">
          {/* Stylish Combined Box */}
          <div className="bg-white/90 backdrop-blur-sm border border-white/60 rounded-2xl shadow-xl shadow-blue-500/10 overflow-hidden">
            {/* Accommodation Details */}
            <div className="p-4 md:p-8 relative">
              <div className="absolute top-0 left-0 w-full h-1 bg-gradient-to-r from-[#285DA6] via-blue-500 to-indigo-500"></div>

              <div className="flex flex-col md:flex-row items-start gap-4 md:gap-8">
                <div className="relative group w-full md:w-auto flex justify-center md:block">
                  <img
                    src={
                      summaryData.hotel?.imageUrl ||
                      "/images/room-placeholder.jpg"
                    }
                    alt={summaryData.hotel?.name || "Hotel"}
                    className="w-32 h-32 md:w-40 md:h-40 object-cover rounded-2xl shadow-lg group-hover:shadow-xl transition-shadow duration-300"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent rounded-2xl opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                </div>
                <div className="flex-1 w-full">
                  <h3 className="text-lg md:text-xl font-bold text-gray-900 mb-2 md:mb-3 text-center md:text-left">
                    {summaryData.hotel?.name || "Hotel Name"}
                  </h3>
                  <p className="text-gray-600 mb-4 md:mb-6 flex items-center justify-center md:justify-start">
                    <svg
                      className="w-4 h-4 md:w-5 md:h-5 mr-2 md:mr-3 text-[#285DA6]"
                      fill="currentColor"
                      viewBox="0 0 20 20"
                    >
                      <path
                        fillRule="evenodd"
                        d="M5.05 4.05a7 7 0 119.9 9.9L10 18.9l-4.95-4.95a7 7 0 010-9.9zM10 11a2 2 0 100-4 2 2 0 000 4z"
                        clipRule="evenodd"
                      />
                    </svg>
                    {summaryData.hotel?.location || "Location"}
                  </p>
                  <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-3 md:gap-6">
                    <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-3 md:p-4 rounded-xl border border-[#285DA6]/20">
                      <span className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider block mb-1">
                        Room
                      </span>
                      <p className="font-bold text-gray-900 text-sm md:text-base">
                        {summaryData.room?.name || "Room Name"}
                      </p>
                    </div>
                    {summaryData.room?.size && (
                      <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-3 md:p-4 rounded-xl border border-[#285DA6]/20">
                        <span className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider block mb-1">
                          Size
                        </span>
                        <p className="font-bold text-gray-900 text-sm md:text-base">
                          {summaryData.room.size} sq ft
                        </p>
                      </div>
                    )}
                    {summaryData.room?.bedType && (
                      <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-3 md:p-4 rounded-xl border border-[#285DA6]/20">
                        <span className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider block mb-1">
                          Bed
                        </span>
                        <p className="font-bold text-gray-900 capitalize text-sm md:text-base">
                          {summaryData.room.bedType}
                        </p>
                      </div>
                    )}
                    <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-3 md:p-4 rounded-xl border border-[#285DA6]/20">
                      <span className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider block mb-1">
                        Meal Plan
                      </span>
                      <p className="font-bold text-gray-900 text-sm md:text-base">
                        {getMealPlanLabel(summaryData.mealPlan || "none")}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Stylish Separator */}
            <div className="relative">
              <div className="absolute inset-0 bg-gradient-to-r from-transparent via-gray-200 to-transparent"></div>
              <div className="border-t border-gray-200"></div>
            </div>

            {/* Stay Details */}
            <div className="p-4 md:p-8">
              <div className="flex items-center gap-3 mb-4 md:mb-6">
                <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                  Important Details
                </h2>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4 md:gap-6">
                <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-6 rounded-2xl border border-[#285DA6]/20 text-center group hover:shadow-lg transition-shadow duration-300">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg
                      className="w-5 h-5 md:w-6 md:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <p className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider mb-2">
                    Check-in
                  </p>
                  <p className="font-bold text-gray-900 text-sm md:text-base leading-tight">
                    {summaryData.checkInDate || summaryData.checkIn
                      ? formatDateUserFriendly(
                          (summaryData.checkInDate || summaryData.checkIn)!,
                          summaryData.checkInTime || "14:00"
                        )
                      : "TBD"}
                  </p>
                </div>

                <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-6 rounded-2xl border border-[#285DA6]/20 text-center group hover:shadow-lg transition-shadow duration-300">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg
                      className="w-5 h-5 md:w-6 md:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                      />
                    </svg>
                  </div>
                  <p className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider mb-2">
                    Check-out
                  </p>
                  <p className="font-bold text-gray-900 text-sm md:text-base leading-tight">
                    {summaryData.checkOutDate || summaryData.checkOut
                      ? formatDateUserFriendly(
                          (summaryData.checkOutDate || summaryData.checkOut)!,
                          summaryData.checkOutTime || "11:00"
                        )
                      : "TBD"}
                  </p>
                </div>

                <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-6 rounded-2xl border border-[#285DA6]/20 text-center group hover:shadow-lg transition-shadow duration-300">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg
                      className="w-5 h-5 md:w-6 md:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 3v1m0 16v1m9-9h-1M4 12H3m15.364 6.364l-.707-.707M6.343 6.343l-.707-.707m12.728 0l-.707.707M6.343 17.657l-.707.707M16 12a4 4 0 11-8 0 4 4 0 018 0z"
                      />
                    </svg>
                  </div>
                  <p className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider mb-2">
                    Duration
                  </p>
                  <p className="font-bold text-gray-900 text-sm md:text-base">
                    {summaryData.nights || 1} night
                    {(summaryData.nights || 1) > 1 ? "s" : ""}
                  </p>
                </div>

                <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-6 rounded-2xl border border-[#285DA6]/20 text-center group hover:shadow-lg transition-shadow duration-300">
                  <div className="w-10 h-10 md:w-12 md:h-12 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-xl flex items-center justify-center mx-auto mb-3 md:mb-4 group-hover:scale-110 transition-transform duration-300">
                    <svg
                      className="w-5 h-5 md:w-6 md:h-6 text-white"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                      />
                    </svg>
                  </div>
                  <p className="text-xs text-[#285DA6] font-semibold uppercase tracking-wider mb-2">
                    Guests
                  </p>
                  <p className="font-bold text-gray-900 text-sm md:text-base">
                    {totalGuests} guest{totalGuests > 1 ? "s" : ""}
                  </p>
                </div>
              </div>
            </div>

            {/* Separator */}
            <div className="border-t border-gray-200"></div>

            {/* Guest Details */}
            <div className="p-4 md:p-6">
              <div className="flex items-center gap-3 mb-4 md:mb-6">
                <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                  Guest Details
                </h2>
              </div>

              {/* Lead Guest */}
              <div className="flex items-start gap-3 md:gap-4 mb-4 md:mb-6">
                <div className="w-10 h-10 bg-[#285DA6] rounded-full flex items-center justify-center flex-shrink-0">
                  <span className="text-white font-medium text-sm">
                    {(summaryData.guestDetails?.firstName || "G")
                      .charAt(0)
                      .toUpperCase()}
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <div className="flex items-center gap-2 mb-2">
                    <h3 className="font-semibold text-gray-900 text-sm md:text-base">
                      {summaryData.guestDetails?.firstName || "Guest"}{" "}
                      {summaryData.guestDetails?.lastName || ""}
                    </h3>
                    {summaryData.guestDetails?.age && (
                      <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-[#285DA6]/10 text-[#285DA6] border border-[#285DA6]/20">
                        Age {summaryData.guestDetails.age}
                      </span>
                    )}
                  </div>
                  <div className="space-y-1 text-xs md:text-sm text-gray-600">
                    <p className="flex items-center">
                      <svg
                        className="w-3 h-3 md:w-4 md:h-4 mr-2 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 8l7.89 4.26a2 2 0 002.22 0L21 8M5 19h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"
                        />
                      </svg>
                      <span className="truncate">
                        {summaryData.guestDetails?.email || "No email provided"}
                      </span>
                    </p>
                    <p className="flex items-center">
                      <svg
                        className="w-3 h-3 md:w-4 md:h-4 mr-2 flex-shrink-0"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M3 5a2 2 0 012-2h3.28a1 1 0 01.948.684l1.498 4.493a1 1 0 01-.502 1.21l-2.257 1.13a11.042 11.042 0 005.516 5.516l1.13-2.257a1 1 0 011.21-.502l4.493 1.498a1 1 0 01.684.949V19a2 2 0 01-2 2h-1C9.716 21 3 14.284 3 6V5z"
                        />
                      </svg>
                      {summaryData.guestDetails?.phone || "No phone provided"}
                    </p>
                  </div>
                </div>
              </div>

              {/* Other Travelers */}
              {allTravelers.length > 0 && (
                <div className="mb-4 md:mb-6">
                  <h3 className="font-medium mb-3 text-gray-900 text-xs md:text-sm">
                    Other Travelers
                  </h3>
                  <div className="space-y-2">
                    {(summaryData.travelers?.adults || []).map(
                      (adult, index) => (
                        <div
                          key={`adult-${index}`}
                          className="flex items-center justify-between p-2 md:p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="min-w-0 flex-1">
                            <span className="font-medium text-gray-900 text-xs md:text-sm block truncate">
                              {adult.name}
                            </span>
                            {adult.age && (
                              <span className="text-xs text-gray-600">
                                (Age: {adult.age})
                              </span>
                            )}
                          </div>
                          <span className="text-xs bg-[#285DA6] text-white px-2 py-1 rounded-full ml-2 flex-shrink-0">
                            Adult
                          </span>
                        </div>
                      )
                    )}
                    {(summaryData.travelers?.children || []).map(
                      (child, index) => (
                        <div
                          key={`child-${index}`}
                          className="flex items-center justify-between p-2 md:p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="min-w-0 flex-1">
                            <span className="font-medium text-gray-900 text-xs md:text-sm block truncate">
                              {child.name}
                            </span>
                            {child.age && (
                              <span className="text-xs text-gray-600">
                                (Age: {child.age})
                              </span>
                            )}
                          </div>
                          <span className="text-xs bg-[#285DA6] text-white px-2 py-1 rounded-full ml-2 flex-shrink-0">
                            Child
                          </span>
                        </div>
                      )
                    )}
                    {(summaryData.travelers?.infants || []).map(
                      (infant, index) => (
                        <div
                          key={`infant-${index}`}
                          className="flex items-center justify-between p-2 md:p-3 bg-gray-50 rounded-lg"
                        >
                          <div className="min-w-0 flex-1">
                            <span className="font-medium text-gray-900 text-xs md:text-sm block truncate">
                              {infant.name}
                            </span>
                            {infant.age && (
                              <span className="text-xs text-gray-600">
                                (Age: {infant.age})
                              </span>
                            )}
                          </div>
                          <span className="text-xs bg-[#285DA6] text-white px-2 py-1 rounded-full ml-2 flex-shrink-0">
                            Infant
                          </span>
                        </div>
                      )
                    )}
                  </div>
                </div>
              )}

              {/* Special Requests */}
              {summaryData.guestDetails?.specialRequests && (
                <div>
                  <h3 className="font-medium mb-3 text-gray-900 text-xs md:text-sm">
                    Special Requests
                  </h3>
                  <div className="p-3 md:p-4 bg-gray-50 rounded-lg">
                    <p className="text-gray-700 text-xs md:text-sm leading-relaxed">
                      {summaryData.guestDetails.specialRequests}
                    </p>
                  </div>
                </div>
              )}
            </div>

            {/* Add-ons Section */}
            {summaryData.selectedAddOns &&
              summaryData.selectedAddOns.length > 0 && (
                <>
                  {/* Separator */}
                  <div className="border-t border-gray-200"></div>

                  <div className="p-4 md:p-6">
                    <div className="flex items-center gap-3 mb-4 md:mb-6">
                      <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-lg flex items-center justify-center">
                        <svg
                          className="w-4 h-4 md:w-5 md:h-5 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <circle cx="12" cy="12" r="3"></circle>
                          <path d="M12 1v6m0 6v6"></path>
                          <path d="m21 12-6 0m-6 0-6 0"></path>
                        </svg>
                      </div>
                      <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                        Add-ons & Services
                      </h2>
                    </div>

                    <div className="space-y-3 md:space-y-4">
                      {summaryData.selectedAddOns
                        .sort((a, b) => {
                          const pricingTypeOrder = {
                            per_person: 1,
                            usage_based: 2,
                            package: 3,
                          };
                          const orderA =
                            pricingTypeOrder[
                              a.pricing_type as keyof typeof pricingTypeOrder
                            ] || 4;
                          const orderB =
                            pricingTypeOrder[
                              b.pricing_type as keyof typeof pricingTypeOrder
                            ] || 4;
                          return orderA - orderB;
                        })
                        .map((addOn, index) => {
                          // Calculate display quantities based on pricing type
                          const isPackagePricing =
                            addOn.pricing_type === "package";
                          const isUsageBasedPricing =
                            addOn.pricing_type === "usage_based";

                          const displayQuantity = isPackagePricing
                            ? addOn.quantities?.package_quantity ||
                              addOn.package_quantity ||
                              0
                            : (addOn.quantities?.adult_quantity ||
                                addOn.adult_quantity ||
                                0) +
                              (addOn.quantities?.child_quantity ||
                                addOn.child_quantity ||
                                0);

                          return (
                            <div
                              key={`addon-${index}`}
                              className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-5 rounded-xl border border-[#285DA6]/20 hover:shadow-lg transition-shadow duration-300"
                            >
                              <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                                <div className="flex-1">
                                  <div className="flex items-center gap-2 mb-2">
                                    <h3 className="font-semibold text-gray-900 text-sm md:text-base">
                                      {addOn.name}
                                    </h3>
                                    <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                      {addOn.pricing_type === "per_person"
                                        ? "Per Person"
                                        : addOn.pricing_type === "usage_based"
                                        ? "Usage Based"
                                        : "Package"}
                                    </span>
                                  </div>

                                  <div className="flex flex-wrap gap-3 text-xs md:text-sm text-gray-600">
                                    {isPackagePricing ? (
                                      /* Package Pricing Display */
                                      <div className="flex items-center gap-1">
                                        <svg
                                          className="w-3 h-3 md:w-4 md:h-4 text-purple-600"
                                          fill="none"
                                          stroke="currentColor"
                                          viewBox="0 0 24 24"
                                        >
                                          <rect
                                            x="3"
                                            y="3"
                                            width="18"
                                            height="18"
                                            rx="2"
                                            ry="2"
                                          ></rect>
                                          <path d="M9 9h6v6H9z"></path>
                                        </svg>
                                        <span>
                                          Package included
                                          {addOn.package_price && (
                                            <span className="text-gray-500 ml-1">
                                              (
                                              {formatCurrency(
                                                addOn.package_price,
                                                summaryData.currencyCode
                                              )}
                                              )
                                            </span>
                                          )}
                                        </span>
                                      </div>
                                    ) : isUsageBasedPricing ? (
                                      /* Usage-Based Pricing Display */
                                      <div className="space-y-2">
                                        {addOn.guest_date_selections &&
                                        addOn.guest_date_selections.length >
                                          0 ? (
                                          <div>
                                            {/* Summary line with toggle */}
                                            <div className="flex items-center justify-between">
                                              <div className="flex items-center gap-1">
                                                <svg
                                                  className="w-3 h-3 md:w-4 md:h-4 text-orange-600"
                                                  fill="none"
                                                  stroke="currentColor"
                                                  viewBox="0 0 24 24"
                                                >
                                                  <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                                  />
                                                </svg>
                                                <span>
                                                  {
                                                    addOn.guest_date_selections.filter(
                                                      (guest) =>
                                                        guest.selectedDates
                                                          .length > 0
                                                    ).length
                                                  }{" "}
                                                  guest
                                                  {addOn.guest_date_selections.filter(
                                                    (guest) =>
                                                      guest.selectedDates
                                                        .length > 0
                                                  ).length !== 1
                                                    ? "s"
                                                    : ""}{" "}
                                                  with bookings
                                                </span>
                                              </div>
                                              <button
                                                onClick={() =>
                                                  toggleAddOnExpansion(index)
                                                }
                                                className="flex items-center gap-1 ml-2 text-[#285DA6] hover:text-[#285DA6]/80 text-xs font-medium transition-colors"
                                              >
                                                {/* {expandedAddOns.has(index)
                                                ? "Hide details"
                                                : "Show details"} */}
                                                <svg
                                                  className={`w-3 h-3 transition-transform ${
                                                    expandedAddOns.has(index)
                                                      ? "rotate-180"
                                                      : ""
                                                  }`}
                                                  fill="none"
                                                  stroke="currentColor"
                                                  viewBox="0 0 24 24"
                                                >
                                                  <path
                                                    strokeLinecap="round"
                                                    strokeLinejoin="round"
                                                    strokeWidth={2}
                                                    d="M19 9l-7 7-7-7"
                                                  />
                                                </svg>
                                              </button>
                                            </div>

                                            {/* Collapsible guest details */}
                                            {expandedAddOns.has(index) && (
                                              <div className="mt-3 space-y-2 pl-4 border-l-2 border-gray-200">
                                                {addOn.guest_date_selections
                                                  .filter(
                                                    (guest) =>
                                                      guest.selectedDates
                                                        .length > 0
                                                  )
                                                  .map((guest, guestIndex) => (
                                                    <div
                                                      key={guestIndex}
                                                      className="flex items-start gap-2"
                                                    >
                                                      <svg
                                                        className={`w-3 h-3 md:w-4 md:h-4 mt-0.5 flex-shrink-0 ${
                                                          guest.guestType ===
                                                          "adult"
                                                            ? "text-blue-600"
                                                            : "text-green-600"
                                                        }`}
                                                        fill="none"
                                                        stroke="currentColor"
                                                        viewBox="0 0 24 24"
                                                      >
                                                        <path
                                                          strokeLinecap="round"
                                                          strokeLinejoin="round"
                                                          strokeWidth={2}
                                                          d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"
                                                        />
                                                        <circle
                                                          cx="9"
                                                          cy="7"
                                                          r="4"
                                                        />
                                                      </svg>
                                                      <div className="flex flex-col min-w-0 flex-1">
                                                        <span className="font-medium text-sm">
                                                          {guest.guestName}
                                                          {(guest.guestType ===
                                                          "adult"
                                                            ? addOn.per_day_adult_price
                                                            : addOn.per_day_child_price) && (
                                                            <span className="text-gray-500 ml-1 font-normal">
                                                              (
                                                              {formatCurrency(
                                                                guest.guestType ===
                                                                  "adult"
                                                                  ? addOn.per_day_adult_price!
                                                                  : addOn.per_day_child_price!,
                                                                summaryData.currencyCode
                                                              )}
                                                              /day)
                                                            </span>
                                                          )}
                                                        </span>
                                                        <span className="text-xs text-gray-500 mt-1">
                                                          {guest.selectedDates
                                                            .sort()
                                                            .map((date) => {
                                                              try {
                                                                const dateObj =
                                                                  new Date(
                                                                    date
                                                                  );
                                                                return dateObj.toLocaleDateString(
                                                                  "en-US",
                                                                  {
                                                                    month:
                                                                      "short",
                                                                    day: "numeric",
                                                                  }
                                                                );
                                                              } catch {
                                                                return date;
                                                              }
                                                            })
                                                            .join(", ")}
                                                        </span>
                                                      </div>
                                                    </div>
                                                  ))}
                                              </div>
                                            )}
                                          </div>
                                        ) : (
                                          <div className="flex items-center gap-1">
                                            <svg
                                              className="w-3 h-3 md:w-4 md:h-4 text-orange-600"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"
                                              />
                                            </svg>
                                            <span>
                                              {displayQuantity} day selection
                                              {displayQuantity !== 1
                                                ? "s"
                                                : ""}{" "}
                                              (dates not available)
                                            </span>
                                          </div>
                                        )}
                                      </div>
                                    ) : (
                                      /* Per-Person Pricing Display */
                                      <div className="space-y-2">
                                        {/* Summary line with toggle */}
                                        <div className="flex items-center justify-between">
                                          <div className="flex items-center gap-1">
                                            <svg
                                              className="w-3 h-3 md:w-4 md:h-4 text-purple-600"
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"
                                              />
                                            </svg>
                                            <span>
                                              {displayQuantity} guest
                                              {displayQuantity !== 1
                                                ? "s"
                                                : ""}{" "}
                                              selected
                                            </span>
                                          </div>
                                          <button
                                            onClick={() =>
                                              toggleAddOnExpansion(index)
                                            }
                                            className="flex items-center gap-1 ml-2 text-[#285DA6] hover:text-[#285DA6]/80 text-xs font-medium transition-colors"
                                          >
                                            <svg
                                              className={`w-3 h-3 transition-transform ${
                                                expandedAddOns.has(index)
                                                  ? "rotate-180"
                                                  : ""
                                              }`}
                                              fill="none"
                                              stroke="currentColor"
                                              viewBox="0 0 24 24"
                                            >
                                              <path
                                                strokeLinecap="round"
                                                strokeLinejoin="round"
                                                strokeWidth={2}
                                                d="M19 9l-7 7-7-7"
                                              />
                                            </svg>
                                          </button>
                                        </div>

                                        {/* Collapsible guest details */}
                                        {expandedAddOns.has(index) && (
                                          <div className="mt-3 space-y-2 pl-4 border-l-2 border-gray-200">
                                            {(() => {
                                              const selectedGuests =
                                                getPerPersonGuestNames(addOn);

                                              return (
                                                <>
                                                  {/* Adults */}
                                                  {selectedGuests.adults
                                                    .length > 0 && (
                                                    <div>
                                                      <div className="flex items-center mb-2">
                                                        <div className="space-y-1">
                                                          {selectedGuests.adults.map(
                                                            (
                                                              adultName,
                                                              adultIndex
                                                            ) => (
                                                              <div
                                                                key={adultIndex}
                                                                className="flex items-center"
                                                              >
                                                                <svg
                                                                  className={`w-3 h-3 md:w-4 md:h-4 mt-0.5 flex-shrink-0 text-blue-600`}
                                                                  fill="none"
                                                                  stroke="currentColor"
                                                                  viewBox="0 0 24 24"
                                                                >
                                                                  <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={
                                                                      2
                                                                    }
                                                                    d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"
                                                                  />
                                                                  <circle
                                                                    cx="9"
                                                                    cy="7"
                                                                    r="4"
                                                                  />
                                                                </svg>
                                                                <div className="flex items-center gap-2">
                                                                  <span className="text-sm text-gray-700 ml-2">
                                                                    {adultName}
                                                                  </span>
                                                                </div>
                                                                {addOn.adult_price && (
                                                                  <span className="text-xs text-gray-600">
                                                                    {formatCurrency(
                                                                      addOn.adult_price,
                                                                      summaryData.currencyCode
                                                                    )}
                                                                  </span>
                                                                )}
                                                              </div>
                                                            )
                                                          )}
                                                        </div>
                                                      </div>
                                                    </div>
                                                  )}

                                                  {/* Children */}
                                                  {selectedGuests.children
                                                    .length > 0 && (
                                                    <div
                                                      className={
                                                        selectedGuests.adults
                                                          .length > 0
                                                          ? "mt-3"
                                                          : ""
                                                      }
                                                    >
                                                      <div className="flex items-center mb-2">
                                                        <div className="space-y-1">
                                                          {selectedGuests.children.map(
                                                            (
                                                              childName,
                                                              childIndex
                                                            ) => (
                                                              <div
                                                                key={childIndex}
                                                                className="flex items-center"
                                                              >
                                                                <svg
                                                                  className="w-3 h-3 md:w-4 md:h-4 flex-shrink-0 text-green-600"
                                                                  fill="none"
                                                                  stroke="currentColor"
                                                                  viewBox="0 0 24 24"
                                                                >
                                                                  <path
                                                                    strokeLinecap="round"
                                                                    strokeLinejoin="round"
                                                                    strokeWidth={
                                                                      2
                                                                    }
                                                                    d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"
                                                                  />
                                                                  <circle
                                                                    cx="9"
                                                                    cy="7"
                                                                    r="4"
                                                                  />
                                                                </svg>
                                                                <div className="flex items-center gap-2">
                                                                  <span className="text-sm text-gray-700 ml-2">
                                                                    {childName}
                                                                  </span>
                                                                </div>
                                                                {addOn.child_price && (
                                                                  <span className="text-xs text-gray-600">
                                                                    {formatCurrency(
                                                                      addOn.child_price,
                                                                      summaryData.currencyCode
                                                                    )}
                                                                  </span>
                                                                )}
                                                              </div>
                                                            )
                                                          )}
                                                        </div>
                                                      </div>
                                                    </div>
                                                  )}
                                                </>
                                              );
                                            })()}
                                          </div>
                                        )}
                                      </div>
                                    )}
                                  </div>
                                </div>

                                <div className="flex flex-col items-end gap-1">
                                  <div className="text-lg md:text-xl font-bold text-[#285DA6]">
                                    {formatCurrency(
                                      addOn.total_price,
                                      summaryData.currencyCode
                                    )}
                                  </div>
                                </div>
                              </div>
                            </div>
                          );
                        })}
                    </div>
                  </div>
                </>
              )}

            {/* Extra Beds Section */}
            {summaryData.extraBedQuantity
              ? summaryData.extraBedQuantity > 0 && (
                  <>
                    <div className="border-t border-gray-200 py-6 md:py-8 px-6">
                      <div className="flex items-center gap-3 mb-4 md:mb-6">
                        <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-lg flex items-center justify-center">
                          <svg
                            className="w-4 h-4 md:w-5 md:h-5 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <rect
                              x="2"
                              y="3"
                              width="20"
                              height="14"
                              rx="2"
                              ry="2"
                            ></rect>
                            <line x1="8" y1="21" x2="16" y2="21"></line>
                            <line x1="12" y1="17" x2="12" y2="21"></line>
                          </svg>
                        </div>
                        <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                          Extra Beds
                        </h2>
                      </div>

                      <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-5 rounded-xl border border-[#285DA6]/20 hover:shadow-lg transition-shadow duration-300">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-gray-900 text-sm md:text-base">
                                Extra Beds
                              </h3>
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {summaryData.extraBedQuantity} bed
                                {summaryData.extraBedQuantity > 1 ? "s" : ""}
                              </span>
                            </div>

                            <div className="flex flex-wrap gap-3 text-xs md:text-sm text-gray-600">
                              <div className="space-y-1">
                                <div className="flex items-center gap-1">
                                  <svg
                                    className="w-3 h-3 md:w-4 md:h-4 text-blue-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  <span>
                                    Extra bed total price:{" "}
                                    {formatCurrency(
                                      summaryData.extraBedDetails
                                        ?.total_price ||
                                        (summaryData.extraBedTotal || 0) /
                                          summaryData.extraBedQuantity,
                                      summaryData.currencyCode
                                    )}
                                  </span>
                                </div>
                                {/* <div className="flex items-center gap-1 ml-5">
                                <span>Nights: x {summaryData.nights}</span>
                              </div> */}
                                <div className="flex items-center gap-1 ml-5">
                                  <span>
                                    No of beds: {summaryData.extraBedQuantity}
                                  </span>
                                </div>

                                {/* Guest Assignments */}
                                {summaryData.extraBedDetails
                                  ?.guest_assignments &&
                                  summaryData.extraBedDetails.guest_assignments
                                    .length > 0 && (
                                    <div className="mt-3 pt-3 border-t border-gray-200">
                                      <div className="text-xs font-medium text-gray-700 mb-2">
                                        Guest Assignments:
                                      </div>
                                      <div className="space-y-1">
                                        {summaryData.extraBedDetails.guest_assignments.map(
                                          (assignment, index) => (
                                            <div
                                              key={index}
                                              className="flex items-center gap-2 text-xs"
                                            >
                                              <svg
                                                className="w-3 h-3 text-green-600"
                                                fill="none"
                                                stroke="currentColor"
                                                viewBox="0 0 24 24"
                                              >
                                                <rect
                                                  x="2"
                                                  y="3"
                                                  width="20"
                                                  height="14"
                                                  rx="2"
                                                  ry="2"
                                                ></rect>
                                                <line
                                                  x1="8"
                                                  y1="21"
                                                  x2="16"
                                                  y2="21"
                                                ></line>
                                                <line
                                                  x1="12"
                                                  y1="17"
                                                  x2="12"
                                                  y2="21"
                                                ></line>
                                              </svg>
                                              <span>
                                                Bed {assignment.bed_number}:{" "}
                                                {assignment.guest_name}
                                                {assignment.guest_age &&
                                                  ` (${assignment.guest_age} years)`}
                                              </span>
                                            </div>
                                          )
                                        )}
                                      </div>
                                    </div>
                                  )}
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-col items-end gap-1">
                            <div className="text-lg md:text-xl font-bold text-[#285DA6]">
                              {formatCurrency(
                                summaryData.extraBedTotal || 0,
                                summaryData.currencyCode
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )
              : null}

            {/* Cots Section */}
            {summaryData.cotQuantity
              ? summaryData.cotQuantity > 0 && (
                  <>
                    <div className="border-t border-gray-200 py-6 md:py-8 px-6">
                      <div className="flex items-center gap-3 mb-4 md:mb-6">
                        <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-lg flex items-center justify-center">
                          <svg
                            className="w-4 h-4 md:w-5 md:h-5 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <rect
                              x="3"
                              y="6"
                              width="18"
                              height="10"
                              rx="2"
                              ry="2"
                            ></rect>
                            <circle cx="12" cy="11" r="2"></circle>
                            <path d="M7 6V4a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2"></path>
                          </svg>
                        </div>
                        <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                          Baby Cots
                        </h2>
                      </div>

                      <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 p-4 md:p-5 rounded-xl border border-[#285DA6]/20 hover:shadow-lg transition-shadow duration-300">
                        <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                          <div className="flex-1">
                            <div className="flex items-center gap-2 mb-2">
                              <h3 className="font-semibold text-gray-900 text-sm md:text-base">
                                Baby Cots
                              </h3>
                              <span className="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800">
                                {summaryData.cotQuantity} cot
                                {summaryData.cotQuantity > 1 ? "s" : ""}
                              </span>
                            </div>

                            <div className="flex flex-wrap gap-3 text-xs md:text-sm text-gray-600">
                              <div className="space-y-1">
                                <div className="flex items-center gap-1">
                                  <svg
                                    className="w-3 h-3 md:w-4 md:h-4 text-blue-600"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                  >
                                    <path
                                      strokeLinecap="round"
                                      strokeLinejoin="round"
                                      strokeWidth={2}
                                      d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                    />
                                  </svg>
                                  <span>
                                    Cot total price:{" "}
                                    {formatCurrency(
                                      summaryData.cotDetails?.total_price ||
                                        (summaryData.cotTotal || 0) /
                                          summaryData.cotQuantity,
                                      summaryData.currencyCode
                                    )}
                                  </span>
                                </div>
                                {/* <div className="flex items-center gap-1 ml-5">
                              <span>Nights: x {summaryData.nights}</span>
                            </div> */}
                                <div className="flex items-center gap-1 ml-5">
                                  <span>
                                    No of cots: {summaryData.cotQuantity}
                                  </span>
                                </div>
                              </div>
                            </div>
                          </div>

                          <div className="flex flex-col items-end gap-1">
                            <div className="text-lg md:text-xl font-bold text-[#285DA6]">
                              {formatCurrency(
                                summaryData.cotTotal || 0,
                                summaryData.currencyCode
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </>
                )
              : null}

            {/* Extra Adults Beyond Capacity Section */}
            {summaryData.extraAdultQuantity ?
              summaryData.extraAdultQuantity > 0 && (
                <>
                  <div className="border-t border-gray-200 py-6 md:py-8 px-6">
                    <div className="flex items-center gap-3 mb-4 md:mb-6">
                      <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-lg flex items-center justify-center">
                        <svg
                          className="w-4 h-4 md:w-5 md:h-5 text-white"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                          <circle cx="9" cy="7" r="4"></circle>
                          <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                          <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                        </svg>
                      </div>
                      <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                        Extra Adults
                      </h2>
                    </div>

                    <div className="bg-gradient-to-br from-blue-50 to-[#285DA6]/10 border border-[#285DA6]/20 p-4 md:p-5 rounded-xl hover:shadow-lg transition-shadow duration-300">
                      <div className="flex flex-col md:flex-row md:items-center justify-between gap-3">
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-gray-900 text-sm md:text-base">
                              Extra Adults
                            </h3>
                          </div>

                          <div className="flex flex-wrap gap-3 text-xs md:text-sm text-gray-600">
                            <div className="space-y-1">
                              <div className="flex items-center gap-1">
                                <svg
                                  className="w-3 h-3 md:w-4 md:h-4 text-blue-600"
                                  fill="none"
                                  stroke="currentColor"
                                  viewBox="0 0 24 24"
                                >
                                  <path
                                    strokeLinecap="round"
                                    strokeLinejoin="round"
                                    strokeWidth={2}
                                    d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                                  />
                                </svg>
                                <span>
                                  Per adult price:{" "}
                                  {formatCurrency(
                                    summaryData.extraAdultTotalPricePerAdult ||
                                      (summaryData.extraAdultTotal || 0) /
                                        summaryData.extraAdultQuantity,
                                    summaryData.currencyCode
                                  )}
                                </span>
                              </div>
                              <div className="flex items-center gap-1 ml-5">
                                <span>
                                  No of adults: {summaryData.extraAdultQuantity}
                                </span>
                              </div>
                            </div>
                          </div>
                        </div>

                        <div className="flex flex-col items-end gap-1">
                          <div className="text-lg md:text-xl font-bold text-[#285DA6]">
                            {formatCurrency(
                              summaryData.extraAdultTotal || 0,
                              summaryData.currencyCode
                            )}
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </>
              ): null}
          </div>

          {/* Stylish Pricing Summary */}
          <div className="px-4 md:px-0">
            <div className="bg-white/90 backdrop-blur-sm border border-white/60 rounded-2xl shadow-xl shadow-blue-500/10 overflow-hidden">
              <div className="border-1 p-1">
                <div className="bg-white rounded-xl p-4 md:p-8">
                  <div className="flex items-center gap-3 mb-4 md:mb-6">
                    <div className="w-6 h-6 md:w-8 md:h-8 bg-gradient-to-br from-[#285DA6] to-blue-500 rounded-lg flex items-center justify-center">
                      <svg
                        className="w-4 h-4 md:w-5 md:h-5 text-white"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                    </div>
                    <h2 className="text-lg md:text-xl font-bold bg-gradient-to-r from-gray-800 to-[#285DA6] bg-clip-text text-transparent">
                      Pricing Summary
                    </h2>
                  </div>

                  <div className="space-y-4 md:space-y-6">
                    {/* Pricing Breakdown */}
                    <div className="space-y-3 md:space-y-4">
                      {/* Room Charges */}
                      <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900 text-sm md:text-base">
                            Room Charges
                          </p>
                          <p className="text-xs md:text-sm text-gray-600">
                            {summaryData.room?.name || "Room"} ×{" "}
                            {summaryData.nights || 1} night
                            {(summaryData.nights || 1) > 1 ? "s" : ""}
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900 text-sm md:text-base">
                            {formatCurrency(
                              (() => {
                                // Calculate room charges based on meal plan pricing if available
                                const currentMealPlan =
                                  summaryData.mealPlan || "none";
                                const mealPlanPrice =
                                  summaryData.room?.mealPlanPrices?.[
                                    currentMealPlan
                                  ];

                                if (mealPlanPrice) {
                                  const baseAmount = mealPlanPrice.total_amount_without_tax ||
                                    mealPlanPrice.per_night_amount *
                                      (summaryData.nights || 1);

                                  // Subtract extra adult costs from room charges to avoid double counting
                                  const extraAdultCost = summaryData.extraAdultTotal || 0;
                                  return Math.max(0, baseAmount - extraAdultCost);
                                } else {
                                  const baseAmount = (summaryData.basePrice || 0) *
                                    (summaryData.nights || 1);

                                  // Subtract extra adult costs from room charges to avoid double counting
                                  const extraAdultCost = summaryData.extraAdultTotal || 0;
                                  return Math.max(0, baseAmount - extraAdultCost);
                                }
                              })(),
                              summaryData.currencyCode || "USD"
                            )}
                          </p>
                        </div>
                      </div>

                      {/* Taxes and Fees */}
                      {/* <div className="flex justify-between items-center py-2 border-b border-gray-100">
                        <div className="flex-1">
                          <p className="font-medium text-gray-900 text-sm md:text-base">
                            Taxes & Fees
                          </p>
                          <p className="text-xs md:text-sm text-gray-600">
                            Service charges and government taxes
                          </p>
                        </div>
                        <div className="text-right">
                          <p className="font-semibold text-gray-900 text-sm md:text-base">
                            {formatCurrency(
                              (() => {
                                const currentMealPlan =
                                  summaryData.mealPlan || "none";
                                const mealPlanPrice =
                                  summaryData.room?.mealPlanPrices?.[
                                    currentMealPlan
                                  ];

                                if (mealPlanPrice) {
                                  return mealPlanPrice.tax_amount || 0;
                                } else {
                                  return summaryData.taxesAndFees || 0;
                                }
                              })(),
                              summaryData.currencyCode || "USD"
                            )}
                          </p>
                        </div>
                      </div> */}

                      {/* Add-ons (if any) */}
                      {summaryData.selectedAddOns &&
                        summaryData.selectedAddOns.length > 0 && (
                          <div className="flex justify-between items-center py-2 border-b border-gray-100">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 text-sm md:text-base">
                                Add-ons & Services
                              </p>
                              <p className="text-xs md:text-sm text-gray-600">
                                {summaryData.selectedAddOns.length} service
                                {summaryData.selectedAddOns.length > 1
                                  ? "s"
                                  : ""}{" "}
                                selected
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-gray-900 text-sm md:text-base">
                                {formatCurrency(
                                  summaryData.addOnsTotal || 0,
                                  summaryData.currencyCode || "USD"
                                )}
                              </p>
                            </div>
                          </div>
                        )}

                      {/* Extra Beds (if any) */}
                      {summaryData.extraBedQuantity
                        ? summaryData.extraBedQuantity > 0 && (
                            <div className="flex justify-between items-center py-2 border-b border-gray-100">
                              <div className="flex-1">
                                <p className="font-medium text-gray-900 text-sm md:text-base">
                                  Extra Beds
                                </p>
                                <p className="text-xs md:text-sm text-gray-600">
                                  {summaryData.extraBedQuantity} bed
                                  {summaryData.extraBedQuantity > 1
                                    ? "s"
                                    : ""}{" "}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold text-gray-900 text-sm md:text-base">
                                  {formatCurrency(
                                    summaryData.extraBedTotal || 0,
                                    summaryData.currencyCode || "USD"
                                  )}
                                </p>
                              </div>
                            </div>
                          )
                        : null}

                      {/* Cots (if any) */}
                      {summaryData.cotQuantity
                        ? summaryData.cotQuantity > 0 && (
                            <div className="flex justify-between items-center py-2 border-b border-gray-100">
                              <div className="flex-1">
                                <p className="font-medium text-gray-900 text-sm md:text-base">
                                  Baby Cots
                                </p>
                                <p className="text-xs md:text-sm text-gray-600">
                                  {summaryData.cotQuantity} cot
                                  {summaryData.cotQuantity > 1 ? "s" : ""}
                                </p>
                              </div>
                              <div className="text-right">
                                <p className="font-semibold text-gray-900 text-sm md:text-base">
                                  {formatCurrency(
                                    summaryData.cotTotal || 0,
                                    summaryData.currencyCode || "USD"
                                  )}
                                </p>
                              </div>
                            </div>
                          )
                        : null}

                      {/* Extra Adults Beyond Capacity (if any) */}
                      {summaryData.extraAdultQuantity ?
                        summaryData.extraAdultQuantity > 0 && (
                          <div className="flex justify-between items-center py-2 border-b border-gray-100">
                            <div className="flex-1">
                              <p className="font-medium text-gray-900 text-sm md:text-base">
                                Extra Adults
                              </p>
                              <p className="text-xs md:text-sm text-gray-600">
                                {summaryData.extraAdultQuantity} adult
                                {summaryData.extraAdultQuantity > 1
                                  ? "s"
                                  : ""}{" "}
                              </p>
                            </div>
                            <div className="text-right">
                              <p className="font-semibold text-gray-900 text-sm md:text-base">
                                {formatCurrency(
                                  summaryData.extraAdultTotal || 0,
                                  summaryData.currencyCode || "USD"
                                )}
                              </p>
                            </div>
                          </div>
                        ): null}

                      {/* Total Amount */}
                      <div className="bg-gradient-to-r from-blue-50 to-[#285DA6]/10 p-4 md:p-5 rounded-xl border border-[#285DA6]/20 mt-4">
                        <div className="flex justify-between items-center">
                          <div className="flex-1">
                            <p className="text-lg md:text-xl font-bold text-gray-900">
                              Total Amount Paid
                            </p>
                            <p className="text-xs md:text-sm text-gray-600">
                              All charges included
                            </p>
                          </div>
                          <div className="text-right">
                            <p className="text-2xl md:text-3xl font-bold bg-gradient-to-r from-[#285DA6] to-blue-500 bg-clip-text text-transparent">
                              {formatCurrency(
                                (() => {
                                  // Calculate correct total based on meal plan pricing if available
                                  const currentMealPlan =
                                    summaryData.mealPlan || "none";
                                  const mealPlanPrice =
                                    summaryData.room?.mealPlanPrices?.[
                                      currentMealPlan
                                    ];

                                  if (mealPlanPrice) {
                                    // Use meal plan pricing for accurate calculation
                                    const baseAmount =
                                      mealPlanPrice.total_amount_without_tax ||
                                      mealPlanPrice.per_night_amount *
                                        (summaryData.nights || 1);
                                    const taxAmount =
                                      mealPlanPrice.tax_amount || 0;

                                    // Calculate room charges excluding extra adults to avoid double counting
                                    const extraAdultCost = summaryData.extraAdultTotal || 0;
                                    const adjustedRoomCharges = Math.max(0, baseAmount - extraAdultCost);

                                    return (
                                      adjustedRoomCharges +
                                      taxAmount +
                                      (summaryData.addOnsTotal || 0) +
                                      (summaryData.extraBedTotal || 0) +
                                      (summaryData.cotTotal || 0) +
                                      (summaryData.extraAdultTotal || 0)
                                    );
                                  } else {
                                    // Fallback to stored values
                                    const baseAmount = (summaryData.basePrice || 0) *
                                      (summaryData.nights || 1);

                                    // Calculate room charges excluding extra adults to avoid double counting
                                    const extraAdultCost = summaryData.extraAdultTotal || 0;
                                    const adjustedRoomCharges = Math.max(0, baseAmount - extraAdultCost);

                                    return (
                                      adjustedRoomCharges +
                                      (summaryData.taxesAndFees || 0) +
                                      (summaryData.addOnsTotal || 0) +
                                      (summaryData.extraBedTotal || 0) +
                                      (summaryData.cotTotal || 0) +
                                      (summaryData.extraAdultTotal || 0) ||
                                      summaryData.totalAmount ||
                                      0
                                    );
                                  }
                                })(),
                                summaryData.currencyCode || "USD"
                              )}
                            </p>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Payment Confirmation */}
                    <div className="bg-gradient-to-r from-green-50 to-emerald-50 p-3 md:p-4 rounded-xl border border-green-200">
                      <div className="flex items-center justify-center gap-2">
                        <div className="w-5 h-5 md:w-6 md:h-6 bg-green-500 rounded-full flex items-center justify-center">
                          <svg
                            className="w-3 h-3 md:w-4 md:h-4 text-white"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth={2.5}
                              d="M5 13l4 4L19 7"
                            />
                          </svg>
                        </div>
                        <p className="text-green-700 font-semibold text-sm md:text-base">
                          Payment Confirmed & Processed
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Error Message */}
          {downloadError && (
            <div className="bg-red-50 border border-red-200 rounded-xl p-4 mb-6">
              <div className="flex items-center">
                <svg
                  className="w-5 h-5 text-red-500 mr-3"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M12 8v4m0 4h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                <p className="text-red-700 text-sm">{downloadError}</p>
              </div>
            </div>
          )}

          {/* Stylish Action Buttons */}
          <div className="flex flex-col md:flex-row gap-3 md:gap-4 px-4 md:px-0">
            <Button
              onClick={
                isAuthenticated ? handleDownloadInvoice : handleLoginRedirect
              }
              disabled={
                isAuthenticated && (downloadingInvoice || !summaryData?.orderId)
              }
              className="flex-1 bg-gradient-to-r from-[#285DA6] to-blue-500 hover:from-[#285DA6]/90 hover:to-blue-500/90 text-white font-semibold py-3 md:py-4 px-6 md:px-8 rounded-xl shadow-lg shadow-blue-500/25 transition-all duration-300 transform hover:scale-105 hover:shadow-xl text-sm md:text-base"
            >
              {isAuthenticated ? (
                downloadingInvoice ? (
                  <>
                    <div className="animate-spin rounded-full h-4 w-4 md:h-5 md:w-5 border-b-2 border-current mr-2"></div>
                    Downloading...
                  </>
                ) : (
                  <>
                    <svg
                      className="w-4 h-4 md:w-5 md:h-5 mr-2"
                      fill="none"
                      stroke="currentColor"
                      viewBox="0 0 24 24"
                    >
                      <path
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        strokeWidth={2}
                        d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                      />
                    </svg>
                    Download Invoice
                  </>
                )
              ) : (
                <>
                  <svg
                    className="w-4 h-4 md:w-5 md:h-5 mr-2"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      strokeWidth={2}
                      d="M11 16l-4-4m0 0l4-4m-4 4h14m-5 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h7a3 3 0 013 3v1"
                    />
                  </svg>
                  Login to Download Invoice
                </>
              )}
            </Button>
            <Button
              onClick={handleViewBookings}
              variant="outline"
              className="flex-1 border-2 border-[#285DA6] text-[#285DA6] hover:bg-gradient-to-r hover:from-[#285DA6] hover:to-blue-500 hover:text-white hover:border-transparent font-semibold py-3 md:py-4 px-6 md:px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 text-sm md:text-base"
            >
              <svg
                className="w-4 h-4 md:w-5 md:h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                />
              </svg>
              View My Bookings
            </Button>
            <Button
              onClick={handleReturnHome}
              variant="outline"
              className="flex-1 border-2 border-[#285DA6] text-[#285DA6] hover:bg-gradient-to-r hover:from-[#285DA6] hover:to-blue-500 hover:text-white hover:border-transparent font-semibold py-3 md:py-4 px-6 md:px-8 rounded-xl transition-all duration-300 transform hover:scale-105 hover:shadow-lg hover:shadow-blue-500/25 text-sm md:text-base"
            >
              <svg
                className="w-4 h-4 md:w-5 md:h-5 mr-2"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth={2}
                  d="M3 12l2-2m0 0l7-7 7 7M5 10v10a1 1 0 001 1h3m10-11l2 2m-2-2v10a1 1 0 01-1 1h-3m-6 0a1 1 0 001-1v-4a1 1 0 011-1h2a1 1 0 011 1v4a1 1 0 001 1m-6 0h6"
                />
              </svg>
              Return to Home
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingSummary;
