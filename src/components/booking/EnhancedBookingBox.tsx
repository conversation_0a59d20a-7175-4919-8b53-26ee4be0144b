import React, { useState, useEffect } from "react";
import DateRangePicker from "../search/DateRangePicker";
import { fetchHotelAvailability } from "../../utils/store/hotels";
import type { MealPlanPrice } from "../../utils/store/hotels";
import { formatDate, formatDateForAPI } from "../../utils/dateUtils";
import BookingModal from "./BookingModal";
import MealPlanSelector, { type MealPlanType } from "./MealPlanSelector";
import { type CartItem } from "../../utils/cartUtils";
import { type RoomType, type EnhancedBookingBoxProps } from "../../utils/types";

const EnhancedBookingBox: React.FC<EnhancedBookingBoxProps> = ({
  hotel,
  availableRooms = [],
  defaultCheckIn,
  defaultCheckOut,
  defaultNights = 2,
  defaultGuests,
  onDatesChange,
  onGuestChange,
  checkInDate: externalCheckInDate,
  checkOutDate: externalCheckOutDate,
  setCheckInDate: externalSetCheckInDate,
  setCheckOutDate: externalSetCheckOutDate,
  lang = "en",
}) => {
  // Function to add item to cart using localStorage
  const addToCart = (item: any) => {
    try {
      // Get existing cart or create a new one
      const existingCartData = localStorage.getItem("cart");
      let existingCart = [];

      if (existingCartData) {
        try {
          existingCart = JSON.parse(existingCartData);
          if (!Array.isArray(existingCart)) {
            console.error(
              "Existing cart is not an array, resetting:",
              existingCart
            );
            existingCart = [];
          }
        } catch (parseError) {
          console.error(
            "Failed to parse existing cart, resetting:",
            parseError
          );
        }
      }

      // Remove any existing items with the same ID (avoid duplicates)
      existingCart = existingCart.filter(
        (cartItem: any) => cartItem.id !== item.id
      );

      // Add the new item
      existingCart.push(item);

      // Save back to localStorage
      localStorage.setItem("cart", JSON.stringify(existingCart));
      return true;
    } catch (error) {
      console.error("Failed to add item to cart:", error);
      return false;
    }
  };
  // State for selected room
  const [selectedRoom, setSelectedRoom] = useState<RoomType | null>(null);
  const [currentRoomCapacity, setCurrentRoomCapacity] = useState({
    maxAdults: 0,
    maxChildren: 0,
    maxInfants: 0,
    maxOccupancy: 0,
  });

  // State for room quantity
  const [roomQuantity, setRoomQuantity] = useState(1);

  // Initialize with default dates - use internal state if external state is not provided
  const [internalStartDate, setInternalStartDate] = useState<Date | null>(
    () => {
      // Use default check-in date or tomorrow
      if (defaultCheckIn) {
        return new Date(defaultCheckIn);
      } else {
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        return tomorrow;
      }
    }
  );
  const [internalEndDate, setInternalEndDate] = useState<Date | null>(() => {
    // Use default check-out date or tomorrow + defaultNights
    if (defaultCheckOut) {
      return new Date(defaultCheckOut);
    } else {
      const checkoutDate = new Date();
      // Start from tomorrow and add defaultNights
      checkoutDate.setDate(checkoutDate.getDate() + 1 + (defaultNights || 2));
      return checkoutDate;
    }
  });

  // Use either external or internal state
  // Important: We need to ensure we're not using undefined as a value
  const startDate =
    externalCheckInDate !== undefined && externalCheckInDate !== null
      ? externalCheckInDate
      : internalStartDate;
  const endDate =
    externalCheckOutDate !== undefined && externalCheckOutDate !== null
      ? externalCheckOutDate
      : internalEndDate;

  // Create wrapper functions to ensure we're updating both states properly
  const setStartDate = (date: Date | null) => {
    if (externalSetCheckInDate) {
      externalSetCheckInDate(date);
    }
    setInternalStartDate(date);
  };

  const setEndDate = (date: Date | null) => {
    if (externalSetCheckOutDate) {
      externalSetCheckOutDate(date);
    }
    setInternalEndDate(date);
  };

  const [guests, setGuests] = useState(
    defaultGuests || {
      adults: 1,
      children: 0,
      infants: 0,
      pets: 0,
    }
  );

  // Initialize nights to 0 when no dates are selected
  const [nights, setNights] = useState(
    startDate && endDate ? defaultNights || 0 : 0
  );
  const [subtotal, setSubtotal] = useState<number>(
    (typeof selectedRoom?.price === 'number' ? selectedRoom.price : 0) ||
      hotel.price * (startDate && endDate ? defaultNights || 0 : 0)
  );

  // State for controlling popovers
  const [datePickerOpen, setDatePickerOpen] = useState(false);
  // const [guestSelectorOpen, setGuestSelectorOpen] = useState(false); // Unused for now
  const [bookingModalOpen, setBookingModalOpen] = useState(false);

  // Meal plan options (needed for BookingModal)
  // Order matters - 'none' is the first item and should be the default
  const mealPlanLabels: Record<MealPlanType, string> = {
    none: "No Meals",
    bb: "Bed & Breakfast",
    hb: "Half Board",
    fb: "Full Board",
  };

  // Default to No Meals (none) as it's the first item in the meal plan array
  const [selectedMealPlan, setSelectedMealPlan] =
    useState<MealPlanType>("none");

  // Get current room price based on selected meal plan
  const getCurrentRoomPrice = () => {
    if (selectedRoom) {
      // If room has meal plan pricing, use it
      if (selectedRoom.priceDetails?.meal_plans) {
        const mealPlans = selectedRoom.priceDetails.meal_plans;

        // Try to get the price for the selected meal plan
        if (mealPlans[selectedMealPlan]) {
          return mealPlans[selectedMealPlan].per_night_amount;
        }
      }

      // Fallback to regular room price
      return (typeof selectedRoom.price === 'number' ? selectedRoom.price : 0) || hotel.price;
    }

    return hotel.price;
  };

  // Note: Total price calculation is now handled directly in the handleMealPlanChange function

  // Listen for date picker open event
  useEffect(() => {
    const handleOpenDatePicker = () => {
      setDatePickerOpen(true);
    };

    // Add event listener for opening date picker
    document.addEventListener("openDatePicker", handleOpenDatePicker);

    // Clean up event listener
    return () => {
      document.removeEventListener("openDatePicker", handleOpenDatePicker);
    };
  }, []);

  // Listen for room selection events from the stays/[id].astro page
  useEffect(() => {
    const handleRoomSelected = (event: CustomEvent) => {
      // Find the selected room in availableRooms
      if (availableRooms && availableRooms.length > 0) {
        const selectedRoomData = availableRooms.find(
          (room) => room.id.toString() === event.detail.roomId.toString()
        );

        if (selectedRoomData) {
          // Update the selected room
          setSelectedRoom(selectedRoomData);

          // Calculate proper max occupancy
          const maxAdults =
            selectedRoomData.maxAdults || selectedRoomData.maxGuests || 0;
          const maxChildren = selectedRoomData.maxChildren || 0;
          const maxInfants = selectedRoomData.maxInfants || 0;
          const maxOccupancy = Math.max(
            selectedRoomData.maxGuests || 0,
            maxAdults + maxChildren + maxInfants
          );

          // Update room capacity with proper values
          const newCapacity = {
            maxAdults,
            maxChildren,
            maxInfants,
            maxOccupancy,
          };

          setCurrentRoomCapacity(newCapacity);

          // Adjust guest counts based on new room capacity
          const adjustedGuests = adjustGuestsToCapacity(guests, newCapacity);
          setGuests(adjustedGuests);

          // Reset room quantity to 1 when selecting a new room
          setRoomQuantity(1);

          // Set default meal plan for the new room
          if (selectedRoomData.priceDetails?.meal_plans) {
            const mealPlans = selectedRoomData.priceDetails.meal_plans;
            let newMealPlan: MealPlanType = "none";

            // Try to set No Meals (none) as default, or find first available option
            if (mealPlans.none) {
              newMealPlan = "none";
            } else {
              // Find first available meal plan
              const availableMealPlan = Object.keys(mealPlans).find(
                (plan) => mealPlans[plan as MealPlanType]
              );
              if (availableMealPlan) {
                newMealPlan = availableMealPlan as MealPlanType;
              }
            }

            // Update the meal plan
            setSelectedMealPlan(newMealPlan);

            // Update pricing based on the new room and selected meal plan
            if (startDate && endDate) {
              // Calculate nights
              const startDay = new Date(
                startDate.getFullYear(),
                startDate.getMonth(),
                startDate.getDate()
              );
              const endDay = new Date(
                endDate.getFullYear(),
                endDate.getMonth(),
                endDate.getDate()
              );

              const diffTime = endDay.getTime() - startDay.getTime();
              const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
              const nightsCount = Math.max(1, diffDays);

              // Update price based on the selected meal plan
              if (mealPlans[newMealPlan]) {
                const mealPlanPrice = mealPlans[newMealPlan];

                // If the API provides a total_amount, use it
                if (mealPlanPrice.total_amount) {
                  setSubtotal(mealPlanPrice.total_amount);
                } else if (mealPlanPrice.per_night_amount) {
                  // Otherwise calculate based on per_night_amount and nights
                  const calculatedTotal =
                    mealPlanPrice.per_night_amount * nightsCount;
                  setSubtotal(calculatedTotal);
                }
              } else {
                // Fallback to calculated prices
                const currentPrice = selectedRoomData.price || 0;
                const newSubtotal = currentPrice * nightsCount;
                setSubtotal(newSubtotal);
              }

              // Force update the pricing summary display
              setTimeout(() => {
                const nightsTotalElement =
                  document.getElementById("nights-total-price");
                const finalTotalElement =
                  document.getElementById("final-total-price");

                if (nightsTotalElement && finalTotalElement) {
                  const formattedPrice = formatCurrency(
                    startDate && endDate
                      ? mealPlans[newMealPlan]?.total_amount ||
                          (mealPlans[newMealPlan]?.per_night_amount ||
                            selectedRoomData.price ||
                            0) * nightsCount
                      : 0,
                    getCurrencyCode()
                  );

                  // Update the displayed prices
                  nightsTotalElement.textContent = formattedPrice;
                  finalTotalElement.textContent = formattedPrice;
                }
              }, 50);
            }
          }
        } else {
          console.warn(
            "Selected room not found in available rooms:",
            event.detail.roomId
          );
        }
      }
    };

    // Add event listener for room selection
    document.addEventListener(
      "roomSelected",
      handleRoomSelected as EventListener
    );

    // Clean up event listener
    return () => {
      document.removeEventListener(
        "roomSelected",
        handleRoomSelected as EventListener
      );
    };
  }, [availableRooms]);

  // Initialize with first available room if any or update selected room when availableRooms changes
  useEffect(() => {
    if (availableRooms && availableRooms.length > 0) {
      // If there's no selected room yet, or if the selected room is no longer available,
      // or if availableRooms has been updated due to date changes
      const currentRoomStillAvailable =
        selectedRoom &&
        availableRooms.some((room) => room.id === selectedRoom.id);

      if (!selectedRoom || !currentRoomStillAvailable) {
        // Select the first available room
        const firstRoom = availableRooms[0];
        setSelectedRoom(firstRoom);

        // Calculate proper max occupancy
        const maxAdults = firstRoom.maxAdults || firstRoom.maxGuests || 0;
        const maxChildren = firstRoom.maxChildren || 0;
        const maxInfants = firstRoom.maxInfants || 0;
        const maxOccupancy = Math.max(
          firstRoom.maxGuests || 0,
          maxAdults + maxChildren + maxInfants
        );

        // Update room capacity with proper values
        const newCapacity = {
          maxAdults,
          maxChildren,
          maxInfants,
          maxOccupancy,
        };
        setCurrentRoomCapacity(newCapacity);

        // Adjust guest counts based on new room capacity
        const adjustedGuests = adjustGuestsToCapacity(guests, newCapacity);
        setGuests(adjustedGuests);

        // Reset room quantity to 1 when selecting a new room
        setRoomQuantity(1);

        // Use default guests if provided, otherwise reset to defaults
        if (!defaultGuests) {
          setGuests({
            adults: 1, // Always start with 1 adult
            children: 0,
            infants: 0,
            pets: 0,
          });
        }

        // Set default meal plan to No Meals (none) if available, otherwise first available option
        if (firstRoom.priceDetails?.meal_plans) {
          const mealPlans = firstRoom.priceDetails.meal_plans;
          if (mealPlans.none) {
            setSelectedMealPlan("none");
          } else {
            // Find first available meal plan
            const availableMealPlan = Object.keys(mealPlans).find(
              (plan) => mealPlans[plan as MealPlanType]
            );
            if (availableMealPlan) {
              setSelectedMealPlan(availableMealPlan as MealPlanType);
            }
          }
        }
      } else if (currentRoomStillAvailable) {
        // If the current room is still available, update its data with the latest from availableRooms
        const updatedRoomData = availableRooms.find(
          (room) => room.id === selectedRoom.id
        );
        if (updatedRoomData) {
          setSelectedRoom(updatedRoomData);

          // Calculate proper max occupancy
          const maxAdults =
            updatedRoomData.maxAdults || updatedRoomData.maxGuests || 0;
          const maxChildren = updatedRoomData.maxChildren || 0;
          const maxInfants = updatedRoomData.maxInfants || 0;
          const maxOccupancy = Math.max(
            updatedRoomData.maxGuests || 0,
            maxAdults + maxChildren + maxInfants
          );

          // Update room capacity with proper values
          const newCapacity = {
            maxAdults,
            maxChildren,
            maxInfants,
            maxOccupancy,
          };

          setCurrentRoomCapacity(newCapacity);

          // Adjust guest counts based on new room capacity
          const adjustedGuests = adjustGuestsToCapacity(guests, newCapacity);
          setGuests(adjustedGuests);

          // Update meal plan prices if needed
          if (
            updatedRoomData.priceDetails?.meal_plans &&
            updatedRoomData.priceDetails.meal_plans[selectedMealPlan]
          ) {
            // The meal plan is still available, update pricing
            const mealPlanPrice =
              updatedRoomData.priceDetails.meal_plans[selectedMealPlan];

            // Calculate nights
            if (startDate && endDate) {
              const startDay = new Date(
                startDate.getFullYear(),
                startDate.getMonth(),
                startDate.getDate()
              );
              const endDay = new Date(
                endDate.getFullYear(),
                endDate.getMonth(),
                endDate.getDate()
              );

              const diffTime = endDay.getTime() - startDay.getTime();
              const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
              const nightsCount = Math.max(1, diffDays);

              // Update price based on the selected meal plan
              if (mealPlanPrice.total_amount) {
                setSubtotal(mealPlanPrice.total_amount);
              } else if (mealPlanPrice.per_night_amount) {
                const calculatedTotal =
                  mealPlanPrice.per_night_amount * nightsCount;
                setSubtotal(calculatedTotal);
              }
            }
          }
        }
      }
    }
  }, [
    availableRooms,
    selectedRoom,
    startDate,
    endDate,
    selectedMealPlan,
    defaultGuests,
  ]);

  // Update calculations when dates, room price, or meal plan changes
  useEffect(() => {
    if (startDate && endDate) {
      // Create date objects without time component to ensure accurate calculation
      const startDay = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate()
      );
      const endDay = new Date(
        endDate.getFullYear(),
        endDate.getMonth(),
        endDate.getDate()
      );

      // Calculate the difference in days
      const diffTime = endDay.getTime() - startDay.getTime();
      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

      // Ensure we always return at least 1 night for valid date ranges
      const nightsCount = Math.max(1, diffDays);

      setNights(nightsCount);

      // Check if we have meal plan pricing available
      if (
        selectedRoom?.priceDetails?.meal_plans &&
        selectedRoom.priceDetails.meal_plans[selectedMealPlan]
      ) {
        const mealPlanPrice =
          selectedRoom.priceDetails.meal_plans[selectedMealPlan];

        // If the API provides a total_amount, use it
        if (mealPlanPrice.total_amount) {
          setSubtotal(mealPlanPrice.total_amount);
        } else if (mealPlanPrice.per_night_amount) {
          // Otherwise calculate based on per_night_amount and nights
          setSubtotal(mealPlanPrice.per_night_amount * nightsCount);
        }
      } else {
        // Fallback to calculated prices
        const currentPrice = getCurrentRoomPrice();
        const newSubtotal = currentPrice * nightsCount;

        setSubtotal(newSubtotal);
      }
    }
  }, [startDate, endDate, selectedRoom, selectedMealPlan]);

  const handleDateRangeChange = async (
    newStartDate: Date | null,
    newEndDate: Date | null
  ) => {
    // Update the state with the new dates
    if (newStartDate) {
      // Create a clean date object without time component
      const cleanStartDate = new Date(
        newStartDate.getFullYear(),
        newStartDate.getMonth(),
        newStartDate.getDate()
      );
      setStartDate(cleanStartDate);
    } else {
      setStartDate(null);
    }

    if (newEndDate) {
      // Create a clean date object without time component
      const cleanEndDate = new Date(
        newEndDate.getFullYear(),
        newEndDate.getMonth(),
        newEndDate.getDate()
      );
      setEndDate(cleanEndDate);
    } else {
      setEndDate(null);
    }

    // Call the parent's onDatesChange function if provided
    if (onDatesChange && newStartDate && newEndDate) {
      onDatesChange(newStartDate, newEndDate);
    } else if (newStartDate && newEndDate) {
      // Otherwise, use the existing implementation for local state
      // Calculate nights for UI update
      // Create date objects without time component to ensure accurate calculation
      const startDay = new Date(
        newStartDate.getFullYear(),
        newStartDate.getMonth(),
        newStartDate.getDate()
      );
      const endDay = new Date(
        newEndDate.getFullYear(),
        newEndDate.getMonth(),
        newEndDate.getDate()
      );

      // Calculate the difference in days
      const diffTime = endDay.getTime() - startDay.getTime();
      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

      // Ensure we always return at least 1 night for valid date ranges
      const nightsCount = Math.max(1, diffDays);

      setNights(nightsCount);

      // Make API call with updated dates to get fresh pricing
      try {
        const hotelId = hotel.id.toString();
        const availabilityData = await fetchHotelAvailability(
          hotelId,
          startDay, // Use startDay instead of cleanStartDate
          endDay, // Use endDay instead of cleanEndDate
          guests.adults,
          guests.children,
          guests.infants,
          getCurrencyCode()
        );

        // Update available rooms with new data
        if (
          availabilityData.available_rooms &&
          availabilityData.available_rooms.length > 0
        ) {
          // Find the currently selected room in the new data
          const updatedSelectedRoom = availabilityData.available_rooms.find(
            (room) => room.id === selectedRoom?.id
          );

          if (updatedSelectedRoom) {
            // Update the selected room with fresh data
            const updatedRoomData = {
              id: updatedSelectedRoom.id,
              name: updatedSelectedRoom.title,
              price: updatedSelectedRoom.price?.per_night_amount || 0,
              maxAdults: updatedSelectedRoom.max_adults,
              maxChildren: updatedSelectedRoom.max_children,
              maxInfants: updatedSelectedRoom.max_infants,
              thumbnail: updatedSelectedRoom.thumbnail,
              availableRooms: updatedSelectedRoom.available_rooms,
              extra_adults_beyond_capacity: updatedSelectedRoom.extra_adults_beyond_capacity,
              priceDetails: {
                ...updatedSelectedRoom.price,
                amount: updatedSelectedRoom.price?.amount || 0,
                original_amount:
                  updatedSelectedRoom.price?.original_amount || 0,
                currency_code:
                  updatedSelectedRoom.price?.currency_code || "USD",
                total_amount: updatedSelectedRoom.price?.total_amount || 0,
                per_night_amount:
                  updatedSelectedRoom.price?.per_night_amount || 0,
                nights: updatedSelectedRoom.price?.nights || 1,
                meal_plans: updatedSelectedRoom.price?.meal_plans as Record<
                  MealPlanType,
                  MealPlanPrice
                >,
              },
            };

            setSelectedRoom(updatedRoomData);

            // Update room capacity based on the new room data
            const maxAdults = updatedSelectedRoom.max_adults || 0;
            const maxChildren = updatedSelectedRoom.max_children || 0;
            const maxInfants = updatedSelectedRoom.max_infants || 0;

            const newCapacity = {
              maxAdults,
              maxChildren,
              maxInfants,
              maxOccupancy: maxAdults + maxChildren + maxInfants,
            };

            setCurrentRoomCapacity(newCapacity);

            // Update price based on the new data
            if (
              updatedSelectedRoom.price?.meal_plans &&
              updatedSelectedRoom.price.meal_plans[
                selectedMealPlan as keyof typeof updatedSelectedRoom.price.meal_plans
              ]
            ) {
              const mealPlanPrice = updatedSelectedRoom.price.meal_plans[
                selectedMealPlan as keyof typeof updatedSelectedRoom.price.meal_plans
              ] as MealPlanPrice;

              // If the API provides a total_amount, use it
              if (mealPlanPrice.total_amount) {
                setSubtotal(mealPlanPrice.total_amount);
              } else if (mealPlanPrice.per_night_amount) {
                // Otherwise calculate based on per_night_amount and nights
                const calculatedTotal =
                  mealPlanPrice.per_night_amount * nightsCount;
                setSubtotal(calculatedTotal);
              }
            } else {
              // Fallback to calculated prices
              const currentPrice =
                updatedSelectedRoom.price?.per_night_amount || 0;
              const newSubtotal = currentPrice * nightsCount;
              setSubtotal(newSubtotal);
            }
          } else {
            // If the previously selected room is no longer available, select the first available room
            const firstRoom = availabilityData.available_rooms[0];
            setSelectedRoom({
              id: firstRoom.id,
              name: firstRoom.title,
              price: firstRoom.price?.per_night_amount || 0,
              maxAdults: firstRoom.max_adults,
              maxChildren: firstRoom.max_children,
              maxInfants: firstRoom.max_infants,
              availableRooms: firstRoom.available_rooms,
              thumbnail: firstRoom.thumbnail,
              extra_adults_beyond_capacity: firstRoom.extra_adults_beyond_capacity,
              priceDetails: {
                ...firstRoom.price,
                amount: firstRoom.price?.amount || 0,
                original_amount: firstRoom.price?.original_amount || 0,
                currency_code: firstRoom.price?.currency_code || "USD",
                total_amount: firstRoom.price?.total_amount || 0,
                per_night_amount: firstRoom.price?.per_night_amount || 0,
                nights: firstRoom.price?.nights || 1,
                meal_plans: firstRoom.price?.meal_plans as Record<
                  MealPlanType,
                  MealPlanPrice
                >,
              },
            });

            // Update room capacity based on the new room data
            const maxAdults = firstRoom.max_adults || 0;
            const maxChildren = firstRoom.max_children || 0;
            const maxInfants = firstRoom.max_infants || 0;

            const newCapacity = {
              maxAdults,
              maxChildren,
              maxInfants,
              maxOccupancy: maxAdults + maxChildren + maxInfants,
            };

            setCurrentRoomCapacity(newCapacity);

            // Update price based on the new data
            if (
              firstRoom.price?.meal_plans &&
              firstRoom.price.meal_plans[
                selectedMealPlan as keyof typeof firstRoom.price.meal_plans
              ]
            ) {
              const mealPlanPrice = firstRoom.price.meal_plans[
                selectedMealPlan as keyof typeof firstRoom.price.meal_plans
              ] as MealPlanPrice;

              // If the API provides a total_amount, use it
              if (mealPlanPrice.total_amount) {
                setSubtotal(mealPlanPrice.total_amount);
              } else if (mealPlanPrice.per_night_amount) {
                // Otherwise calculate based on per_night_amount and nights
                setSubtotal(mealPlanPrice.per_night_amount * nightsCount);
              }
            } else {
              // Fallback to calculated prices
              const currentPrice = firstRoom.price?.per_night_amount || 0;
              const newSubtotal = currentPrice * nightsCount;
              setSubtotal(newSubtotal);
            }
          }
        } else {
          console.warn("No available rooms returned from API");
        }
      } catch (error) {
        console.error("Error fetching updated availability data:", error);

        // Fallback to calculated prices if API call fails
        if (
          selectedRoom?.priceDetails?.meal_plans &&
          selectedRoom.priceDetails.meal_plans[selectedMealPlan]
        ) {
          const mealPlanPrice =
            selectedRoom.priceDetails.meal_plans[selectedMealPlan];

          // If the API provides a total_amount, use it
          if (mealPlanPrice?.total_amount) {
            setSubtotal(mealPlanPrice.total_amount);
          } else if (mealPlanPrice?.per_night_amount) {
            // Otherwise calculate based on per_night_amount and nights
            const calculatedTotal =
              mealPlanPrice.per_night_amount * nightsCount;
            setSubtotal(calculatedTotal);
          }
        } else {
          // Fallback to calculated prices
          const currentPrice = getCurrentRoomPrice();
          const newSubtotal = currentPrice * nightsCount;
          setSubtotal(newSubtotal);
        }
      }
    } else {
      // If either date is not selected, set nights and subtotal to 0
      setNights(0);
      setSubtotal(0);
    }

    // Close the date picker modal after selection is complete
    if (newStartDate && newEndDate) {
      setTimeout(() => {
        setDatePickerOpen(false);
      }, 500);
    }
  };

  // Helper function to calculate total occupancy
  const calculateTotalOccupancy = (guestObj: typeof guests) => {
    return guestObj.adults + guestObj.children + guestObj.infants;
  };

  // Helper function to adjust guest counts based on room capacity
  const adjustGuestsToCapacity = (
    currentGuests: typeof guests,
    capacity: typeof currentRoomCapacity
  ) => {
    // Check if total exceeds max occupancy
    const totalGuests = calculateTotalOccupancy(currentGuests);

    if (totalGuests > capacity.maxOccupancy) {
      // Reset to default values that fit within capacity
      return {
        adults: Math.min(1, capacity.maxAdults || 1),
        children: 0,
        infants: 0,
        pets: 0,
      };
    }

    // Check if individual counts exceed their limits
    const adjustedGuests = { ...currentGuests };
    let needsAdjustment = false;

    // If maxAdults is 0, set adults to 0
    if (
      capacity.maxAdults === 0 ||
      adjustedGuests.adults > (capacity.maxAdults || 1)
    ) {
      adjustedGuests.adults =
        capacity.maxAdults === 0
          ? 0
          : Math.min(capacity.maxAdults || 1, capacity.maxOccupancy);
      needsAdjustment = true;
    }

    // If maxChildren is 0, set children to 0
    if (
      capacity.maxChildren === 0 ||
      adjustedGuests.children > (capacity.maxChildren || 0)
    ) {
      adjustedGuests.children =
        capacity.maxChildren === 0
          ? 0
          : Math.min(
              capacity.maxChildren || 0,
              capacity.maxOccupancy - adjustedGuests.adults
            );
      needsAdjustment = true;
    }

    // If maxInfants is 0, set infants to 0
    if (
      capacity.maxInfants === 0 ||
      adjustedGuests.infants > (capacity.maxInfants || 0)
    ) {
      adjustedGuests.infants =
        capacity.maxInfants === 0
          ? 0
          : Math.min(
              capacity.maxInfants || 0,
              capacity.maxOccupancy -
                adjustedGuests.adults -
                adjustedGuests.children
            );
      needsAdjustment = true;
    }

    if (needsAdjustment) {
      return adjustedGuests;
    }

    // No adjustment needed
    return currentGuests;
  };

  // Function to handle guest changes and make API call
  const handleGuestChange = async (newGuests: typeof guests) => {
    // Check if the new guest configuration exceeds maximum occupancy
    const newTotalOccupancy = calculateTotalOccupancy(newGuests);
    if (newTotalOccupancy > currentRoomCapacity.maxOccupancy) {
      console.warn(
        "Cannot add more guests: would exceed maximum occupancy of",
        currentRoomCapacity.maxOccupancy
      );
      return;
    }

    // Ensure individual guest types don't exceed their specific limits
    const adjustedGuests = adjustGuestsToCapacity(
      newGuests,
      currentRoomCapacity
    );

    // Update the guests state
    setGuests(adjustedGuests);

    // Call the parent's onGuestChange function if provided
    if (onGuestChange) {
      onGuestChange(
        adjustedGuests.adults,
        adjustedGuests.children,
        adjustedGuests.infants
      );
      // Parent will handle the API call and update all components
      return;
    }

    // Only make API call if we have valid dates and no parent handler
    if (startDate && endDate) {
      try {
        const hotelId = hotel.id.toString();

        // Create clean date objects
        const startDay = new Date(
          startDate.getFullYear(),
          startDate.getMonth(),
          startDate.getDate()
        );
        const endDay = new Date(
          endDate.getFullYear(),
          endDate.getMonth(),
          endDate.getDate()
        );

        // Fetch updated availability data with new guest count
        const availabilityData = await fetchHotelAvailability(
          hotelId,
          startDay,
          endDay,
          adjustedGuests.adults,
          adjustedGuests.children,
          adjustedGuests.infants,
          getCurrencyCode()
        );

        // Update available rooms with new data
        if (
          availabilityData.available_rooms &&
          availabilityData.available_rooms.length > 0
        ) {
          // Find the currently selected room in the new data
          const updatedSelectedRoom = availabilityData.available_rooms.find(
            (room) => room.id === selectedRoom?.id
          );

          if (updatedSelectedRoom) {
            // Update the selected room with fresh data
            const updatedRoomData = {
              id: updatedSelectedRoom.id,
              name: updatedSelectedRoom.title,
              price: updatedSelectedRoom.price?.per_night_amount || 0,
              maxAdults: updatedSelectedRoom.max_adults,
              maxChildren: updatedSelectedRoom.max_children,
              maxInfants: updatedSelectedRoom.max_infants,
              availableRooms: updatedSelectedRoom.available_rooms,
              thumbnail: updatedSelectedRoom.thumbnail,
              extra_adults_beyond_capacity: updatedSelectedRoom.extra_adults_beyond_capacity,
              priceDetails: {
                ...updatedSelectedRoom.price,
                amount: updatedSelectedRoom.price?.amount || 0,
                original_amount:
                  updatedSelectedRoom.price?.original_amount || 0,
                currency_code:
                  updatedSelectedRoom.price?.currency_code || "USD",
                total_amount: updatedSelectedRoom.price?.total_amount || 0,
                per_night_amount:
                  updatedSelectedRoom.price?.per_night_amount || 0,
                nights: updatedSelectedRoom.price?.nights || 1,
                meal_plans: updatedSelectedRoom.price?.meal_plans as Record<
                  MealPlanType,
                  MealPlanPrice
                >,
              },
            };

            setSelectedRoom(updatedRoomData);

            // Update room capacity based on the new room data
            const maxAdults = updatedSelectedRoom.max_adults || 0;
            const maxChildren = updatedSelectedRoom.max_children || 0;
            const maxInfants = updatedSelectedRoom.max_infants || 0;

            const newCapacity = {
              maxAdults,
              maxChildren,
              maxInfants,
              maxOccupancy: maxAdults + maxChildren + maxInfants,
            };

            setCurrentRoomCapacity(newCapacity);

            // Update price based on the new data
            updatePriceFromRoomData(updatedSelectedRoom, nights);
          } else {
            // If the previously selected room is no longer available, select the first available room
            const firstRoom = availabilityData.available_rooms[0];
            setSelectedRoom({
              id: firstRoom.id,
              name: firstRoom.title,
              price: firstRoom.price?.per_night_amount || 0,
              maxAdults: firstRoom.max_adults,
              maxChildren: firstRoom.max_children,
              maxInfants: firstRoom.max_infants,
              availableRooms: firstRoom.available_rooms,
              thumbnail: firstRoom.thumbnail,
              extra_adults_beyond_capacity: firstRoom.extra_adults_beyond_capacity,
              priceDetails: {
                ...firstRoom.price,
                amount: firstRoom.price?.amount || 0,
                original_amount: firstRoom.price?.original_amount || 0,
                currency_code: firstRoom.price?.currency_code || "USD",
                total_amount: firstRoom.price?.total_amount || 0,
                per_night_amount: firstRoom.price?.per_night_amount || 0,
                nights: firstRoom.price?.nights || 1,
                meal_plans: firstRoom.price?.meal_plans as Record<
                  MealPlanType,
                  MealPlanPrice
                >,
              },
            });

            // Update price based on the new data
            updatePriceFromRoomData(firstRoom, nights);
          }
        } else {
          console.warn(
            "No available rooms returned from API after guest change"
          );
        }
      } catch (error) {
        console.error(
          "Error fetching updated availability data after guest change:",
          error
        );
      }
    }
  };

  // Helper function to update price from room data
  const updatePriceFromRoomData = (roomData: any, nightsCount: number) => {
    if (
      roomData.price?.meal_plans &&
      roomData.price.meal_plans[selectedMealPlan]
    ) {
      const mealPlanPrice = roomData.price.meal_plans[selectedMealPlan];

      // If the API provides a total_amount, use it
      if (mealPlanPrice.total_amount) {
        setSubtotal(mealPlanPrice.total_amount);
      } else if (mealPlanPrice.per_night_amount) {
        // Otherwise calculate based on per_night_amount and nights
        const calculatedTotal = mealPlanPrice.per_night_amount * nightsCount;
        setSubtotal(calculatedTotal);
      }
    } else {
      // Fallback to calculated prices
      const currentPrice = roomData.price?.per_night_amount || 0;
      const newSubtotal = currentPrice * nightsCount;
      setSubtotal(newSubtotal);
    }
  };

  const handleMealPlanChange = (newMealPlan: MealPlanType) => {
    // Set the new meal plan immediately
    setSelectedMealPlan(newMealPlan);

    // Calculate current nights count
    let nightsCount = nights;
    if (startDate && endDate) {
      const startDay = new Date(
        startDate.getFullYear(),
        startDate.getMonth(),
        startDate.getDate()
      );
      const endDay = new Date(
        endDate.getFullYear(),
        endDate.getMonth(),
        endDate.getDate()
      );

      const diffTime = endDay.getTime() - startDay.getTime();
      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));
      nightsCount = Math.max(1, diffDays);
    }

    // Update price based on the selected meal plan without making API calls
    if (
      selectedRoom?.priceDetails?.meal_plans?.[
        newMealPlan as keyof typeof selectedRoom.priceDetails.meal_plans
      ]
    ) {
      const mealPlanPrice = selectedRoom.priceDetails.meal_plans[
        newMealPlan as keyof typeof selectedRoom.priceDetails.meal_plans
      ] as MealPlanPrice;

      // If the API provides a total_amount, use it
      if (mealPlanPrice?.total_amount) {
        setSubtotal(mealPlanPrice.total_amount);
      } else if (mealPlanPrice?.per_night_amount) {
        // Otherwise calculate based on per_night_amount and nights
        const calculatedTotal = mealPlanPrice.per_night_amount * nightsCount;
        setSubtotal(calculatedTotal);
      }
    } else {
      // Fallback to calculated prices
      const currentPrice = getCurrentRoomPrice();
      const newSubtotal = currentPrice * nightsCount;
      setSubtotal(newSubtotal);
    }

    // Calculate the new price for display
    let displayPrice = 0;

    if (startDate && endDate) {
      if (
        selectedRoom?.priceDetails?.meal_plans?.[
          newMealPlan as keyof typeof selectedRoom.priceDetails.meal_plans
        ]
      ) {
        const mealPlanPrice = selectedRoom.priceDetails.meal_plans[
          newMealPlan as keyof typeof selectedRoom.priceDetails.meal_plans
        ] as MealPlanPrice;

        // If the API provides a total_amount, use it
        if (mealPlanPrice?.total_amount) {
          displayPrice = mealPlanPrice.total_amount;
        } else if (mealPlanPrice?.per_night_amount) {
          // Otherwise calculate based on per_night_amount and nights
          displayPrice = mealPlanPrice.per_night_amount * nightsCount;
        }
      } else {
        // Fallback to calculated prices
        const currentPrice = getCurrentRoomPrice();
        displayPrice = currentPrice * nightsCount;
      }
    }

    // Force update the pricing summary display
    setTimeout(() => {
      const nightsTotalElement = document.getElementById("nights-total-price");
      const finalTotalElement = document.getElementById("final-total-price");

      if (nightsTotalElement && finalTotalElement) {
        const formattedPrice = formatCurrency(displayPrice, getCurrencyCode());
        const formattedTotalPrice = formatCurrency(
          displayPrice * roomQuantity,
          getCurrencyCode()
        );

        // Update the displayed prices
        nightsTotalElement.textContent = formattedPrice;
        finalTotalElement.textContent = formattedTotalPrice;
      }
    }, 50);
  };

  const handleBookNow = () => {
    // If dates aren't selected, show the date picker modal
    if (!startDate || !endDate) {
      setBookingModalOpen(true);
      return;
    }

    // Format dates for URL
    const checkInStr = formatDateForAPI(startDate);
    const checkOutStr = formatDateForAPI(endDate);

    // Get currency and guest count
    const currency = getCurrencyCode();
    const guestTotal = guests.adults + guests.children;

    // Redirect to review booking page instead of opening modal
    const summaryUrl = new URL("/review-booking", window.location.origin);

    // Add all required parameters
    summaryUrl.searchParams.append(
      "hotelId",
      hotel.uuid || hotel.id.toString()
    );
    summaryUrl.searchParams.append(
      "roomId",
      selectedRoom?.id?.toString() || ""
    );
    summaryUrl.searchParams.append("checkIn", checkInStr);
    summaryUrl.searchParams.append("checkOut", checkOutStr);
    summaryUrl.searchParams.append(
      "checkInTime",
      hotel.check_in_time || "14:00"
    );
    summaryUrl.searchParams.append(
      "checkOutTime",
      hotel.check_out_time || "11:00"
    );
    summaryUrl.searchParams.append("totalAmount", subtotal.toString());
    summaryUrl.searchParams.append("currencyCode", currency);
    summaryUrl.searchParams.append("guestCount", guestTotal.toString());
    summaryUrl.searchParams.append("infantCount", guests.infants.toString());
    summaryUrl.searchParams.append("mealPlan", selectedMealPlan);
    summaryUrl.searchParams.append("roomQuantity", roomQuantity.toString());

    // Add region ID if available
    if (hotel.region_id) {
      summaryUrl.searchParams.append("regionId", hotel.region_id);
    }

    // Navigate to the summary page
    window.location.href = summaryUrl.toString();

    // Generate a unique ID for the cart item (keeping this for reference)
    const cartItemId = `${hotel.id}_${
      selectedRoom?.id || ""
    }_${checkInStr}_${checkOutStr}_${Date.now()}`;

    // Create cart item
    const cartItem: CartItem = {
      id: cartItemId,
      hotelId: hotel.id.toString(),
      roomId: selectedRoom?.id?.toString() || "",
      roomType: selectedRoom?.name || "Standard Room",
      hotelName: hotel.name,
      checkIn: checkInStr,
      checkOut: checkOutStr,
      guests: guestTotal,
      infants: guests.infants,
      price: subtotal * roomQuantity, // Multiply price by quantity
      currency: currency,
      mealPlan: selectedMealPlan,
      addedAt: new Date().toISOString(),
      image: selectedRoom?.thumbnail || "",
      checkInTime: hotel.check_in_time || "14:00",
      checkOutTime: hotel.check_out_time || "11:00",
      regionId: "reg_01JP9R0NP6B5DXGDYHFSSW0FK1", // Adding default region_id
      available_rooms: selectedRoom?.availableRooms || 1, // Add available rooms count
      quantity: roomQuantity, // Add quantity of rooms being booked
    };

    // Add the item to the cart using our utility function
    addToCart(cartItem);

    window.location.href = "/cart";
  };

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Get currency code - convert symbols to ISO codes
  const getCurrencyCode = () => {
    if (selectedRoom?.priceDetails?.currency_code) {
      return ensureValidCurrencyCode(selectedRoom.priceDetails.currency_code);
    }
    return ensureValidCurrencyCode(hotel.currency || "USD");
  };

  // Helper function to ensure we have a valid ISO currency code
  const ensureValidCurrencyCode = (code: string): string => {
    // Map of common currency symbols to their ISO codes
    const symbolToCode: Record<string, string> = {
      $: "usd",
      "€": "eur",
      "£": "gbp",
      "¥": "jpy",
      "₹": "inr",
      "₽": "rub",
      "₣": "chf",
      kr: "sek",
      C$: "cad",
      A$: "aud",
    };

    // If it's a symbol, convert it to the ISO code (lowercase)
    if (symbolToCode[code]) {
      return symbolToCode[code];
    }

    // If it's already a valid 3-letter ISO code, convert to lowercase and return it
    if (/^[A-Z]{3}$/.test(code)) {
      return code.toLowerCase();
    }

    // Default to USD (lowercase) for any other case
    return "usd";
  };

  // Check if there are no available rooms
  const noRoomsAvailable = !availableRooms || availableRooms.length === 0;

  return (
    <div
      id="booking"
      className="bg-white border border-[#3566ab]/10 rounded-xl shadow-lg px-6 py-4 sticky top-6 backdrop-blur-sm"
    >
      {/* Booking Box Header */}
      <div className="mb-6 pb-4 border-b border-[#3566ab]/10">
        <h2 className="text-xl font-baskervville text-[#3566ab] mb-2">
          {selectedRoom ? "Book Your Stay" : "Select Your Room"}
        </h2>
        <p className="text-sm text-foreground/70">
          {selectedRoom
            ? `${hotel.name}, ${hotel.location}`
            : "Choose your dates and preferred room"}
        </p>
      </div>

      {noRoomsAvailable ? (
        <div className="my-6 text-center">
          <div className="w-16 h-16 rounded-full bg-gradient-to-br from-[#3566ab]/10 to-[#3566ab]/20 flex items-center justify-center mx-auto mb-4 shadow-md">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="text-[#3566ab]"
            >
              <circle cx="12" cy="12" r="10"></circle>
              <line x1="12" y1="8" x2="12" y2="12"></line>
              <line x1="12" y1="16" x2="12.01" y2="16"></line>
            </svg>
          </div>
          <div className="text-xl font-baskervville text-[#3566ab] mb-2">
            Not Available
          </div>
          <p className="text-sm text-foreground/70 mb-4">
            No rooms are available for the selected dates.
          </p>
          <button
            className="px-6 py-2.5 bg-[#3566ab] text-white rounded-md hover:bg-[#3566ab]/90 transition-colors font-medium flex items-center mx-auto"
            onClick={() => setDatePickerOpen(true)}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2"
            >
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            Change Dates
          </button>
        </div>
      ) : (
        /* Price Display */
        <div className="mb-6 mt-4 text-center">
          <div className="relative inline-block">
            <div className="absolute -top-3 -right-3 bg-green-100 text-green-800 text-xs font-karla rounded-full px-2 py-0.5 shadow-sm">
              Save 20%
            </div>
            <div className="text-3xl font-baskervville text-[#3566ab] relative">
              <span className="text-sm font-karla uppercase tracking-wider text-foreground/60 absolute -top-5 left-0">
                From
              </span>
              {formatCurrency(getCurrentRoomPrice(), getCurrencyCode())}
              <span className="text-base font-karla uppercase tracking-wider text-foreground/60 ml-1">
                per night
              </span>
            </div>
          </div>
          {selectedRoom?.priceDetails?.meal_plans &&
            selectedRoom.priceDetails.meal_plans[selectedMealPlan] && (
              <div className="text-sm text-[#3566ab]/80 mt-2 font-medium">
                {mealPlanLabels[selectedMealPlan]} plan included
              </div>
            )}
        </div>
      )}
      {/* Selected Room */}
      <div className="mb-6">
        <h3 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <path d="M2 3h20v10H2z"></path>
            <path d="M2 17h20v4H2z"></path>
          </svg>
          Selected Room
        </h3>
        <div className="bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-5 backdrop-blur-sm shadow-sm">
          {noRoomsAvailable ? (
            <div className="text-center py-4">
              <div className="w-12 h-12 rounded-full bg-white flex items-center justify-center mx-auto mb-3 shadow-md">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                  <line
                    x1="8"
                    y1="14"
                    x2="16"
                    y2="14"
                    strokeDasharray="2"
                  ></line>
                </svg>
              </div>
              <div className="text-sm text-[#3566ab] font-medium mb-1">
                No rooms available for the selected dates
              </div>
              <div className="text-xs text-foreground/60">
                Please try selecting different dates
              </div>
            </div>
          ) : (
            <>
              <div className="flex items-center">
                <div className="w-24 h-24 mr-4 rounded-xl overflow-hidden shadow-md relative group">
                  {selectedRoom && (
                    <>
                      <img
                        src={
                          selectedRoom.thumbnail ||
                          (selectedRoom.images && selectedRoom.images.length > 0
                            ? typeof selectedRoom.images[0] === "string"
                              ? selectedRoom.images[0]
                              : (selectedRoom.images[0] as any)?.url ||
                                "/images/room-placeholder.jpg"
                            : "/images/room-placeholder.jpg")
                        }
                        alt={selectedRoom.name}
                        className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700 ease-out"
                      />
                      <div className="absolute inset-0 bg-gradient-to-t from-black/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
                    </>
                  )}
                </div>
                <div>
                  <div className="font-baskervville text-lg text-[#3566ab] booking-box-room-type">
                    {selectedRoom?.name || "Deluxe Suite"}
                  </div>
                  <div className="flex items-center text-sm text-foreground/70 mt-1 bg-white/50 px-2 py-0.5 rounded-full inline-block">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab] mr-1"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                    </svg>
                    Up to{" "}
                    {selectedRoom?.maxGuests ||
                      (selectedRoom?.maxAdults || 0) +
                        (selectedRoom?.maxChildren || 0) +
                        (selectedRoom?.maxInfants || 0) ||
                      ""}{" "}
                    guests
                  </div>
                  {selectedRoom?.availableRooms &&
                    selectedRoom.availableRooms > 0 && (
                      <div className="text-sm text-white mt-2 bg-[#3566ab] px-2 py-0.5 rounded-full inline-block">
                        {selectedRoom.availableRooms}{" "}
                        {selectedRoom.availableRooms === 1 ? "room" : "rooms"}{" "}
                        available
                      </div>
                    )}
                </div>
              </div>

              {/* Room information only - Room Quantity moved to Guest section */}
            </>
          )}
        </div>
      </div>
      {/* Trip Dates */}
      <div className="mb-6">
        <h3 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
            <line x1="16" y1="2" x2="16" y2="6"></line>
            <line x1="8" y1="2" x2="8" y2="6"></line>
            <line x1="3" y1="10" x2="21" y2="10"></line>
          </svg>
          Your Trip Dates
        </h3>
        <div
          className="bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-5 shadow-sm hover:shadow-md transition-all duration-300 cursor-pointer transform hover:-translate-y-0.5"
          onClick={(e) => {
            e.preventDefault();
            setDatePickerOpen(true);
          }}
        >
          <div className="text-xs font-karla uppercase tracking-wider text-[#3566ab]/70 mb-2">
            Check In - Check Out
          </div>
          <div className="text-sm font-medium flex items-center">
            {startDate && endDate ? (
              <div className="flex flex-col sm:flex-row sm:items-center w-full">
                <div className="flex items-center bg-white/70 rounded-full px-3 py-1.5 shadow-sm mr-2 mb-2 sm:mb-0">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#3566ab] mr-2"
                  >
                    <rect
                      x="3"
                      y="4"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                  <span className="text-[#3566ab]">
                    {formatDate(startDate)}
                  </span>
                </div>
                <div className="hidden sm:block text-[#3566ab] mx-1">→</div>
                <div className="flex items-center bg-white/70 rounded-full px-3 py-1.5 shadow-sm">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#3566ab] mr-2"
                  >
                    <rect
                      x="3"
                      y="4"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                  <span className="text-[#3566ab]">{formatDate(endDate)}</span>
                </div>
              </div>
            ) : (
              <div className="flex items-center bg-white/70 rounded-full px-3 py-1.5 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab] mr-2"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
                <span className="text-[#3566ab]">Select dates</span>
              </div>
            )}
          </div>
        </div>

        {/* Calendar Modal */}
        <div
          id="date-picker-modal"
          className={`fixed inset-0 z-[9999] flex items-center justify-center bg-black/50 backdrop-blur-sm transition-opacity duration-300 ${
            datePickerOpen
              ? "opacity-100 pointer-events-auto"
              : "opacity-0 pointer-events-none"
          }`}
          onClick={() => setDatePickerOpen(false)}
          style={{
            position: "fixed",
            top: 0,
            left: 0,
            right: 0,
            bottom: 0,
            zIndex: 9999,
          }}
        >
          <div
            className="bg-white rounded-xl shadow-2xl max-w-md w-full transform transition-transform duration-300 scale-100 mmt-date-picker-popover"
            onClick={(e) => e.stopPropagation()}
            style={{
              position: "relative",
              maxHeight: "90vh",
              overflowY: "auto",
              margin: "20px",
            }}
          >
            <div className="flex justify-between items-center p-5 border-b">
              <h3 className="text-lg font-baskervville text-[#3566ab]">
                Select Your Dates
              </h3>
              <button
                onClick={() => setDatePickerOpen(false)}
                className="p-1.5 rounded-full hover:bg-[#3566ab]/10 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="20"
                  height="20"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>
            <DateRangePicker
              startDate={startDate}
              endDate={endDate}
              onChange={handleDateRangeChange}
              onDateRangeChange={handleDateRangeChange}
              onClose={() => setDatePickerOpen(false)}
              lang={lang}
            />
          </div>
        </div>

        <div className="flex justify-between items-center mt-3">
          <div className="text-sm bg-[#3566ab] text-white px-3 py-1 rounded-full inline-block shadow-sm">
            {startDate && endDate ? (
              <>
                {nights} {nights === 1 ? "night" : "nights"}
              </>
            ) : (
              "0 nights"
            )}
          </div>
          {startDate && endDate && (
            <div className="text-xs text-[#3566ab]/70">
              {new Date(startDate).toLocaleDateString("en-US", {
                month: "short",
              })}{" "}
              {new Date(startDate).getDate()} -{" "}
              {new Date(endDate).toLocaleDateString("en-US", {
                month: "short",
              })}{" "}
              {new Date(endDate).getDate()}, {new Date(endDate).getFullYear()}
            </div>
          )}
        </div>
      </div>
      {/* Guests */}
      <div className="mb-8">
        <h3 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
            <circle cx="9" cy="7" r="4"></circle>
            <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
            <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
          </svg>
          Guests
        </h3>
        <div className="bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-5 shadow-sm">
          <div className="flex justify-between items-center mb-5 pb-4 border-b border-[#3566ab]/10">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                  <circle cx="12" cy="7" r="4"></circle>
                </svg>
              </div>
              <div>
                <div className="text-sm font-medium">Adults</div>
                <div className="text-xs text-foreground/60">Ages 13+</div>
              </div>
            </div>
            <div className="flex items-center bg-white/50 rounded-full px-1 py-1 shadow-sm">
              <button
                onClick={() => {
                  if (guests.adults > 1) {
                    const newGuests = { ...guests, adults: guests.adults - 1 };
                    handleGuestChange(newGuests);
                  }
                }}
                disabled={guests.adults <= 1}
                className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
              <span className="w-10 text-center font-medium">
                {guests.adults}
              </span>
              <button
                onClick={() => {
                  if (
                    currentRoomCapacity.maxAdults !== 0 &&
                    guests.adults < (currentRoomCapacity.maxAdults || 4) &&
                    calculateTotalOccupancy(guests) <
                      currentRoomCapacity.maxOccupancy
                  ) {
                    const newGuests = { ...guests, adults: guests.adults + 1 };
                    handleGuestChange(newGuests);
                  }
                }}
                disabled={
                  currentRoomCapacity.maxAdults === 0 ||
                  guests.adults >= (currentRoomCapacity.maxAdults || 4) ||
                  calculateTotalOccupancy(guests) >=
                    currentRoomCapacity.maxOccupancy
                }
                className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
            </div>
          </div>

          <div className="flex justify-between items-center mb-5 pb-4 border-b border-[#3566ab]/10">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <path d="M8 10l4-4 4 4"></path>
                  <path d="M12 6v8"></path>
                  <path d="M20 12v6a2 2 0 0 1-2 2H6a2 2 0 0 1-2-2v-6"></path>
                </svg>
              </div>
              <div>
                <div className="text-sm font-medium">Children</div>
                <div className="text-xs text-foreground/60">Ages 2-12</div>
              </div>
            </div>
            <div className="flex items-center bg-white/50 rounded-full px-1 py-1 shadow-sm">
              <button
                onClick={() => {
                  if (guests.children > 0) {
                    const newGuests = {
                      ...guests,
                      children: guests.children - 1,
                    };
                    handleGuestChange(newGuests);
                  }
                }}
                disabled={guests.children <= 0}
                className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
              <span className="w-10 text-center font-medium">
                {guests.children}
              </span>
              <button
                onClick={() => {
                  if (
                    currentRoomCapacity.maxChildren !== 0 &&
                    guests.children < (currentRoomCapacity.maxChildren || 2) &&
                    calculateTotalOccupancy(guests) <
                      currentRoomCapacity.maxOccupancy
                  ) {
                    const newGuests = {
                      ...guests,
                      children: guests.children + 1,
                    };
                    handleGuestChange(newGuests);
                  }
                }}
                disabled={
                  currentRoomCapacity.maxChildren === 0 ||
                  guests.children >= (currentRoomCapacity.maxChildren || 2) ||
                  calculateTotalOccupancy(guests) >=
                    currentRoomCapacity.maxOccupancy
                }
                className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
            </div>
          </div>

          <div className="flex justify-between items-center">
            <div className="flex items-center">
              <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <path d="M9 12h.01"></path>
                  <path d="M15 12h.01"></path>
                  <path d="M10 16c.5.3 1.5.5 2 .5s1.5-.2 2-.5"></path>
                  <path d="M19 6.3a9 9 0 0 1 1.8 3.9 2 2 0 0 1 0 3.6 9 9 0 0 1-17.6 0 2 2 0 0 1 0-3.6A9 9 0 0 1 12 3c2 0 3.5 1.1 3.5 2.5s-.9 2.5-2 2.5c-.8 0-1.5-.4-1.5-1"></path>
                </svg>
              </div>
              <div>
                <div className="text-sm font-medium">Infants</div>
                <div className="text-xs text-foreground/60">Under 2</div>
              </div>
            </div>
            <div className="flex items-center bg-white/50 rounded-full px-1 py-1 shadow-sm">
              <button
                onClick={() => {
                  if (guests.infants > 0) {
                    const newGuests = {
                      ...guests,
                      infants: guests.infants - 1,
                    };
                    handleGuestChange(newGuests);
                  }
                }}
                disabled={guests.infants <= 0}
                className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
              <span className="w-10 text-center font-medium">
                {guests.infants}
              </span>
              <button
                onClick={() => {
                  if (
                    currentRoomCapacity.maxInfants !== 0 &&
                    guests.infants < (currentRoomCapacity.maxInfants || 2) &&
                    calculateTotalOccupancy(guests) <
                      currentRoomCapacity.maxOccupancy
                  ) {
                    const newGuests = {
                      ...guests,
                      infants: guests.infants + 1,
                    };
                    handleGuestChange(newGuests);
                  }
                }}
                disabled={
                  currentRoomCapacity.maxInfants === 0 ||
                  guests.infants >= (currentRoomCapacity.maxInfants || 2) ||
                  calculateTotalOccupancy(guests) >=
                    currentRoomCapacity.maxOccupancy
                }
                className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab]"
                >
                  <line x1="12" y1="5" x2="12" y2="19"></line>
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                </svg>
              </button>
            </div>
          </div>

          {/* Room Quantity Selector */}
          {selectedRoom?.availableRooms && selectedRoom.availableRooms >= 1 && (
            <div className="flex justify-between items-center mb-5 pb-4 border-b border-[#3566ab]/10">
              <div className="flex items-center">
                <div className="w-10 h-10 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#3566ab]"
                  >
                    <path d="M3 3h18v10H3z"></path>
                    <path d="M3 16h18"></path>
                    <path d="M8 16v5"></path>
                    <path d="M16 16v5"></path>
                    <path d="M10 6h.01"></path>
                    <path d="M14 6h.01"></path>
                    <path d="M10 10h.01"></path>
                    <path d="M14 10h.01"></path>
                  </svg>
                </div>
                <div>
                  <div className="text-sm font-medium">Rooms</div>
                  <div className="text-xs text-foreground/60">
                    {selectedRoom.availableRooms} available
                  </div>
                </div>
              </div>
              <div className="flex items-center bg-white/50 rounded-full px-1 py-1 shadow-sm">
                <button
                  onClick={() => {
                    if (roomQuantity > 1) {
                      setRoomQuantity(roomQuantity - 1);
                    }
                  }}
                  disabled={roomQuantity <= 1}
                  className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#3566ab]"
                  >
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
                <span className="w-10 text-center font-medium">
                  {roomQuantity}
                </span>
                <button
                  onClick={() => {
                    if (roomQuantity < (selectedRoom.availableRooms || 1)) {
                      setRoomQuantity(roomQuantity + 1);
                    }
                  }}
                  disabled={roomQuantity >= (selectedRoom.availableRooms || 1)}
                  className="w-8 h-8 flex items-center justify-center border border-[#3566ab]/10 rounded-full disabled:opacity-50 hover:bg-[#3566ab]/5 transition-colors"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#3566ab]"
                  >
                    <line x1="12" y1="5" x2="12" y2="19"></line>
                    <line x1="5" y1="12" x2="19" y2="12"></line>
                  </svg>
                </button>
              </div>
            </div>
          )}

          <div className="mt-5 pt-4 border-t border-[#3566ab]/10 text-center">
            <div className="text-sm text-[#3566ab] font-medium">
              Total guests: {guests.adults + guests.children + guests.infants}
            </div>
            <div className="text-xs text-foreground/60 mt-1">
              Maximum occupancy: {currentRoomCapacity.maxOccupancy}
            </div>
            {roomQuantity > 1 && (
              <div className="text-xs text-[#3566ab] font-medium mt-2 bg-[#3566ab]/10 rounded-full px-3 py-1 inline-block">
                {roomQuantity} rooms ×{" "}
                {guests.adults + guests.children + guests.infants} guests
              </div>
            )}
          </div>
        </div>
      </div>
      {/* Meal Plan Options - Only show when rooms are available */}
      {!noRoomsAvailable && (
        <MealPlanSelector
          selectedRoom={selectedRoom}
          selectedMealPlan={selectedMealPlan}
          onMealPlanChange={handleMealPlanChange}
          formatCurrency={formatCurrency}
          getCurrencyCode={getCurrencyCode}
        />
      )}
      {/* Price Summary */}
      <div className="mb-8 bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-5 shadow-sm">
        <h3 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] flex items-center">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
            className="mr-2"
          >
            <circle cx="12" cy="12" r="10"></circle>
            <path d="M16 8h-6.5a2.5 2.5 0 0 0 0 5h3a2.5 2.5 0 0 1 0 5H6"></path>
            <path d="M12 18v2"></path>
            <path d="M12 6v2"></path>
          </svg>
          Price Summary
        </h3>

        {noRoomsAvailable ? (
          <div className="text-center py-5">
            <div className="w-16 h-16 rounded-full bg-white flex items-center justify-center mx-auto mb-4 shadow-md">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#3566ab]"
              >
                <circle cx="12" cy="12" r="10"></circle>
                <path d="M16 8h-6.5a2.5 2.5 0 0 0 0 5h3a2.5 2.5 0 0 1 0 5H6"></path>
                <path d="M12 18v2"></path>
                <path d="M12 6v2"></path>
              </svg>
            </div>
            <div className="text-sm text-[#3566ab] font-medium mb-2">
              No pricing available
            </div>
            <div className="text-xs text-foreground/60">
              Please select different dates to see available rates
            </div>
          </div>
        ) : (
          <>
            <div className="flex justify-between mb-4 pb-3 border-b border-[#3566ab]/10">
              <div className="flex items-center">
                <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-2 shadow-sm">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="text-[#3566ab]"
                  >
                    <rect
                      x="3"
                      y="4"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="16" y1="2" x2="16" y2="6"></line>
                    <line x1="8" y1="2" x2="8" y2="6"></line>
                    <line x1="3" y1="10" x2="21" y2="10"></line>
                  </svg>
                </div>
                <span className="text-sm">
                  {formatCurrency(getCurrentRoomPrice(), getCurrencyCode())} ×{" "}
                  {startDate && endDate ? nights : 0}{" "}
                  {startDate && endDate && nights === 1 ? "night" : "nights"}
                  {selectedRoom?.priceDetails?.meal_plans &&
                    selectedRoom.priceDetails.meal_plans[selectedMealPlan] && (
                      <span className="block text-xs text-foreground/70 mt-1">
                        {mealPlanLabels[selectedMealPlan]} plan included
                      </span>
                    )}
                </span>
              </div>
              <span
                className="text-sm font-medium nights-total bg-white/70 px-3 py-1 rounded-full shadow-sm"
                id="nights-total-price"
              >
                {formatCurrency(
                  startDate && endDate ? subtotal : 0,
                  getCurrencyCode()
                )}
              </span>
            </div>

            {/* Room quantity calculation */}
            {roomQuantity > 1 && (
              <div className="flex justify-between mb-4 pb-3 border-b border-[#3566ab]/10">
                <div className="flex items-center">
                  <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-2 shadow-sm">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab]"
                    >
                      <path d="M2 3h20v10H2z"></path>
                      <path d="M2 17h20v4H2z"></path>
                    </svg>
                  </div>
                  <span className="text-sm">{roomQuantity} rooms</span>
                </div>
                <span className="text-sm font-medium bg-white/70 px-3 py-1 rounded-full shadow-sm">
                  {formatCurrency(
                    startDate && endDate ? subtotal * roomQuantity : 0,
                    getCurrencyCode()
                  )}
                </span>
              </div>
            )}

            <div className="flex justify-between font-medium pt-3 mt-3 bg-white/70 px-4 py-3 rounded-xl shadow-sm">
              <span className="text-[#3566ab] flex items-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="mr-2"
                >
                  <line x1="12" y1="1" x2="12" y2="23"></line>
                  <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"></path>
                </svg>
                Total
              </span>
              <span
                className="final-total text-[#3566ab] text-lg"
                id="final-total-price"
              >
                {formatCurrency(
                  startDate && endDate ? subtotal * roomQuantity : 0,
                  getCurrencyCode()
                )}
              </span>
            </div>
          </>
        )}
      </div>
      {/* Book Now Button */}
      <div className="mt-8">
        <button
          onClick={handleBookNow}
          disabled={noRoomsAvailable}
          className={`w-full py-4 px-6 font-karla uppercase tracking-wider shadow-lg transition-all duration-300 rounded-xl ${
            noRoomsAvailable
              ? "bg-gray-400 text-white cursor-not-allowed opacity-70"
              : "bg-gradient-to-r from-[#3566ab] to-[#2a5089] text-white hover:shadow-xl transform hover:-translate-y-0.5"
          }`}
        >
          {noRoomsAvailable ? "Not Available" : "Book Now"}
        </button>
        <p className="text-xs text-center text-foreground/60 mt-3">
          {!noRoomsAvailable && "You won't be charged yet"}
        </p>
      </div>
      {/* Booking Modal - Only used when dates aren't selected */}
      {!startDate || !endDate
        ? // If dates aren't selected, show an alert when trying to book
          bookingModalOpen && (
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
              <div className="bg-white p-6 rounded-lg shadow-xl max-w-md w-full">
                <h3 className="text-xl font-baskervville mb-4">
                  Please Select Dates
                </h3>
                <p className="mb-6">
                  You need to select check-in and check-out dates before
                  booking.
                </p>
                <div className="flex justify-end">
                  <button
                    onClick={() => {
                      setBookingModalOpen(false);
                      setDatePickerOpen(true);
                    }}
                    className="px-4 py-2 bg-[#285DA6] text-white rounded-md mr-2"
                  >
                    Select Dates
                  </button>
                  <button
                    onClick={() => setBookingModalOpen(false)}
                    className="px-4 py-2 border border-[#285DA6] text-[#285DA6] rounded-md"
                  >
                    Cancel
                  </button>
                </div>
              </div>
            </div>
          )
        : null}
    </div>
  );
};

export default EnhancedBookingBox;
