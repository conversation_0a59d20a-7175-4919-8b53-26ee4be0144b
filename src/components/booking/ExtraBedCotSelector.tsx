import React, { useState, useEffect } from "react";
import {
  fetchExtraBeds,
  type ExtraBedResponse,
} from "../../utils/store/hotels";
import { formatDateForAPI } from "../../utils/dateUtils";
import { getCurrentCurrencyCode } from "../../utils/currencyHelper";

interface ExtraBedCotSelectorProps {
  roomConfigId: string;
  checkIn: string;
  checkOut: string;
  onExtraBedChange: (
    quantity: number,
    totalPrice: number,
    totalPricePerBed?: number
  ) => void;
  onCotChange: (
    quantity: number,
    totalPrice: number,
    totalPricePerCot?: number
  ) => void;
  onExtraAdultChange: (
    quantity: number,
    totalPrice: number,
    totalPricePerAdult?: number
  ) => void;
  initialExtraAdultQuantity?: number;
  showExtraAdults?: boolean; // New prop to control Extra Adults visibility
  initialExtraBedQuantity?: number;
  initialCotQuantity?: number;
  infantCount?: number; // New prop to track number of infants
  // New props for API-provided extra adult pricing
  extraAdultPricing?: {
    count: number;
    per_night_amount: number;
    currency_code: string;
  };
  nights?: number; // Number of nights for calculating total price
}

const ExtraBedCotSelector: React.FC<ExtraBedCotSelectorProps> = ({
  roomConfigId,
  checkIn,
  checkOut,
  onExtraBedChange,
  onCotChange,
  onExtraAdultChange,
  initialExtraAdultQuantity = 0,
  showExtraAdults = true, // Default to true for backward compatibility
  initialExtraBedQuantity = 0,
  initialCotQuantity = 0,
  infantCount = 0,
  extraAdultPricing,
  nights = 1,
}) => {
  const [data, setData] = useState<ExtraBedResponse | null>(null);
  const [selectedExtraBedQuantity, setSelectedExtraBedQuantity] = useState(0);
  const [selectedCotQuantity, setSelectedCotQuantity] = useState(0);
  const [selectedExtraAdultQuantity, setSelectedExtraAdultQuantity] = useState(
    initialExtraAdultQuantity
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch data when component mounts
  useEffect(() => {
    const loadData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Convert dates to API format if needed
        const apiCheckIn = checkIn.includes("-")
          ? checkIn
          : formatDateForAPI(new Date(checkIn));
        const apiCheckOut = checkOut.includes("-")
          ? checkOut
          : formatDateForAPI(new Date(checkOut));

        const response = await fetchExtraBeds(
          roomConfigId,
          apiCheckIn,
          apiCheckOut,
          getCurrentCurrencyCode()
        );
        setData(response);
      } catch (err) {
        console.error("Error loading extra bed/cot data:", err);
        setError("Failed to load extra bed and cot information");
      } finally {
        setLoading(false);
      }
    };

    if (roomConfigId && checkIn && checkOut) {
      loadData();
    }
  }, [roomConfigId, checkIn, checkOut]);

  // Trigger callbacks when data is loaded and we have initial quantities
  useEffect(() => {
    // Trigger extra adult callback if we have initial quantity and pricing data
    if (initialExtraAdultQuantity > 0 && extraAdultPricing) {
      const totalPricePerAdult = extraAdultPricing.per_night_amount * nights;
      const totalPrice = initialExtraAdultQuantity * totalPricePerAdult;
      onExtraAdultChange(
        initialExtraAdultQuantity,
        totalPrice,
        totalPricePerAdult
      );
    } else if (initialExtraAdultQuantity > 0 && data) {
      // Fallback to API data if extraAdultPricing is not provided (backward compatibility)
      const totalPrice =
        initialExtraAdultQuantity *
        data.extra_adults_beyond_capacity_pricing.total_price_per_adult;
      onExtraAdultChange(
        initialExtraAdultQuantity,
        totalPrice,
        data.extra_adults_beyond_capacity_pricing.total_price_per_adult
      );
    }
  }, [data, initialExtraAdultQuantity, onExtraAdultChange, extraAdultPricing, nights]);

  // Handle extra bed quantity change
  const handleExtraBedQuantityChange = (quantity: number) => {
    setSelectedExtraBedQuantity(quantity);
    const totalPrice = data
      ? quantity * data.extra_bed_pricing.total_price_per_bed
      : 0;
    const totalPricePerBed = data?.extra_bed_pricing.total_price_per_bed || 0;
    console.log("Extra bed calculation:", {
      quantity,
      totalPricePerBed: totalPricePerBed,
      totalPrice,
    });
    onExtraBedChange(quantity, totalPrice, totalPricePerBed);
  };

  // Handle cot quantity change
  const handleCotQuantityChange = (quantity: number) => {
    setSelectedCotQuantity(quantity);
    const totalPrice = data
      ? quantity * data.cot_pricing.total_price_per_cot
      : 0;
    const totalPricePerCot = data?.cot_pricing.total_price_per_cot || 0;
    console.log("Cot calculation:", {
      quantity,
      totalPricePerCot: totalPricePerCot,
      totalPrice,
    });
    onCotChange(quantity, totalPrice, totalPricePerCot);
  };

  // Handle extra adult beyond capacity quantity change
  const handleExtraAdultQuantityChange = (quantity: number) => {
    setSelectedExtraAdultQuantity(quantity);

    let totalPrice = 0;
    let totalPricePerAdult = 0;

    if (extraAdultPricing) {
      // Use API-provided pricing from room availability response
      totalPricePerAdult = extraAdultPricing.per_night_amount * nights;
      totalPrice = quantity * totalPricePerAdult;
    } else if (data) {
      // Fallback to separate API data (backward compatibility)
      totalPrice = quantity * data.extra_adults_beyond_capacity_pricing.total_price_per_adult;
      totalPricePerAdult = data.extra_adults_beyond_capacity_pricing.total_price_per_adult;
    }

    console.log("Extra adult calculation:", {
      quantity,
      totalPricePerAdult: totalPricePerAdult,
      totalPrice,
      extraAdultPricing,
      nights,
    });
    onExtraAdultChange(quantity, totalPrice, totalPricePerAdult);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white border border-red-200 rounded-lg shadow-sm p-6">
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  // No extra beds, cots, or extra adults available
  if (
    !data ||
    (!data.extra_beds_available &&
      !data.cots_available &&
      !data.extra_adults_beyond_capacity_available)
  ) {
    return null; // Don't render anything if none are available
  }
  // No extra beds or cots available (considering infant count for cots)
  if (!data || (!data.extra_beds_available && (!data.cots_available || infantCount === 0))) {
    return null; // Don't render anything if neither are available
  }

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 overflow-hidden">
      <div className="p-4 space-y-5">
        {/* Extra Beds Section */}
        {data.extra_beds_available && (
          <div className="group bg-gradient-to-r from-gray-50/50 to-[#3566ab]/5 rounded-lg p-4 border border-gray-100 hover:border-[#3566ab]/20 transition-all duration-300">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-[#3566ab]/10 rounded-lg flex items-center justify-center group-hover:bg-[#3566ab]/20 transition-colors duration-200">
                  <svg
                    className="w-5 h-5 text-[#3566ab]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <rect
                      x="2"
                      y="3"
                      width="20"
                      height="14"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="8" y1="21" x2="16" y2="21"></line>
                    <line x1="12" y1="17" x2="12" y2="21"></line>
                  </svg>
                </div>
                <div>
                  <h4 className="text-base font-bold text-gray-900 mb-1">
                    Extra Beds
                  </h4>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    Additional beds for extra comfort and space
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center bg-white rounded-md border border-gray-200 shadow-sm">
                  <button
                    onClick={() =>
                      handleExtraBedQuantityChange(
                        Math.max(0, selectedExtraBedQuantity - 1)
                      )
                    }
                    disabled={selectedExtraBedQuantity <= 0}
                    className="w-7 h-7 rounded-l-md border-r border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:text-[#3566ab] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M5 12h14"></path>
                    </svg>
                  </button>

                  <span className="w-8 h-7 flex items-center justify-center font-bold text-gray-900 bg-gray-50 text-sm">
                    {selectedExtraBedQuantity}
                  </span>

                  <button
                    onClick={() =>
                      handleExtraBedQuantityChange(
                        Math.min(
                          data.max_extra_beds,
                          selectedExtraBedQuantity + 1
                        )
                      )
                    }
                    disabled={selectedExtraBedQuantity >= data.max_extra_beds}
                    className="w-7 h-7 rounded-r-md border-l border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:text-[#3566ab] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 5v14"></path>
                      <path d="M5 12h14"></path>
                    </svg>
                  </button>
                </div>

                <div className="text-right bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg border border-[#3566ab]/10 shadow-sm">
                  <div className="text-lg font-bold text-[#3566ab]">
                    {data.extra_bed_pricing.currency_code}{" "}
                    {formatCurrency(data.extra_bed_pricing.total_price_per_bed)}
                  </div>
                  <div className="text-xs text-gray-500 text-center font-medium">
                    per bed
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Cots Section - Only show if cots are available AND there are infants */}
        {data.cots_available && infantCount > 0 && (
          <div className="group bg-gradient-to-r from-gray-50/50 to-[#3566ab]/5 rounded-lg p-4 border border-gray-100 hover:border-[#3566ab]/20 transition-all duration-300">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-[#3566ab]/10 rounded-lg flex items-center justify-center group-hover:bg-[#3566ab]/20 transition-colors duration-200">
                  <svg
                    className="w-5 h-5 text-[#3566ab]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <rect
                      x="3"
                      y="8"
                      width="18"
                      height="12"
                      rx="2"
                      ry="2"
                    ></rect>
                    <path d="M7 8V6a2 2 0 0 1 2-2h6a2 2 0 0 1 2 2v2"></path>
                    <circle cx="8" cy="14" r="1"></circle>
                    <circle cx="16" cy="14" r="1"></circle>
                  </svg>
                </div>
                <div>
                  <h4 className="text-base font-bold text-gray-900 mb-1">
                    Baby Cots
                  </h4>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    Safe and comfortable cots for your little ones
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center bg-white rounded-md border border-gray-200 shadow-sm">
                  <button
                    onClick={() =>
                      handleCotQuantityChange(
                        Math.max(0, selectedCotQuantity - 1)
                      )
                    }
                    disabled={selectedCotQuantity <= 0}
                    className="w-7 h-7 rounded-l-md border-r border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:text-[#3566ab] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M5 12h14"></path>
                    </svg>
                  </button>

                  <span className="w-8 h-7 flex items-center justify-center font-bold text-gray-900 bg-gray-50 text-sm">
                    {selectedCotQuantity}
                  </span>

                  <button
                    onClick={() =>
                      handleCotQuantityChange(
                        Math.min(data.max_cots, selectedCotQuantity + 1)
                      )
                    }
                    disabled={selectedCotQuantity >= data.max_cots}
                    className="w-7 h-7 rounded-r-md border-l border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:text-[#3566ab] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 5v14"></path>
                      <path d="M5 12h14"></path>
                    </svg>
                  </button>
                </div>

                <div className="text-right bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg border border-[#3566ab]/10 shadow-sm">
                  <div className="text-lg font-bold text-[#3566ab]">
                    {data.cot_pricing.currency_code}{" "}
                    {formatCurrency(data.cot_pricing.total_price_per_cot)}
                  </div>
                  <div className="text-xs text-center text-gray-500 font-medium">
                    per cot
                  </div>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Extra Adults Beyond Capacity Section */}
        {/* {showExtraAdults && data.extra_adults_beyond_capacity_available && (
          <div className="group bg-gradient-to-r from-gray-50/50 to-[#3566ab]/5 rounded-lg p-4 border border-gray-100 hover:border-[#3566ab]/20 transition-all duration-300">
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-start gap-3">
                <div className="w-10 h-10 bg-[#3566ab]/10 rounded-lg flex items-center justify-center group-hover:bg-[#3566ab]/20 transition-colors duration-200">
                  <svg
                    className="w-5 h-5 text-[#3566ab]"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                </div>
                <div>
                  <h4 className="text-base font-bold text-gray-900 mb-1">
                    Extra Adults
                  </h4>
                  <p className="text-xs text-gray-600 leading-relaxed">
                    Additional adult guests beyond room capacity
                  </p>
                </div>
              </div>
              <div className="flex items-center gap-3">
                <div className="flex items-center bg-white rounded-md border border-gray-200 shadow-sm">
                  <button
                    onClick={() =>
                      handleExtraAdultQuantityChange(
                        Math.max(0, selectedExtraAdultQuantity - 1)
                      )
                    }
                    disabled={selectedExtraAdultQuantity <= 0}
                    className="w-7 h-7 rounded-l-md border-r border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:text-[#3566ab] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M5 12h14"></path>
                    </svg>
                  </button>

                  <span className="w-8 h-7 flex items-center justify-center font-bold text-gray-900 bg-gray-50 text-sm">
                    {selectedExtraAdultQuantity}
                  </span>

                  <button
                    onClick={() =>
                      handleExtraAdultQuantityChange(
                        Math.min(
                          data.max_adults_beyond_capacity,
                          selectedExtraAdultQuantity + 1
                        )
                      )
                    }
                    disabled={
                      selectedExtraAdultQuantity >=
                      data.max_adults_beyond_capacity
                    }
                    className="w-7 h-7 rounded-r-md border-l border-gray-200 flex items-center justify-center text-gray-600 hover:bg-gray-50 hover:text-[#3566ab] disabled:opacity-50 disabled:cursor-not-allowed transition-all duration-200"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M12 5v14"></path>
                      <path d="M5 12h14"></path>
                    </svg>
                  </button>
                </div>

                <div className="text-right bg-white/80 backdrop-blur-sm px-3 py-2 rounded-lg border border-[#3566ab]/10 shadow-sm">
                  <div className="text-lg font-bold text-[#3566ab]">
                    {extraAdultPricing ? (
                      <>
                        {extraAdultPricing.currency_code}{" "}
                        {formatCurrency(extraAdultPricing.per_night_amount * nights)}
                      </>
                    ) : (
                      <>
                        {data.extra_adults_beyond_capacity_pricing.currency_code}{" "}
                        {formatCurrency(
                          data.extra_adults_beyond_capacity_pricing
                            .total_price_per_adult
                        )}
                      </>
                    )}
                  </div>
                  <div className="text-xs text-center text-gray-500 font-medium">
                    per adult
                  </div>
                </div>
              </div>
            </div>
          </div>
        )} */}
      </div>
    </div>
  );
};

export default ExtraBedCotSelector;
