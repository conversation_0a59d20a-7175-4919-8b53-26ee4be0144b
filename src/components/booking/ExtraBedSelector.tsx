import React, { useState, useEffect } from "react";
import {
  fetchExtraBeds,
  type ExtraBedResponse,
} from "../../utils/store/hotels";
import { formatDateForAPI } from "../../utils/dateUtils";
import { getCurrentCurrencyCode } from "../../utils/currencyHelper";

interface ExtraBedSelectorProps {
  roomConfigId: string;
  checkIn: string;
  checkOut: string;
  onExtraBedChange: (quantity: number, totalPrice: number) => void;
  onCotChange: (quantity: number, totalPrice: number) => void;
  initialExtraBedQuantity?: number;
  initialCotQuantity?: number;
}

const ExtraBedSelector: React.FC<ExtraBedSelectorProps> = ({
  roomConfigId,
  checkIn,
  checkOut,
  onExtraBedChange,
  onCotChange,
  initialExtraBedQuantity = 0,
  initialCotQuantity = 0,
}) => {
  const [extraBedData, setExtraBedData] = useState<ExtraBedResponse | null>(
    null
  );
  const [selectedExtraBedQuantity, setSelectedExtraBedQuantity] = useState(
    initialExtraBedQuantity
  );
  const [selectedCotQuantity, setSelectedCotQuantity] =
    useState(initialCotQuantity);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Fetch extra bed data when component mounts
  useEffect(() => {
    const loadExtraBedData = async () => {
      try {
        setLoading(true);
        setError(null);

        // Convert dates to API format if needed
        const apiCheckIn = checkIn.includes("-")
          ? checkIn
          : formatDateForAPI(new Date(checkIn));
        const apiCheckOut = checkOut.includes("-")
          ? checkOut
          : formatDateForAPI(new Date(checkOut));

        const data = await fetchExtraBeds(
          roomConfigId,
          apiCheckIn,
          apiCheckOut,
          getCurrentCurrencyCode()
        );
        setExtraBedData(data);
      } catch (err) {
        console.error("Error loading extra bed data:", err);
        setError("Failed to load extra bed information");
      } finally {
        setLoading(false);
      }
    };

    if (roomConfigId && checkIn && checkOut) {
      loadExtraBedData();
    }
  }, [roomConfigId, checkIn, checkOut]);

  // Handle extra bed quantity change
  const handleExtraBedQuantityChange = (quantity: number) => {
    setSelectedExtraBedQuantity(quantity);
    const totalPrice = extraBedData
      ? quantity * extraBedData.extra_bed_pricing.total_price_per_bed
      : 0;
    onExtraBedChange(quantity, totalPrice);
  };

  // Handle cot quantity change
  const handleCotQuantityChange = (quantity: number) => {
    setSelectedCotQuantity(quantity);
    const totalPrice = extraBedData
      ? quantity * extraBedData.cot_pricing.total_price_per_cot
      : 0;
    onCotChange(quantity, totalPrice);
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "decimal",
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Loading state
  if (loading) {
    return (
      <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-gray-200 rounded w-1/3 mb-4"></div>
          <div className="h-3 bg-gray-200 rounded w-2/3"></div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="bg-white border border-red-200 rounded-lg shadow-sm p-6">
        <p className="text-red-600 text-sm">{error}</p>
      </div>
    );
  }

  // No extra beds available
  if (!extraBedData || !extraBedData.extra_beds_available) {
    return null; // Don't render anything if extra beds are not available
  }

  return (
    <div className="bg-white border border-[#3566ab]/10 rounded-lg shadow-sm p-6">
      <div className="flex items-start justify-between mb-4">
        <div>
          <h3 className="text-lg font-bold text-gray-900 mb-1">Extra Beds</h3>
          <p className="text-sm text-gray-600">
            Add extra beds to your room for additional comfort
          </p>
        </div>
        <div className="text-right">
          <div className="text-lg font-bold text-[#3566ab]">
            {extraBedData.extra_bed_pricing.currency_code}{" "}
            {formatCurrency(extraBedData.extra_bed_pricing.total_price_per_bed)}
          </div>
          <div className="text-xs text-gray-500">per bed</div>
        </div>
      </div>

      {/* Quantity Selector */}
      <div className="flex items-center justify-between">
        <div className="text-sm text-gray-600">
          Maximum {extraBedData.max_extra_beds} extra bed
          {extraBedData.max_extra_beds > 1 ? "s" : ""} available
        </div>

        <div className="flex items-center space-x-3">
          <button
            onClick={() =>
              handleExtraBedQuantityChange(
                Math.max(0, selectedExtraBedQuantity - 1)
              )
            }
            disabled={selectedExtraBedQuantity <= 0}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M5 12h14"></path>
            </svg>
          </button>

          <span className="w-8 text-center font-medium text-gray-900">
            {selectedExtraBedQuantity}
          </span>

          <button
            onClick={() =>
              handleExtraBedQuantityChange(
                Math.min(
                  extraBedData.max_extra_beds,
                  selectedExtraBedQuantity + 1
                )
              )
            }
            disabled={selectedExtraBedQuantity >= extraBedData.max_extra_beds}
            className="w-8 h-8 rounded-full border border-gray-300 flex items-center justify-center text-gray-600 hover:bg-gray-50 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <path d="M12 5v14"></path>
              <path d="M5 12h14"></path>
            </svg>
          </button>
        </div>
      </div>
    </div>
  );
};

export default ExtraBedSelector;
