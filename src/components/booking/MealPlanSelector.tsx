import React, { useEffect, useRef } from "react";

export type MealPlanType = "none" | "bb" | "hb" | "fb";

interface MealPlanSelectorProps {
  selectedRoom: any;
  selectedMealPlan: MealPlanType;
  onMealPlanChange: (mealPlan: MealPlanType) => void;
  formatCurrency: (amount: number, currency: string) => string;
  getCurrencyCode: () => string;
}

// Get meal plan label from API response or fallback to default
const getMealPlanLabel = (selectedRoom: any, mealPlanKey: MealPlanType): string => {
  try {
    // Check if the selected room has meal plan data with labels
    if (selectedRoom?.priceDetails?.meal_plans) {
      const mealPlan = selectedRoom.priceDetails.meal_plans[mealPlanKey];
      if (mealPlan && typeof mealPlan === "object" && mealPlan.label) {
        return mealPlan.label;
      }
    }

    // Check price structure (new API format)
    if (selectedRoom?.price?.meal_plans) {
      const mealPlan = selectedRoom.price.meal_plans[mealPlanKey];
      if (mealPlan && typeof mealPlan === "object" && mealPlan.label) {
        return mealPlan.label;
      }
    }

    // Fallback to hardcoded labels
    const fallbackLabels: Record<MealPlanType, string> = {
      none: "Room Only",
      bb: "Bed & Breakfast",
      hb: "Half Board",
      fb: "Full Board",
    };

    return fallbackLabels[mealPlanKey] || `Meal Plan ${mealPlanKey}`;
  } catch (error) {
    console.error("Error getting meal plan label:", error);
    return `Meal Plan ${mealPlanKey}`;
  }
};

// Order matters - 'none' is the first item and should be the default
const mealPlanKeys: MealPlanType[] = ["none", "bb", "hb", "fb"];

// Icons for meal plans
const MealIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
    <path d="M7 2v20"></path>
    <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
  </svg>
);

const BreakfastIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
    <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
    <line x1="6" y1="1" x2="6" y2="4"></line>
    <line x1="10" y1="1" x2="10" y2="4"></line>
    <line x1="14" y1="1" x2="14" y2="4"></line>
  </svg>
);

const LunchIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
    <path d="M7 2v20"></path>
    <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
  </svg>
);

const DinnerIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 24 24"
    fill="none"
    stroke="currentColor"
    strokeWidth="2"
    strokeLinecap="round"
    strokeLinejoin="round"
  >
    <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
    <path d="M7 2v20"></path>
    <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
  </svg>
);

// Meal plan descriptions
const mealPlanDescriptions: Record<MealPlanType, string> = {
  none: "Accommodation only",
  bb: "Includes breakfast",
  hb: "Breakfast and dinner",
  fb: "All meals included",
};

// Meal plan icons
const mealPlanIcons: Record<MealPlanType, React.ReactNode> = {
  none: <MealIcon />,
  bb: <BreakfastIcon />,
  hb: (
    <React.Fragment>
      <BreakfastIcon />
      <DinnerIcon />
    </React.Fragment>
  ),
  fb: (
    <React.Fragment>
      <BreakfastIcon />
      <LunchIcon />
      <DinnerIcon />
    </React.Fragment>
  ),
};

const MealPlanSelector: React.FC<MealPlanSelectorProps> = ({
  selectedRoom,
  selectedMealPlan,
  onMealPlanChange,
  formatCurrency,
  getCurrencyCode,
}) => {
  // Track previous room ID to detect changes
  const prevRoomIdRef = useRef<string | number | null>(null);

  // Log when selected room changes
  useEffect(() => {
    const currentRoomId = selectedRoom?.id;
    if (currentRoomId !== prevRoomIdRef.current) {
      prevRoomIdRef.current = currentRoomId;
    }
  }, [selectedRoom]);

  return (
    <div className="mb-8">
      <h3 className="text-sm font-karla uppercase tracking-wider mb-4 text-[#3566ab] flex items-center">
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
          className="mr-2"
        >
          <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
          <path d="M7 2v20"></path>
          <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
        </svg>
        Meal Plan Options
      </h3>
      <div className="flex overflow-x-auto pb-2 gap-4">
        {mealPlanKeys.map((mealPlanValue) => {
          const label = getMealPlanLabel(selectedRoom, mealPlanValue);
          const isAvailable =
            !!selectedRoom?.priceDetails?.meal_plans?.[mealPlanValue];
          const isSelected = selectedMealPlan === mealPlanValue;

          // Get the per night amount for this meal plan
          const perNightAmount =
            selectedRoom?.priceDetails?.meal_plans?.[mealPlanValue]
              ?.per_night_amount || 0;

          // Calculate price difference from base (Room Only) plan
          const basePlanAmount =
            selectedRoom?.priceDetails?.meal_plans?.none?.per_night_amount || 0;
          const priceDifference = perNightAmount - basePlanAmount;

          return (
            <div
              key={mealPlanValue}
              onClick={() => isAvailable && onMealPlanChange(mealPlanValue)}
              className={`
                relative flex-shrink-0 min-w-[220px] max-w-[280px] rounded-xl border transition-all duration-300
                ${
                  !isAvailable
                    ? "opacity-60 cursor-not-allowed"
                    : "cursor-pointer hover:shadow-md transform hover:-translate-y-0.5"
                }
                ${
                  isSelected && isAvailable
                    ? "border-[#3566ab] shadow-md bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10"
                    : "border-[#3566ab]/10 bg-white"
                }
              `}
            >
              {isSelected && isAvailable && (
                <div className="absolute top-2 right-2 w-5 h-5 rounded-full bg-[#3566ab] flex items-center justify-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="12"
                    height="12"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="white"
                    strokeWidth="3"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                </div>
              )}

              <div className="p-4">
                <div className="flex items-center text-[#3566ab] mb-2">
                  <div className="mr-2">{mealPlanIcons[mealPlanValue]}</div>
                  <h4 className="font-medium">{label}</h4>
                </div>

                <p className="text-sm text-foreground/70 mb-3 h-10">
                  {mealPlanDescriptions[mealPlanValue]}
                </p>

                {isAvailable ? (
                  <div className="flex justify-between items-end mt-auto">
                    <div>
                      <div className="text-xs text-foreground/60">
                        Per night
                      </div>
                      <div className="text-lg font-medium text-[#3566ab]">
                        {formatCurrency(perNightAmount, getCurrencyCode())}
                      </div>
                      {priceDifference > 0 && mealPlanValue !== "none" && (
                        <div className="text-xs text-foreground/60">
                          +{formatCurrency(priceDifference, getCurrencyCode())}
                        </div>
                      )}
                    </div>

                    {isSelected ? (
                      <div className="px-3 py-2 bg-[#3566ab]/20 text-[#3566ab] rounded-md font-medium text-sm">
                        Selected
                      </div>
                    ) : (
                      <div className="px-3 py-2 bg-[#3566ab] text-white rounded-md font-medium text-sm">
                        Select
                      </div>
                    )}
                  </div>
                ) : (
                  <div className="text-sm text-foreground/40 italic mt-auto">
                    Not available
                  </div>
                )}
              </div>
            </div>
          );
        })}
      </div>
    </div>
  );
};

export default MealPlanSelector;
