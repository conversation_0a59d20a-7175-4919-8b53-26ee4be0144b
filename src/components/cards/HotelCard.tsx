import React from "react";

export interface HotelCardProps {
  id: number | string;
  name: string;
  location: string;
  rating: number;
  imageUrl: string;
  description?: string;
  amenities?: string[];
  isFeatured?: boolean;
  onCardClick?: (e: React.MouseEvent) => void;
  onOpenInNewTab?: (e: React.MouseEvent) => void;
}

const HotelCard: React.FC<HotelCardProps> = ({
  id,
  name,
  location,
  rating,
  imageUrl,
  description,
  amenities = [],
  isFeatured,
  onCardClick,
  onOpenInNewTab,
}) => {
  const handleCardClick = (e: React.MouseEvent) => {
    if (onCardClick) {
      onCardClick(e);
    } else {
      window.location.href = `/stays/${id}`;
    }
  };

  return (
    <div className="group cursor-pointer" onClick={handleCardClick}>
      <div className="overflow-hidden bg-white border border-[#285DA6]/10 rounded-lg shadow-sm transition-all duration-300 hover:shadow-lg hover:border-[#285DA6]/30 group-hover:translate-y-[-4px]">
        <div className="relative">
          <div className="h-64 overflow-hidden">
            <img
              src={imageUrl}
              alt={name}
              className="w-full h-full object-cover transition-transform duration-700 group-hover:scale-105"
            />
          </div>

          {/* Featured badge */}
          {isFeatured && (
            <div className="absolute top-3 left-3 bg-yellow-400 text-white text-xs px-2 py-1 rounded-md flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="currentColor"
                stroke="none"
                className="mr-1"
              >
                <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
              </svg>
              <span>Featured</span>
            </div>
          )}

          {/* Open in new tab button */}
          {onOpenInNewTab && (
            <a
              href={`/stays/${id}`}
              target="_blank"
              rel="noopener noreferrer"
              className="absolute top-3 right-3 bg-white/80 p-1.5 rounded-full hover:bg-white transition-colors"
              aria-label="Open in new tab"
              onClick={onOpenInNewTab}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <path d="M18 13v6a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2V8a2 2 0 0 1 2-2h6"></path>
                <polyline points="15 3 21 3 21 9"></polyline>
                <line x1="10" y1="14" x2="21" y2="3"></line>
              </svg>
            </a>
          )}
        </div>

        <div className="p-6">
          <div className="flex items-center mb-2">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="currentColor"
              stroke="none"
              className="text-[#285DA6] mr-1"
            >
              <polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon>
            </svg>
            <span className="text-sm font-medium">{rating}</span>
            <span className="mx-2 text-foreground/30">•</span>
            <span className="text-sm text-foreground/70">{location}</span>
          </div>

          <h3 className="text-xl font-baskervville mb-3 group-hover:text-[#285DA6] transition-colors">
            {name}
          </h3>

          <p className="text-foreground/70 text-sm mb-4 line-clamp-2">
            {description ||
              "Experience luxury and comfort in this exceptional accommodation, offering the perfect blend of elegance and mountain charm."}
          </p>

          <div className="flex flex-wrap gap-2 mb-4">
            {amenities && amenities.length > 0 ? (
              amenities.slice(0, 3).map((tag, index) => (
                <span
                  key={index}
                  className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-karla bg-[#285DA6]/5 text-[#285DA6]"
                >
                  {tag}
                </span>
              ))
            ) : (
              <>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-karla bg-[#285DA6]/5 text-[#285DA6]">
                  Mountain View
                </span>
                <span className="inline-flex items-center px-2.5 py-0.5 rounded-md text-xs font-karla bg-[#285DA6]/5 text-[#285DA6]">
                  Luxury
                </span>
              </>
            )}
          </div>

          <div className="flex items-center text-[#285DA6] font-karla uppercase tracking-wider text-xs border-t border-[#285DA6]/10 pt-4 mt-4">
            <span className="mr-1">View Details</span>
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="14"
              height="14"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="group-hover:translate-x-1 transition-transform duration-300"
            >
              <path d="M5 12h14"></path>
              <path d="m12 5 7 7-7 7"></path>
            </svg>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HotelCard;
