---
import { getLangFromUrl, buildUrl } from "../../i18n/utils";

// Get current language from URL
const lang = getLangFromUrl(Astro.url);
---

<section class="py-16 md:py-24 bg-[#285DA6]">
  <div class="container-custom">
    <div class="max-w-3xl mx-auto text-center">
      <h2
        class="text-2xl sm:text-3xl md:text-4xl font-baskervville text-white mb-6"
      >
        Ready to become a Perfect Piste partner?
      </h2>
      <p class="text-lg font-baskervville text-white/90 mb-8">
        Join our exclusive network of partners and offer your clients the
        ultimate luxury ski experience.
      </p>
      <a
        href={buildUrl("/contact", lang)}
        class="inline-flex items-center justify-center px-8 py-3 bg-white text-[#285DA6] font-karla font-bold text-sm uppercase tracking-wider rounded-md hover:bg-white/90 transition-colors"
      >
        Apply Now
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>
  </div>
</section>
