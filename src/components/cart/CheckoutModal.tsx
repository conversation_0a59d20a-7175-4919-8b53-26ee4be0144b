import React, { useState, useEffect } from "react";
import { getCart } from "../../utils/cartUtils";
import {
  createHotelCart,
  createCartPaymentSession,
} from "../../utils/store/cart";
import { Elements } from "@stripe/react-stripe-js";
import { getStripe } from "../../utils/payment/stripe";
import StripeCheckout from "../payment/StripeCheckout";
import { useUser } from "../../contexts/UserContext";

// Meal plan mapping - similar to what's in the EnhancedBookingBox component
const mealPlanLabels: Record<string, string> = {
  none: "No Meals",
  bb: "Bed & Breakfast",
  hb: "Half Board",
  fb: "Full Board",
};

// We'll use the getStripe function from our utils

interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specialRequests: string;
}

const CheckoutModal: React.FC = () => {
  // State for modal visibility
  const [isOpen, setIsOpen] = useState(false);

  // Function to close the modal
  const onClose = () => {
    setIsOpen(false);
    // Show the checkout button again
    const checkoutButtonContainer = document.getElementById(
      "checkout-button-container"
    );
    if (checkoutButtonContainer) {
      checkoutButtonContainer.classList.remove("hidden");
    }
  };

  // Listen for custom event to open the modal
  useEffect(() => {
    const handleOpenModal = () => {
      setIsOpen(true);
    };

    // Add event listener
    window.addEventListener("openCheckoutModal", handleOpenModal);

    // Clean up
    return () => {
      window.removeEventListener("openCheckoutModal", handleOpenModal);
    };
  }, []);

  // State for customer information
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    specialRequests: "",
  });

  // Try to get user context
  let userContext = null;
  try {
    userContext = useUser();
  } catch (error) {
    // console.log("UserContext not available in CheckoutModal");
  }

  // Prepopulate customer information if user is logged in
  useEffect(() => {
    if (isOpen) {
      // First try to use UserContext if available
      if (userContext && userContext.isAuthenticated && userContext.user) {
        setCustomerInfo({
          firstName: userContext.user.first_name || "",
          lastName: userContext.user.last_name || "",
          email: userContext.user.email || "",
          phone: userContext.user.phone || "",
          specialRequests: "",
        });
      } else {
        // Fallback to token-based auth
        const token = localStorage.getItem("auth_token");
        if (token) {
          // Try to fetch user data
          try {
            const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";
            fetch(`${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`, {
              headers: {
                "Content-Type": "application/json",
                Authorization: `Bearer ${token}`,
                "x-publishable-api-key": apiKey,
              },
              credentials: "include",
            })
              .then((response) => {
                if (response.ok) {
                  return response.json();
                }
                throw new Error("Failed to fetch user data");
              })
              .then((data) => {
                if (data.customer) {
                  setCustomerInfo({
                    firstName: data.customer.first_name || "",
                    lastName: data.customer.last_name || "",
                    email: data.customer.email || "",
                    phone: data.customer.phone || "",
                    specialRequests: "",
                  });
                }
              })
              .catch((error) => {
                console.error("Error fetching user data:", error);
              });
          } catch (error) {
            console.error("Error fetching user data:", error);
          }
        }
      }
    }
  }, [isOpen, userContext]);

  // State for cart and payment
  const [cart, setCart] = useState<any[]>([]);
  const [totalPrice, setTotalPrice] = useState(0);
  const [isProcessing, setIsProcessing] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [showStripeElements, setShowStripeElements] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);

  // Load cart data on component mount
  useEffect(() => {
    if (isOpen) {
      const cartData = getCart();
      setCart(cartData);

      // Calculate total price
      const total = cartData.reduce(
        (sum, item) => sum + (typeof item.price === "number" ? item.price : 0),
        0
      );
      setTotalPrice(total);
    }
  }, [isOpen]);

  // Handle input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (cart.length === 0) {
      setPaymentError("Your cart is empty");
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      // Get the first item in the cart
      const firstItem = cart[0];

      // Create a cart in the backend
      const cartResponse = await createHotelCart({
        hotel_id: firstItem.hotelId,
        room_config_id: firstItem.roomId,
        // room_id: firstItem.roomId,
        check_in_date: firstItem.checkIn,
        check_out_date: firstItem.checkOut,
        check_in_time: firstItem.checkInTime || "",
        check_out_time: firstItem.checkOutTime || "",
        guest_name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        guest_email: customerInfo.email,
        guest_phone: customerInfo.phone,
        adults: firstItem.guests,
        children: 0,
        infants: firstItem.infants || 0,
        number_of_rooms: firstItem.quantity || 1,
        total_amount: firstItem.price,
        currency_code: firstItem.currency,
        special_requests: customerInfo.specialRequests,
        region_id: firstItem.regionId || "reg_01",
      });

      if (!cartResponse.cart?.id) {
        throw new Error("Failed to create cart");
      }

      // Store the cart ID in session storage for later use
      sessionStorage.setItem("cart_id", cartResponse.cart.id);

      // Create a payment session
      const paymentResponse = await createCartPaymentSession(
        cartResponse.cart.id,
        "pp_stripe_stripe"
      );

      if (!paymentResponse.payment_session?.id) {
        throw new Error("Failed to create payment session");
      }

      // Check if we have valid payment session data
      if (!paymentResponse.payment_session?.data) {
        throw new Error("No payment session data found in the response");
      }

      // Show a loading message
      setPaymentError("Initializing payment...");

      try {
        // Extract the client secret and payment session ID
        const clientSecret =
          paymentResponse.payment_session?.data?.client_secret;
        const paymentSessionId = paymentResponse.payment_session?.id;
        const paymentIntentId = paymentResponse.payment_session?.data?.id;

        if (!clientSecret) {
          throw new Error("No client secret found in payment session data");
        }

        // Store in session storage for reference
        sessionStorage.setItem("stripe_client_secret", clientSecret);
        if (paymentSessionId) {
          sessionStorage.setItem("stripe_payment_session_id", paymentSessionId);
        }

        // Instead of redirecting, show Stripe Elements
        setClientSecret(clientSecret);
        setShowStripeElements(true);
        setPaymentError(null);
        setIsProcessing(false);
      } catch (err) {
        console.error("Error processing payment:", err);
        setPaymentError(
          err instanceof Error ? err.message : "An unknown error occurred"
        );
        setIsProcessing(false);
      }
    } catch (error) {
      console.error("Error processing payment:", error);
      setPaymentError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      setIsProcessing(false);
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-[9999] flex items-center justify-center">
      {/* Backdrop */}
      <div
        className="absolute inset-0 bg-black/50 backdrop-blur-sm"
        onClick={onClose}
      ></div>

      {/* Modal */}
      <div className="relative bg-white rounded-xl shadow-2xl max-w-3xl w-full max-h-[80vh] overflow-y-auto z-[101]">
        {/* Header */}
        <div className="sticky top-0 bg-white z-[101] px-8 py-6 border-b border-gray-100 flex justify-between items-center">
          <h2 className="text-2xl font-baskervville text-[#285DA6]">
            Complete Your Booking
          </h2>
          <button
            onClick={onClose}
            className="text-gray-400 hover:text-gray-600 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="p-8">
          {/* Order Summary */}
          <div className="mb-8 bg-gray-50 p-6 rounded-lg">
            <h3 className="text-lg font-medium mb-4">Order Summary</h3>

            {cart.map((item) => (
              <div
                key={item.id}
                className="flex items-start gap-4 mb-4 pb-4 border-b border-gray-200 last:border-0 last:pb-0 last:mb-0"
              >
                {item.image && (
                  <img
                    src={item.image}
                    alt={item.roomType}
                    className="w-20 h-20 object-cover rounded-md flex-shrink-0"
                  />
                )}
                <div className="flex-grow">
                  <div className="flex justify-between">
                    <div>
                      <h4 className="font-medium">{item.hotelName}</h4>
                      <p className="text-sm text-gray-600">{item.roomType}</p>
                    </div>
                    <span className="font-medium whitespace-nowrap">
                      {item.currency} {item.price.toFixed(2)}
                    </span>
                  </div>
                  <div className="mt-1 text-sm text-gray-500 grid grid-cols-2 gap-x-4 gap-y-1">
                    <p>Check-in: {item.checkIn}</p>
                    <p>Check-out: {item.checkOut}</p>
                    <p>Guests: {item.guests}</p>
                    {item.mealPlan && (
                      <p>
                        Meal Plan:{" "}
                        {mealPlanLabels[item.mealPlan] || item.mealPlan}
                      </p>
                    )}
                    {item.quantity && item.quantity > 1 && (
                      <p className="font-medium text-[#285DA6]">
                        Rooms: {item.quantity}
                      </p>
                    )}
                  </div>
                </div>
              </div>
            ))}

            <div className="flex justify-between items-center pt-4 font-medium">
              <span>Total</span>
              <span className="text-lg">
                {cart[0]?.currency} {totalPrice.toFixed(2)}
              </span>
            </div>
          </div>

          {paymentSuccess ? (
            <div className="py-6 text-center">
              <div className="mb-4 mx-auto w-12 h-12 rounded-full bg-green-100 flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-green-600"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <h3 className="text-xl font-medium mb-2">Payment Successful!</h3>
              <p className="text-gray-600 mb-2">
                Your booking has been confirmed. Thank you for your purchase.
              </p>
              <p className="text-sm text-green-600 mb-6">
                Please wait for a moment...
              </p>
              <button
                onClick={onClose}
                className="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-medium transition-colors"
              >
                Close
              </button>
            </div>
          ) : showStripeElements && clientSecret ? (
            <div className="py-4">
              <h3 className="text-lg font-medium mb-4">Payment Details</h3>
              <p className="text-gray-600 mb-6">
                Please enter your card details to complete the payment.
              </p>

              <Elements stripe={getStripe()}>
                <StripeCheckout
                  clientSecret={clientSecret}
                  amount={totalPrice}
                  currency={cart[0]?.currency || "USD"}
                  onSuccess={() => {
                    setPaymentSuccess(true);
                    // Clear cart after successful payment
                    localStorage.setItem("cart", JSON.stringify([]));

                    // Show success message for 2 seconds before redirecting
                    setTimeout(() => {
                      window.location.href = "/account";
                    }, 2000);
                  }}
                  onError={(error) => setPaymentError(error)}
                />
              </Elements>

              <div className="mt-4 text-center">
                <button
                  type="button"
                  onClick={onClose}
                  className="text-gray-500 hover:text-gray-700"
                >
                  Cancel
                </button>
              </div>
            </div>
          ) : (
            <form onSubmit={handleSubmit}>
              <h3 className="text-lg font-medium mb-4">Contact Information</h3>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
                <div>
                  <label
                    htmlFor="firstName"
                    className="block mb-1 text-sm font-medium text-gray-700"
                  >
                    First Name
                  </label>
                  <input
                    type="text"
                    id="firstName"
                    name="firstName"
                    value={customerInfo.firstName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6] focus:border-transparent transition-all"
                    placeholder="John"
                  />
                </div>

                <div>
                  <label
                    htmlFor="lastName"
                    className="block mb-1 text-sm font-medium text-gray-700"
                  >
                    Last Name
                  </label>
                  <input
                    type="text"
                    id="lastName"
                    name="lastName"
                    value={customerInfo.lastName}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6] focus:border-transparent transition-all"
                    placeholder="Doe"
                  />
                </div>

                <div>
                  <label
                    htmlFor="email"
                    className="block mb-1 text-sm font-medium text-gray-700"
                  >
                    Email
                  </label>
                  <input
                    type="email"
                    id="email"
                    name="email"
                    value={customerInfo.email}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6] focus:border-transparent transition-all"
                    placeholder="<EMAIL>"
                  />
                </div>

                <div>
                  <label
                    htmlFor="phone"
                    className="block mb-1 text-sm font-medium text-gray-700"
                  >
                    Phone
                  </label>
                  <input
                    type="tel"
                    id="phone"
                    name="phone"
                    value={customerInfo.phone}
                    onChange={handleInputChange}
                    required
                    className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6] focus:border-transparent transition-all"
                    placeholder="+****************"
                  />
                </div>
              </div>

              <div className="mb-6">
                <label
                  htmlFor="specialRequests"
                  className="block mb-1 text-sm font-medium text-gray-700"
                >
                  Special Requests (Optional)
                </label>
                <textarea
                  id="specialRequests"
                  name="specialRequests"
                  value={customerInfo.specialRequests}
                  onChange={handleInputChange}
                  rows={3}
                  className="w-full px-4 py-3 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-[#285DA6] focus:border-transparent transition-all"
                  placeholder="Any special requirements or requests..."
                />
              </div>

              {paymentError && (
                <div className="p-4 mb-6 bg-red-50 border border-red-200 text-red-700 rounded-lg">
                  <div className="flex items-center">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-2"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    {paymentError}
                  </div>
                </div>
              )}

              <div className="flex justify-end gap-4">
                <button
                  type="button"
                  onClick={onClose}
                  className="px-6 py-3 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 font-karla uppercase tracking-wider transition-all duration-300"
                >
                  Cancel
                </button>
                <button
                  type="submit"
                  disabled={isProcessing || cart.length === 0}
                  className="px-6 py-3 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed"
                >
                  {isProcessing ? (
                    <span className="flex items-center">
                      <svg
                        className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                      >
                        <circle
                          className="opacity-25"
                          cx="12"
                          cy="12"
                          r="10"
                          stroke="currentColor"
                          strokeWidth="4"
                        ></circle>
                        <path
                          className="opacity-75"
                          fill="currentColor"
                          d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                        ></path>
                      </svg>
                      Processing...
                    </span>
                  ) : (
                    "Proceed to Payment"
                  )}
                </button>
              </div>
            </form>
          )}
        </div>
      </div>
    </div>
  );
};

export default CheckoutModal;
