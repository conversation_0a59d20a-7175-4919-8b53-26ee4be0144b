import React, { useState, useEffect } from "react";
import { formatDate } from "../../utils/dateUtils";
import { Elements } from "@stripe/react-stripe-js";
import { getStripe } from "../../utils/payment/stripe";
import StripeCheckout from "../payment/StripeCheckout";
import { useUser } from "../../contexts/UserContext";
import {
  createHotelCart,
  createCartPaymentSession,
  capturePayment,
  completeCart,
} from "../../utils/store/cart";

// Meal plan mapping
const mealPlanLabels: Record<string, string> = {
  none: "No Meals",
  bb: "Bed & Breakfast",
  hb: "Half Board",
  fb: "Full Board",
};

interface CheckoutFormProps {
  hotelId: string;
  roomConfigId: string;
  checkInDate: string;
  checkOutDate: string;
  checkInTime: string;
  checkOutTime: string;
  totalAmount: number;
  currencyCode: string;
  guestCount: number;
  infantCount: number;
  mealPlan: string;
  roomQuantity: number;
  hotelName: string;
  hotelLocation: string;
  roomName: string;
  roomImage: string;
  regionId: string;
}

interface CustomerInfo {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  specialRequests: string;
}

const CheckoutForm: React.FC<CheckoutFormProps> = ({
  hotelId,
  roomConfigId,
  checkInDate,
  checkOutDate,
  checkInTime,
  checkOutTime,
  totalAmount,
  currencyCode,
  guestCount,
  infantCount,
  mealPlan,
  roomQuantity,
  hotelName,
  hotelLocation,
  roomName,
  roomImage,
  regionId,
}) => {
  // Customer information state
  const [customerInfo, setCustomerInfo] = useState<CustomerInfo>({
    firstName: "",
    lastName: "",
    email: "",
    phone: "",
    specialRequests: "",
  });

  // Payment state
  const [isProcessing, setIsProcessing] = useState(false);
  const [isCapturingPayment, setIsCapturingPayment] = useState(false);
  const [paymentError, setPaymentError] = useState<string | null>(null);
  const [showStripeElements, setShowStripeElements] = useState(false);
  const [clientSecret, setClientSecret] = useState<string | null>(null);
  const [paymentSuccess, setPaymentSuccess] = useState(false);
  const [bookingId, setBookingId] = useState<string | null>(null);
  const [cartId, setCartId] = useState<string | null>(null);
  const [paymentSessionId, setPaymentSessionId] = useState<string | null>(null);

  // Try to get user context
  let userContext = null;
  try {
    userContext = useUser();
  } catch (error) {
    // User context not available
  }

  // Prepopulate customer information if user is logged in
  useEffect(() => {
    // First try to use UserContext if available
    if (userContext && userContext.isAuthenticated && userContext.user) {
      setCustomerInfo({
        firstName: userContext.user.first_name || "",
        lastName: userContext.user.last_name || "",
        email: userContext.user.email || "",
        phone: userContext.user.phone || "",
        specialRequests: "",
      });
    } else {
      // Fallback to token-based auth
      const token = localStorage.getItem("auth_token");
      if (token) {
        // Try to fetch user data
        try {
          const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";
          fetch(`${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`, {
            headers: {
              "Content-Type": "application/json",
              Authorization: `Bearer ${token}`,
              "x-publishable-api-key": apiKey,
            },
            credentials: "include",
          })
            .then((response) => {
              if (response.ok) {
                return response.json();
              }
              throw new Error("Failed to fetch user data");
            })
            .then((data) => {
              if (data.customer) {
                setCustomerInfo({
                  firstName: data.customer.first_name || "",
                  lastName: data.customer.last_name || "",
                  email: data.customer.email || "",
                  phone: data.customer.phone || "",
                  specialRequests: "",
                });
              }
            })
            .catch((error) => {
              console.error("Error fetching user data:", error);
            });
        } catch (error) {
          console.error("Error fetching user data:", error);
        }
      }
    }
  }, [userContext]);

  // Calculate nights - handle empty or invalid dates
  const isValidDate = (dateStr: string) => {
    if (!dateStr) return false;
    const date = new Date(dateStr);
    return !isNaN(date.getTime());
  };

  // Only create Date objects if the strings are valid
  const startDate = isValidDate(checkInDate) ? new Date(checkInDate) : null;
  const endDate = isValidDate(checkOutDate) ? new Date(checkOutDate) : null;

  // Calculate nights only if both dates are valid
  let nights = 0;
  if (startDate && endDate) {
    const diffTime = endDate.getTime() - startDate.getTime();
    nights = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    // Ensure nights is at least 1 for valid date ranges
    nights = Math.max(1, nights);
  }

  // Format currency
  const formatCurrency = (amount: number, currency: string = "USD") => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(amount);
  };

  // Handle form input changes
  const handleInputChange = (
    e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>
  ) => {
    const { name, value } = e.target;
    setCustomerInfo((prev) => ({ ...prev, [name]: value }));
  };

  // Handle form submission
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!hotelId || !roomConfigId || !checkInDate || !checkOutDate) {
      setPaymentError("Missing required booking information");
      return;
    }

    setIsProcessing(true);
    setPaymentError(null);

    try {
      // Create a cart in the backend
      const cartResponse = await createHotelCart({
        hotel_id: hotelId,
        room_config_id: roomConfigId,
        check_in_date: checkInDate,
        check_out_date: checkOutDate,
        check_in_time: checkInTime,
        check_out_time: checkOutTime,
        guest_name: `${customerInfo.firstName} ${customerInfo.lastName}`,
        guest_email: customerInfo.email,
        guest_phone: customerInfo.phone,
        adults: guestCount,
        children: 0,
        infants: infantCount,
        number_of_rooms: roomQuantity,
        total_amount: totalAmount,
        currency_code: currencyCode.toLowerCase(),
        special_requests: customerInfo.specialRequests,
        region_id: regionId,
      });

      if (!cartResponse.cart?.id) {
        throw new Error("Failed to create cart");
      }

      // Store the cart ID
      setCartId(cartResponse.cart.id);
      sessionStorage.setItem("cart_id", cartResponse.cart.id);

      // Create a payment session
      const paymentResponse = await createCartPaymentSession(
        cartResponse.cart.id,
        "pp_stripe_stripe"
      );

      if (!paymentResponse.payment_session?.id) {
        throw new Error("Failed to create payment session");
      }

      // Check if we have valid payment session data
      if (!paymentResponse.payment_session?.data) {
        throw new Error("No payment session data found in the response");
      }

      // Extract the client secret and payment session ID
      const clientSecret = paymentResponse.payment_session?.data?.client_secret;
      const paymentSessionId = paymentResponse.payment_session?.id;

      if (!clientSecret) {
        throw new Error("No client secret found in payment session data");
      }

      // Store payment session ID
      setPaymentSessionId(paymentSessionId);
      sessionStorage.setItem("stripe_payment_session_id", paymentSessionId);
      sessionStorage.setItem("stripe_client_secret", clientSecret);

      // Show Stripe Elements
      setClientSecret(clientSecret);
      setShowStripeElements(true);
      setPaymentError(null);
      setIsProcessing(false);
    } catch (error) {
      console.error("Error processing payment:", error);
      setPaymentError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      setIsProcessing(false);
    }
  };

  // Handle successful payment
  const handlePaymentSuccess = async () => {
    if (
      !cartId ||
      !paymentSessionId ||
      !roomConfigId ||
      !checkInDate ||
      !checkOutDate
    ) {
      setPaymentError("Missing required information to complete booking");
      return;
    }

    // Set capturing payment state to true to keep the button in loading state
    setIsCapturingPayment(true);

    try {
      // Complete the cart to create a booking
      const completeResponse = await completeCart(
        cartId,
        roomConfigId,
        checkInDate,
        checkOutDate
      );

      console.log("Complete cart response:", completeResponse);

      // Check for order ID in the response (new API format)
      if (completeResponse.order?.id) {
        // New API format returns order.id
        setBookingId(completeResponse.order.id);
      } else if (completeResponse.booking?.id) {
        // Old API format returns booking.id
        setBookingId(completeResponse.booking.id);
      } else {
        throw new Error(
          "Failed to complete booking - no booking or order ID found in response"
        );
      }

      try {
        // Capture the payment
        console.log("Capturing payment with session ID:", paymentSessionId);
        const captureResponse = await capturePayment(paymentSessionId);
        console.log("Payment capture response:", captureResponse);
      } catch (captureError) {
        console.error(
          "Error capturing payment, but booking was created:",
          captureError
        );
        // Continue with success flow even if capture fails, as the booking was created
      } finally {
        // Always set capturing payment state to false
        setIsCapturingPayment(false);
      }

      // Create booking summary data
      const orderId =
        completeResponse.order?.id || completeResponse.booking?.id;
      const summaryData = {
        hotelId: hotelId,
        roomId: roomConfigId,
        checkInDate: checkInDate,
        checkOutDate: checkOutDate,
        checkInTime: checkInTime,
        checkOutTime: checkOutTime,
        nights: nights,
        adults: guestCount - (infantCount || 0),
        children: 0, // Not tracked in this flow
        infants: infantCount || 0,
        mealPlan: mealPlan || "none",
        totalAmount: totalAmount,
        basePrice: totalAmount * 0.9, // Approximate base price (90% of total)
        taxesAndFees: totalAmount * 0.1, // Approximate tax (10% of total)
        currencyCode: currencyCode?.toUpperCase() || "USD",
        hotel: {
          name: hotelName,
          location: hotelLocation,
          image: roomImage || "",
          check_in_time: checkInTime,
          check_out_time: checkOutTime,
        },
        room: {
          name: roomName,
          images: [roomImage || ""],
          thumbnail: roomImage || "",
          description: "",
          amenities: [],
          size: "",
          bedType: "",
          maxAdults: guestCount,
          maxChildren: 0,
          maxInfants: infantCount || 0,
          mealPlanPrices: {},
        },
        guestDetails: {
          title: "Mr", // Default
          firstName: customerInfo.firstName,
          lastName: customerInfo.lastName,
          email: customerInfo.email,
          phone: customerInfo.phone,
          specialRequests: "",
        },
        travelers: {
          adults: [],
          children: [],
          infants: [],
        },
        orderId: orderId,
        bookingReference: `PP-${Math.floor(Math.random() * 10000)
          .toString()
          .padStart(4, "0")}`,
        confirmedAt: new Date().toISOString(),
      };

      localStorage.setItem("bookingSummaryData", JSON.stringify(summaryData));

      // Set success state
      setPaymentSuccess(true);

      // Clear cart after successful payment
      localStorage.setItem("cart", JSON.stringify([]));

      // Hide the "Complete Your Booking" header
      const headerElement = document.getElementById("booking-header");
      if (headerElement) {
        headerElement.style.display = "none";
      }

      // Redirect to booking summary page after a short delay
      setTimeout(() => {
        window.location.href = `/booking-summary/${orderId}`;
      }, 2000);
    } catch (error) {
      console.error("Error completing booking:", error);
      setPaymentError(
        error instanceof Error ? error.message : "An unknown error occurred"
      );
      // Set capturing payment state to false on error
      setIsCapturingPayment(false);
    }
  };

  // Hide the header when payment is successful
  useEffect(() => {
    if (paymentSuccess) {
      const headerElement = document.getElementById("booking-header");
      if (headerElement) {
        headerElement.style.display = "none";
      }
    }
  }, [paymentSuccess]);

  if (paymentSuccess) {
    return (
      <div className="max-w-4xl mx-auto">
        <div className="bg-white border border-[#285DA6]/10 rounded-lg shadow-md overflow-hidden">
          {/* Header with gradient background */}
          <div className="bg-gradient-to-r from-[#285DA6] to-[#3A7BD5] text-white p-6 text-center">
            <div className="flex justify-center mb-4">
              <div className="w-20 h-20 bg-white/20 backdrop-blur-sm rounded-full flex items-center justify-center">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="40"
                  height="40"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white"
                >
                  <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                  <polyline points="22 4 12 14.01 9 11.01"></polyline>
                </svg>
              </div>
            </div>
            <h2 className="text-2xl font-karla uppercase tracking-wider mb-2">
              Booking Confirmed!
            </h2>
            <p className="text-white/90">
              Thank you for your booking. We've sent a confirmation email to{" "}
              <span className="font-medium">{customerInfo.email}</span>
            </p>
          </div>

          {/* Booking Details */}
          <div className="p-8">
            <div className="flex items-center justify-between mb-6 pb-6 border-b border-[#285DA6]/10">
              <div>
                <div className="text-sm text-foreground/60">
                  Booking Reference
                </div>
                <div className="text-xl font-medium text-[#285DA6]">
                  {bookingId}
                </div>
              </div>
              <div className="bg-[#285DA6]/10 px-4 py-2 rounded-full">
                <span className="text-sm font-medium text-[#285DA6]">
                  Confirmed
                </span>
              </div>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
              <div className="space-y-1">
                <div className="text-sm text-foreground/60">Hotel</div>
                <div className="font-medium">{hotelName}</div>
                <div className="text-sm">{hotelLocation}</div>
              </div>

              <div className="space-y-1">
                <div className="text-sm text-foreground/60">Room Type</div>
                <div className="font-medium">{roomName}</div>
                <div className="text-sm">
                  {roomQuantity > 1 ? `${roomQuantity} rooms` : "1 room"},{" "}
                  {guestCount} {guestCount === 1 ? "guest" : "guests"}
                  {infantCount > 0 &&
                    `, ${infantCount} ${
                      infantCount === 1 ? "infant" : "infants"
                    }`}
                </div>
              </div>

              <div className="space-y-1">
                <div className="text-sm text-foreground/60">Check-in</div>
                <div className="font-medium">
                  {startDate ? formatDate(startDate) : ""}
                </div>
                <div className="text-sm">{checkInTime}</div>
              </div>

              <div className="space-y-1">
                <div className="text-sm text-foreground/60">Check-out</div>
                <div className="font-medium">
                  {endDate ? formatDate(endDate) : ""}
                </div>
                <div className="text-sm">{checkOutTime}</div>
              </div>
            </div>

            <div className="bg-[#F8FAFC] rounded-lg p-4 mb-6">
              <div className="flex justify-between items-center">
                <span className="font-medium">Total Amount</span>
                <span className="text-lg font-baskervville text-[#285DA6]">
                  {formatCurrency(totalAmount, currencyCode)}
                </span>
              </div>
            </div>

            <div className="text-center">
              <div
                className="inline-block cursor-pointer bg-[#285DA6]/10 text-[#285DA6] px-3 py-3 rounded-full text-sm"
                onClick={() => {
                  window.location.href = "/account";
                }}
              >
                <svg
                  className="inline-block mr-2 h-4 w-4 mb-1"
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M13 9l3 3m0 0l-3 3m3-3H8m13 0a9 9 0 11-18 0 9 9 0 0118 0z"
                  />
                </svg>
                View My Bookings
              </div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="grid grid-cols-1 md:grid-cols-2 xl:grid-cols-12 gap-8 max-w-7xl mx-auto">
      {/* Order Summary - Fixed position on desktop, top on mobile */}
      <div className="md:col-span-1 xl:col-span-4 order-1 md:order-1">
        <div className="bg-white border border-[#285DA6]/10 rounded-lg shadow-md md:sticky md:top-24">
          {/* Header with gradient background */}
          {/* <div className="bg-gradient-to-r from-[#285DA6] to-[#3A7BD5] text-white p-4 rounded-t-lg">
            <h2 className="text-base font-karla uppercase tracking-wider">
              Booking Summary
            </h2>
          </div> */}

          {/* Hotel and Room Image */}
          <div className="relative">
            <div className="aspect-video overflow-hidden">
              <img
                src={roomImage || "/images/room-placeholder.jpg"}
                alt={roomName}
                className="w-full h-full object-cover"
              />
            </div>
            <div className="absolute bottom-0 left-0 right-0 bg-gradient-to-t from-black/70 to-transparent p-4">
              <h3 className="font-baskervville text-lg text-white mb-1">
                {hotelName}
              </h3>
              <p className="text-sm text-white/90">{hotelLocation}</p>
            </div>
          </div>

          {/* Booking Details */}
          <div className="p-4">
            {/* Room Type and Guests */}
            <div className="flex items-start mb-3 pb-3 border-b border-[#285DA6]/10">
              <div className="text-[#285DA6] mr-3 mt-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <path d="M3 22v-3.92a2.43 2.43 0 0 1 .76-1.78L9 11l2 2 2-2 5.24 5.3a2.43 2.43 0 0 1 .76 1.78V22"></path>
                  <path d="M8 11V5c0-1.1.9-2 2-2h4c1.1 0 2 .9 2 2v6"></path>
                </svg>
              </div>
              <div className="flex-1">
                <div className="font-medium text-[#285DA6]">{roomName}</div>
                <div className="text-sm text-foreground/70 mt-1">
                  {roomQuantity > 1 ? `${roomQuantity} rooms` : "1 room"},{" "}
                  {guestCount} {guestCount === 1 ? "guest" : "guests"}
                  {infantCount > 0 &&
                    `, ${infantCount} ${
                      infantCount === 1 ? "infant" : "infants"
                    }`}
                </div>
              </div>
            </div>

            {/* Dates */}
            <div className="flex items-start mb-3 pb-3 border-b border-[#285DA6]/10">
              <div className="text-[#285DA6] mr-3 mt-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                  <line x1="16" y1="2" x2="16" y2="6"></line>
                  <line x1="8" y1="2" x2="8" y2="6"></line>
                  <line x1="3" y1="10" x2="21" y2="10"></line>
                </svg>
              </div>
              <div className="flex-1">
                <div className="font-medium text-[#285DA6]">Dates</div>
                <div className="text-sm text-foreground/70 mt-1">
                  {startDate ? formatDate(startDate) : ""} -{" "}
                  {endDate ? formatDate(endDate) : ""}
                  {nights > 0 && (
                    <span className="block mt-1 text-[#285DA6]/80 font-medium">
                      {nights} {nights === 1 ? "night" : "nights"}
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Check-in/out Times */}
            <div className="flex items-start mb-3 pb-3 border-b border-[#285DA6]/10">
              <div className="text-[#285DA6] mr-3 mt-1">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10"></circle>
                  <polyline points="12 6 12 12 16 14"></polyline>
                </svg>
              </div>
              <div className="flex-1">
                <div className="font-medium text-[#285DA6]">Check-in/out</div>
                <div className="text-sm text-foreground/70 mt-1">
                  <div className="flex justify-between">
                    <span>Check-in:</span>
                    <span className="font-medium">{checkInTime}</span>
                  </div>
                  <div className="flex justify-between mt-1">
                    <span>Check-out:</span>
                    <span className="font-medium">{checkOutTime}</span>
                  </div>
                </div>
              </div>
            </div>

            {/* Meal Plan */}
            {mealPlan && (
              <div className="flex items-start mb-3 pb-3 border-b border-[#285DA6]/10">
                <div className="text-[#285DA6] mr-3 mt-1">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                    <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                    <line x1="6" y1="1" x2="6" y2="4"></line>
                    <line x1="10" y1="1" x2="10" y2="4"></line>
                    <line x1="14" y1="1" x2="14" y2="4"></line>
                  </svg>
                </div>
                <div className="flex-1">
                  <div className="font-medium text-[#285DA6]">Meal Plan</div>
                  <div className="text-sm text-foreground/70 mt-1">
                    {mealPlanLabels[mealPlan] || mealPlan}
                  </div>
                </div>
              </div>
            )}

            {/* Price Summary */}
            <div className="bg-[#F8FAFC] rounded-lg p-3 mt-3">
              <div className="border-[#285DA6]/10">
                <div className="flex justify-between items-center">
                  <span className="font-medium">Total</span>
                  <span className="text-lg font-baskervville text-[#285DA6]">
                    {formatCurrency(totalAmount, currencyCode)}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Guest Information Form */}
      <div className="md:col-span-1 xl:col-span-8 order-2 md:order-2">
        <div className="bg-white border border-[#285DA6]/10 rounded-lg shadow-md overflow-hidden">
          {/* Form Header */}
          <div className="bg-gradient-to-r from-[#285DA6]/10 to-[#3A7BD5]/10 p-6 border-b border-[#285DA6]/10">
            <h2 className="text-base font-karla uppercase tracking-wider text-[#285DA6]">
              {showStripeElements && clientSecret
                ? "Payment Details"
                : "Guest Information"}
            </h2>
            {!showStripeElements && (
              <p className="text-sm text-foreground/70 mt-1">
                Please provide your details to complete your reservation
              </p>
            )}
            {showStripeElements && clientSecret && (
              <p className="text-sm text-foreground/70 mt-1">
                Please enter your card details to complete the payment
              </p>
            )}
          </div>

          <div className="p-6">
            {showStripeElements && clientSecret ? (
              <div>
                <div className="bg-[#F8FAFC] rounded-lg p-4 mb-6">
                  <div className="flex items-center mb-4">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#285DA6] mr-3"
                    >
                      <rect
                        x="1"
                        y="4"
                        width="22"
                        height="16"
                        rx="2"
                        ry="2"
                      ></rect>
                      <line x1="1" y1="10" x2="23" y2="10"></line>
                    </svg>
                    <span className="font-medium">Secure Payment</span>
                  </div>
                  <p className="text-sm text-foreground/70">
                    Your payment information is encrypted and secure. We never
                    store your full card details.
                  </p>
                </div>

                <Elements stripe={getStripe()}>
                  <StripeCheckout
                    clientSecret={clientSecret}
                    amount={totalAmount}
                    currency={currencyCode}
                    onSuccess={handlePaymentSuccess}
                    onError={(error) => setPaymentError(error)}
                    externalProcessing={isCapturingPayment}
                  />
                </Elements>

                {paymentError && (
                  <div className="mt-6 p-4 bg-red-50 text-red-700 rounded-md flex items-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-red-500 mr-3 mt-0.5"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <div>
                      <p className="font-medium">Payment Error</p>
                      <p className="text-sm">{paymentError}</p>
                    </div>
                  </div>
                )}
              </div>
            ) : (
              <form onSubmit={handleSubmit} className="space-y-6">
                <div className="space-y-6">
                  <div>
                    {/* <h3 className="text-sm font-medium text-[#285DA6] mb-4">
                      Contact Information
                    </h3> */}
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                      <div>
                        <label
                          htmlFor="firstName"
                          className="block text-sm font-medium mb-1"
                        >
                          First Name*
                        </label>
                        <input
                          type="text"
                          id="firstName"
                          name="firstName"
                          value={customerInfo.firstName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                        />
                      </div>
                      <div>
                        <label
                          htmlFor="lastName"
                          className="block text-sm font-medium mb-1"
                        >
                          Last Name*
                        </label>
                        <input
                          type="text"
                          id="lastName"
                          name="lastName"
                          value={customerInfo.lastName}
                          onChange={handleInputChange}
                          required
                          className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                        />
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <label
                        htmlFor="email"
                        className="block text-sm font-medium mb-1"
                      >
                        Email Address*
                      </label>
                      <input
                        type="email"
                        id="email"
                        name="email"
                        value={customerInfo.email}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                      />
                    </div>
                    <div>
                      <label
                        htmlFor="phone"
                        className="block text-sm font-medium mb-1"
                      >
                        Phone Number*
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={customerInfo.phone}
                        onChange={handleInputChange}
                        required
                        className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                      />
                    </div>
                  </div>

                  <div>
                    <h3 className="text-sm font-medium text-[#285DA6] mb-4">
                      Additional Information
                    </h3>
                    <div>
                      <label
                        htmlFor="specialRequests"
                        className="block text-sm font-medium mb-1"
                      >
                        Special Requests
                      </label>
                      <textarea
                        id="specialRequests"
                        name="specialRequests"
                        value={customerInfo.specialRequests}
                        onChange={handleInputChange}
                        rows={3}
                        placeholder="Let us know if you have any special requirements"
                        className="w-full px-3 py-2 border border-[#285DA6]/20 rounded-md focus:outline-none focus:ring-1 focus:ring-[#285DA6]"
                      ></textarea>
                    </div>
                  </div>
                </div>

                {paymentError && (
                  <div className="p-4 bg-red-50 text-red-700 rounded-md flex items-start">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-red-500 mr-3 mt-0.5"
                    >
                      <circle cx="12" cy="12" r="10"></circle>
                      <line x1="12" y1="8" x2="12" y2="12"></line>
                      <line x1="12" y1="16" x2="12.01" y2="16"></line>
                    </svg>
                    <div>
                      <p className="font-medium">Error</p>
                      <p className="text-sm">{paymentError}</p>
                    </div>
                  </div>
                )}

                <div className="pt-4">
                  <button
                    type="submit"
                    disabled={isProcessing}
                    className="w-full py-3 px-4 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 transition-colors font-karla uppercase tracking-wider disabled:opacity-70 flex items-center justify-center"
                  >
                    {isProcessing ? (
                      <>
                        <svg
                          className="animate-spin -ml-1 mr-3 h-5 w-5 text-white"
                          xmlns="http://www.w3.org/2000/svg"
                          fill="none"
                          viewBox="0 0 24 24"
                        >
                          <circle
                            className="opacity-25"
                            cx="12"
                            cy="12"
                            r="10"
                            stroke="currentColor"
                            strokeWidth="4"
                          ></circle>
                          <path
                            className="opacity-75"
                            fill="currentColor"
                            d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                          ></path>
                        </svg>
                        Processing...
                      </>
                    ) : (
                      <>
                        Proceed to Payment
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="20"
                          height="20"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          strokeWidth="2"
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          className="ml-2"
                        >
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                          <polyline points="12 5 19 12 12 19"></polyline>
                        </svg>
                      </>
                    )}
                  </button>
                </div>
              </form>
            )}
          </div>
        </div>
      </div>
    </div>
  );
};

export default CheckoutForm;
