import React, { useState } from "react";

interface ContactFormProps {
  translationsJson: string;
}

interface Translations {
  title: string;
  successTitle: string;
  successMessage: string;
  travelType: string;
  travelTypePlaceholder: string;
  travelTypeOptions: {
    ski: string;
    luxury: string;
    family: string;
    adventure: string;
    other: string;
  };
  name: string;
  namePlaceholder: string;
  email: string;
  emailPlaceholder: string;
  phone: string;
  phonePlaceholder: string;
  travelStartDate: string;
  travelStartDatePlaceholder: string;
  message: string;
  messagePlaceholder: string;
  preferredContact: string;
  preferredContactEmail: string;
  preferredContactPhone: string;
  privacyPolicyPrefix: string;
  privacyPolicyLink: string;
  privacyPolicyAnd: string;
  bookingConditionsLink: string;
  submit: string;
}

const ContactForm: React.FC<ContactFormProps> = ({ translationsJson }) => {
  // Parse translations from JSON
  let t: Translations;
  try {
    t = JSON.parse(translationsJson);
  } catch (error) {
    console.error("Failed to parse translations:", error);
    return <div>Error loading form</div>;
  }
  const [formData, setFormData] = useState({
    travelType: "",
    name: "",
    email: "",
    phone: "",
    travelStartDate: "",
    message: "",
    preferredContact: "email",
    privacyPolicy: false,
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [showError, setShowError] = useState(false);
  const [errorMessage, setErrorMessage] = useState("");

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLSelectElement | HTMLTextAreaElement
    >
  ) => {
    const { name, value, type } = e.target;

    if (type === "checkbox") {
      const checked = (e.target as HTMLInputElement).checked;
      setFormData((prev) => ({ ...prev, [name]: checked }));
    } else {
      setFormData((prev) => ({ ...prev, [name]: value }));
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // Basic validation
    if (
      !formData.name ||
      !formData.email ||
      !formData.message ||
      !formData.travelType ||
      !formData.travelStartDate ||
      !formData.privacyPolicy
    ) {
      alert(
        "Please fill in all required fields and accept the privacy policy."
      );
      return;
    }

    // Date validation
    const startDate = new Date(formData.travelStartDate);
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    if (startDate < today) {
      alert("Travel start date cannot be in the past.");
      return;
    }

    setIsSubmitting(true);
    setShowError(false);

    // Prepare form data for the internal API
    const apiFormData = new FormData();
    apiFormData.append("travel-type", formData.travelType);
    apiFormData.append("name", formData.name);
    apiFormData.append("email", formData.email);
    apiFormData.append("phone", formData.phone);
    apiFormData.append("travel-start-date", formData.travelStartDate);
    apiFormData.append("message", formData.message);
    apiFormData.append("preferredContact", formData.preferredContact);
    apiFormData.append("privacy-policy", formData.privacyPolicy ? "on" : "");

    try {
      // Call internal API route
      const response = await fetch("/api/contact", {
        method: "POST",
        body: apiFormData,
      });

      const result = await response.json();

      if (response.ok && result.success) {
        // Show success message
        setShowSuccess(true);

        // Reset form
        setFormData({
          travelType: "",
          name: "",
          email: "",
          phone: "",
          travelStartDate: "",
          message: "",
          preferredContact: "email",
          privacyPolicy: false,
        });

        // Hide success message after 5 seconds
        setTimeout(() => {
          setShowSuccess(false);
        }, 5000);
      } else {
        // Handle API error response
        const errorMessage =
          result.message || `API call failed: ${response.status}`;
        throw new Error(errorMessage);
      }
    } catch (error) {
      const errorMessage =
        error instanceof Error
          ? error.message
          : "There was an error submitting your message. Please try again.";
      setErrorMessage(errorMessage);
      setShowError(true);
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <div>
      <h2 className="text-2xl font-baskervville mb-3">{t.title}</h2>

      {showSuccess && (
        <div className="bg-primary/10 border border-primary rounded-lg p-4 text-center mb-4">
          <h3 className="text-lg font-baskervville mb-1">{t.successTitle}</h3>
          <p className="text-sm">{t.successMessage}</p>
        </div>
      )}

      {showError && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 text-center mb-4">
          <h3 className="text-lg font-baskervville mb-1 text-red-800">Error</h3>
          <p className="text-sm text-red-600">{errorMessage}</p>
        </div>
      )}

      <form
        onSubmit={handleSubmit}
        className="space-y-5 transition-opacity duration-300"
      >
        {/* Travel Type Dropdown */}
        <div>
          <label
            htmlFor="travelType"
            className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >
            {t.travelType}
          </label>
          <div className="relative">
            <select
              id="travelType"
              name="travelType"
              value={formData.travelType}
              onChange={handleInputChange}
              className="w-full appearance-none px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200 pr-8"
              disabled={isSubmitting}
            >
              <option value="" disabled>
                {t.travelTypePlaceholder}
              </option>
              <option value="ski">{t.travelTypeOptions.ski}</option>
              <option value="luxury">{t.travelTypeOptions.luxury}</option>
              <option value="family">{t.travelTypeOptions.family}</option>
              <option value="adventure">{t.travelTypeOptions.adventure}</option>
              <option value="other">{t.travelTypeOptions.other}</option>
            </select>
            <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-3 text-[#285DA6]">
              <svg
                className="fill-current h-4 w-4"
                xmlns="http://www.w3.org/2000/svg"
                viewBox="0 0 20 20"
              >
                <path d="M9.293 12.95l.707.707L15.657 8l-1.414-1.414L10 10.828 5.757 6.586 4.343 8z"></path>
              </svg>
            </div>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="name"
              className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
            >
              {t.name}
            </label>
            <input
              type="text"
              id="name"
              name="name"
              value={formData.name}
              onChange={handleInputChange}
              placeholder={t.namePlaceholder}
              className="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
              required
              disabled={isSubmitting}
            />
          </div>

          <div>
            <label
              htmlFor="email"
              className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
            >
              {t.email}
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              placeholder={t.emailPlaceholder}
              className="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
              required
              disabled={isSubmitting}
            />
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label
              htmlFor="phone"
              className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
            >
              {t.phone}
            </label>
            <input
              type="tel"
              id="phone"
              name="phone"
              value={formData.phone}
              onChange={handleInputChange}
              placeholder={t.phonePlaceholder}
              className="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
              disabled={isSubmitting}
            />
          </div>

          <div>
            <label
              htmlFor="travelStartDate"
              className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
            >
              {t.travelStartDate} <span className="text-red-500">*</span>
            </label>
            <input
              type="date"
              id="travelStartDate"
              name="travelStartDate"
              value={formData.travelStartDate}
              onChange={handleInputChange}
              min={new Date().toISOString().split("T")[0]}
              className="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200"
              required
              disabled={isSubmitting}
            />
          </div>
        </div>

        <div>
          <label
            htmlFor="message"
            className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-1"
          >
            {t.message}
          </label>
          <textarea
            id="message"
            name="message"
            rows={4}
            value={formData.message}
            onChange={handleInputChange}
            placeholder={t.messagePlaceholder}
            className="w-full px-4 py-3 text-sm border border-border/30 rounded-md bg-background focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/60 transition-all duration-200 resize-none"
            required
            disabled={isSubmitting}
          />
        </div>

        <div>
          <label className="block text-xs uppercase tracking-wider text-[#285DA6] font-karla mb-2">
            {t.preferredContact}
          </label>
          <div className="flex space-x-6">
            <label className="flex items-center">
              <input
                type="radio"
                name="preferredContact"
                value="email"
                checked={formData.preferredContact === "email"}
                onChange={handleInputChange}
                className="mr-2 h-4 w-4 text-[#285DA6] focus:ring-[#285DA6]"
                disabled={isSubmitting}
              />
              <span className="text-sm">{t.preferredContactEmail}</span>
            </label>

            <label className="flex items-center">
              <input
                type="radio"
                name="preferredContact"
                value="phone"
                checked={formData.preferredContact === "phone"}
                onChange={handleInputChange}
                className="mr-2 h-4 w-4 text-[#285DA6] focus:ring-[#285DA6]"
                disabled={isSubmitting}
              />
              <span className="text-sm">{t.preferredContactPhone}</span>
            </label>
          </div>
        </div>

        {/* Privacy Policy Checkbox */}
        <div className="mt-4">
          <label className="flex items-start">
            <input
              type="checkbox"
              name="privacyPolicy"
              checked={formData.privacyPolicy}
              onChange={handleInputChange}
              required
              className="mt-0.5 mr-2 h-5 w-5 text-[#285DA6] focus:ring-[#285DA6] rounded"
              disabled={isSubmitting}
            />
            <span className="text-sm">
              {t.privacyPolicyPrefix}
              <a
                href="/privacy-policy"
                className="text-[#285DA6] hover:underline"
              >
                {t.privacyPolicyLink}
              </a>
              {t.privacyPolicyAnd}
              <a
                href="/booking-conditions"
                className="text-[#285DA6] hover:underline"
              >
                {t.bookingConditionsLink}
              </a>
              .
            </span>
          </label>
        </div>

        <div className="pt-4">
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center justify-center px-10 py-3 bg-[#285DA6] text-white rounded-md hover:bg-[#285DA6]/90 transition-all duration-200 text-sm font-medium disabled:opacity-60 disabled:cursor-not-allowed"
          >
            {isSubmitting ? "Submitting..." : t.submit}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="16"
              height="16"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="ml-2"
            >
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          </button>
        </div>
      </form>
    </div>
  );
};

export default ContactForm;
