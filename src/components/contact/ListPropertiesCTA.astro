---
import { getLangFromUrl, buildUrl, useTranslations } from "../../i18n/utils";

// Props for the component
interface Props {
  title?: string;
  description?: string;
  buttonText?: string;
  buttonLink?: string;
}

// Get current language from URL
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

const {
  title = t("contact.listProperties.title"),
  description = t("contact.listProperties.description"),
  buttonText = t("contact.listProperties.button"),
  buttonLink = buildUrl("/contact", lang),
} = Astro.props;
---

<div
  class="bg-gradient-to-r from-[#F8FAFC] to-white p-8 rounded-lg shadow-md border border-[#285DA6]/10 mt-4 relative overflow-hidden"
>
  <!-- Decorative elements -->
  <div
    class="absolute top-0 right-0 w-32 h-32 bg-[#285DA6]/5 rounded-full -translate-y-1/2 translate-x-1/2"
  >
  </div>
  <div
    class="absolute bottom-0 left-0 w-24 h-24 bg-[#285DA6]/5 rounded-full translate-y-1/2 -translate-x-1/2"
  >
  </div>

  <div
    class="flex flex-col md:flex-row md:items-center md:justify-between gap-6 relative z-10"
  >
    <div class="max-w-2xl">
      <!-- Decorative line -->
      <div class="w-12 h-0.5 bg-[#285DA6] mb-4"></div>
      <h3 class="text-2xl font-baskervville mb-3">{title}</h3>
      <p class="text-sm text-foreground/80 leading-relaxed">{description}</p>
    </div>

    <a
      href={buttonLink}
      class="inline-flex items-center justify-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white bg-[#285DA6] hover:bg-[#285DA6]/90 py-3 px-6 rounded transition-all hover:shadow-md whitespace-nowrap self-start md:self-center"
    >
      {buttonText}
      <svg
        xmlns="http://www.w3.org/2000/svg"
        width="16"
        height="16"
        viewBox="0 0 24 24"
        fill="none"
        stroke="currentColor"
        stroke-width="2"
        stroke-linecap="round"
        stroke-linejoin="round"
        class="ml-2"
      >
        <line x1="5" y1="12" x2="19" y2="12"></line>
        <polyline points="12 5 19 12 12 19"></polyline>
      </svg>
    </a>
  </div>
</div>
