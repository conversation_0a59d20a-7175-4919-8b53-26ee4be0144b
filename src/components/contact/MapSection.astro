---
import { getLangFromUrl, useTranslations } from "../../i18n/utils";

// Props for the component
interface Props {
  height?: string;
  address?: string;
  zoom?: number;
}

const {
  height = "h-96",
  address = "Perfect Piste Headquarters, 1234 Luxury Avenue, Zurich, Switzerland 8001",
  zoom = 15,
} = Astro.props;

// Encode the address for the Google Maps URL
const encodedAddress = encodeURIComponent(address);
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<!-- Map Section -->
<section class="py-8 container-custom">
  <div class="mx-auto">
    <div class="bg-white p-6 rounded-lg shadow-sm border border-border/20 mb-4">
      <h3 class="text-xl font-baskervville mb-3">
        {t("contact.location.title")}
      </h3>
      <p class="text-sm text-foreground/70 mb-4">
        {t("contact.location.description")}
      </p>

      <div class={`${height} relative rounded-md overflow-hidden`}>
        <iframe
          width="100%"
          height="100%"
          style="border:0;"
          loading="lazy"
          allowfullscreen
          referrerpolicy="no-referrer-when-downgrade"
          src={`https://www.google.com/maps/embed/v1/place?key=AIzaSyBFw0Qbyq9zTFTd-tUY6dZWTgaQzuU17R8&q=${encodedAddress}&zoom=${zoom}`}
          class="rounded-md shadow-sm"
        >
        </iframe>
      </div>
    </div>
  </div>
</section>
