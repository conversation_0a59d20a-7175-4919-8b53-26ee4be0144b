import { useState, useEffect } from "react";
import { getCookieConsent, setCookieConsent } from "../../utils/cookieConsent";

interface CookieConsentProps {
  className?: string;
}

const CookieConsent = ({ className = "" }: CookieConsentProps) => {
  const [isVisible, setIsVisible] = useState(false);
  const [showPreferences, setShowPreferences] = useState(false);
  const [preferences, setPreferences] = useState({
    necessary: true, // Always true and disabled
    analytics: false,
    marketing: false,
    preferences: false,
  });

  useEffect(() => {
    // Check if user has already given consent
    const consent = getCookieConsent();
    if (!consent) {
      // If no consent is stored, show the banner after a short delay
      const timer = setTimeout(() => {
        setIsVisible(true);
      }, 1000);
      return () => clearTimeout(timer);
    } else {
      // If consent exists, set the preferences
      setPreferences({
        ...preferences,
        analytics: consent.analytics || false,
        marketing: consent.marketing || false,
        preferences: consent.preferences || false,
      });
    }
  }, []);

  const handleAcceptAll = () => {
    const newPreferences = {
      necessary: true,
      analytics: true,
      marketing: true,
      preferences: true,
    };
    setPreferences(newPreferences);
    setCookieConsent(newPreferences);
    setIsVisible(false);
  };

  const handleRejectAll = () => {
    const newPreferences = {
      necessary: true,
      analytics: false,
      marketing: false,
      preferences: false,
    };
    setPreferences(newPreferences);
    setCookieConsent(newPreferences);
    setIsVisible(false);
  };

  const handleSavePreferences = () => {
    setCookieConsent(preferences);
    setIsVisible(false);
    setShowPreferences(false);
  };

  const handlePreferenceChange = (key: keyof typeof preferences) => {
    if (key === "necessary") return; // Cannot change necessary cookies
    setPreferences({
      ...preferences,
      [key]: !preferences[key],
    });
  };

  if (!isVisible) return null;

  return (
    <div
      className={`fixed bottom-0 left-0 right-0 z-[100] ${className}`}
      data-cookie-consent
      style={
        {
          "--cookie-primary": "#285DA6",
          "--cookie-primary-hover": "#1e4a8d",
          "--cookie-primary-light": "rgba(40, 93, 166, 0.1)",
        } as React.CSSProperties
      }
    >
      <div className="bg-background border-t border-border/30 shadow-lg animate-fade-in">
        <div className="container-custom py-6">
          {!showPreferences ? (
            <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-6">
              <div className="max-w-3xl">
                <h3 className="text-lg font-baskervville mb-2">
                  Cookie Preferences
                </h3>
                <p className="text-foreground/80 text-sm mb-0">
                  We use cookies to enhance your browsing experience, serve
                  personalized content, and analyze our traffic. By clicking
                  "Accept All", you consent to our use of cookies.
                </p>
              </div>
              <div className="flex flex-wrap gap-3 mt-2 md:mt-0">
                <button
                  onClick={() => setShowPreferences(true)}
                  className="py-3 px-6 min-w-0 text-sm border rounded transition-all duration-300 font-karla font-bold uppercase tracking-wider"
                  style={{
                    borderColor: "var(--cookie-primary)",
                    color: "var(--cookie-primary)",
                    backgroundColor: "transparent",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary-light)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                  }}
                >
                  Preferences
                </button>
                <button
                  onClick={handleRejectAll}
                  className="py-3 px-6 min-w-0 text-sm border rounded transition-all duration-300 font-karla font-bold uppercase tracking-wider"
                  style={{
                    borderColor: "var(--cookie-primary)",
                    color: "var(--cookie-primary)",
                    backgroundColor: "transparent",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary-light)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                  }}
                >
                  Reject All
                </button>
                <button
                  onClick={handleAcceptAll}
                  className="py-3 px-6 min-w-0 text-sm border rounded transition-all duration-300 font-karla font-bold uppercase tracking-wider text-white"
                  style={{
                    backgroundColor: "var(--cookie-primary)",
                    borderColor: "var(--cookie-primary)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary-hover)";
                    e.currentTarget.style.borderColor =
                      "var(--cookie-primary-hover)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary)";
                    e.currentTarget.style.borderColor = "var(--cookie-primary)";
                  }}
                >
                  Accept All
                </button>
              </div>
            </div>
          ) : (
            <div className="animate-fade-in">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-baskervville">
                  Cookie Preferences
                </h3>
                <button
                  onClick={() => setShowPreferences(false)}
                  className="text-foreground/70 transition-colors duration-200"
                  style={{ color: "var(--foreground)" }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.color = "var(--cookie-primary)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.color = "var(--foreground)";
                  }}
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <line x1="18" y1="6" x2="6" y2="18"></line>
                    <line x1="6" y1="6" x2="18" y2="18"></line>
                  </svg>
                </button>
              </div>

              <p className="text-foreground/80 text-sm mb-4">
                Customize your cookie preferences. Necessary cookies help make
                our website usable by enabling basic functions.
              </p>

              <div className="space-y-4 mb-6">
                <div className="flex items-center justify-between p-3 border border-border/50 rounded">
                  <div>
                    <h4 className="font-baskervville text-base">
                      Necessary Cookies
                    </h4>
                    <p className="text-foreground/70 text-xs">
                      Essential for the website to function properly.
                    </p>
                  </div>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={preferences.necessary}
                      disabled
                      className="sr-only"
                      id="necessary-cookies"
                    />
                    <label
                      htmlFor="necessary-cookies"
                      className="block w-10 h-5 rounded-full cursor-not-allowed opacity-70"
                      style={{ backgroundColor: "var(--cookie-primary)" }}
                    >
                      <span className="absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform transform translate-x-5"></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 border border-border/50 rounded">
                  <div>
                    <h4 className="font-baskervville text-base">
                      Analytics Cookies
                    </h4>
                    <p className="text-foreground/70 text-xs">
                      Help us improve our website by collecting anonymous
                      information.
                    </p>
                  </div>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={preferences.analytics}
                      onChange={() => handlePreferenceChange("analytics")}
                      className="sr-only"
                      id="analytics-cookies"
                    />
                    <label
                      htmlFor="analytics-cookies"
                      className="block w-10 h-5 rounded-full cursor-pointer transition-colors duration-200"
                      style={{
                        backgroundColor: preferences.analytics
                          ? "var(--cookie-primary)"
                          : "var(--secondary)",
                      }}
                    >
                      <span
                        className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform transform ${
                          preferences.analytics
                            ? "translate-x-5"
                            : "translate-x-0"
                        }`}
                      ></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 border border-border/50 rounded">
                  <div>
                    <h4 className="font-baskervville text-base">
                      Marketing Cookies
                    </h4>
                    <p className="text-foreground/70 text-xs">
                      Used to track visitors across websites to display relevant
                      advertisements.
                    </p>
                  </div>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={preferences.marketing}
                      onChange={() => handlePreferenceChange("marketing")}
                      className="sr-only"
                      id="marketing-cookies"
                    />
                    <label
                      htmlFor="marketing-cookies"
                      className="block w-10 h-5 rounded-full cursor-pointer transition-colors duration-200"
                      style={{
                        backgroundColor: preferences.marketing
                          ? "var(--cookie-primary)"
                          : "var(--secondary)",
                      }}
                    >
                      <span
                        className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform transform ${
                          preferences.marketing
                            ? "translate-x-5"
                            : "translate-x-0"
                        }`}
                      ></span>
                    </label>
                  </div>
                </div>

                <div className="flex items-center justify-between p-3 border border-border/50 rounded">
                  <div>
                    <h4 className="font-baskervville text-base">
                      Preference Cookies
                    </h4>
                    <p className="text-foreground/70 text-xs">
                      Allow the website to remember choices you make to provide
                      enhanced functionality.
                    </p>
                  </div>
                  <div className="relative">
                    <input
                      type="checkbox"
                      checked={preferences.preferences}
                      onChange={() => handlePreferenceChange("preferences")}
                      className="sr-only"
                      id="preference-cookies"
                    />
                    <label
                      htmlFor="preference-cookies"
                      className="block w-10 h-5 rounded-full cursor-pointer transition-colors duration-200"
                      style={{
                        backgroundColor: preferences.preferences
                          ? "var(--cookie-primary)"
                          : "var(--secondary)",
                      }}
                    >
                      <span
                        className={`absolute left-0.5 top-0.5 bg-white w-4 h-4 rounded-full transition-transform transform ${
                          preferences.preferences
                            ? "translate-x-5"
                            : "translate-x-0"
                        }`}
                      ></span>
                    </label>
                  </div>
                </div>
              </div>

              <div className="flex justify-end gap-3">
                <button
                  onClick={() => setShowPreferences(false)}
                  className="py-2 px-4 min-w-0 text-xs border rounded transition-all duration-300 font-karla font-bold uppercase tracking-wider"
                  style={{
                    borderColor: "var(--cookie-primary)",
                    color: "var(--cookie-primary)",
                    backgroundColor: "transparent",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary-light)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor = "transparent";
                  }}
                >
                  Cancel
                </button>
                <button
                  onClick={handleSavePreferences}
                  className="py-2 px-4 min-w-0 text-xs border rounded transition-all duration-300 font-karla font-bold uppercase tracking-wider text-white"
                  style={{
                    backgroundColor: "var(--cookie-primary)",
                    borderColor: "var(--cookie-primary)",
                  }}
                  onMouseEnter={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary-hover)";
                    e.currentTarget.style.borderColor =
                      "var(--cookie-primary-hover)";
                  }}
                  onMouseLeave={(e) => {
                    e.currentTarget.style.backgroundColor =
                      "var(--cookie-primary)";
                    e.currentTarget.style.borderColor = "var(--cookie-primary)";
                  }}
                >
                  Save Preferences
                </button>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export default CookieConsent;
