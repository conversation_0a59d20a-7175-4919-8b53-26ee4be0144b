---
interface Props {
  name: string;
  description: string;
  activities: string[];
  imageUrl: string;
}

const { name, description, activities, imageUrl } = Astro.props;
---

<section id="experience" class="py-16">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
      <div>
        <p class="section-micro-headline" style="color: #285DA6;">Discover</p>
        <h2 class="text-3xl md:text-4xl font-baskervville mb-6">
          Experience {name}
        </h2>
        <p class="text-lg mb-8 leading-relaxed">
          {description}
        </p>
        <div class="flex flex-wrap gap-3">
          {
            activities.map((activity) => (
              <div class="px-4 py-2 rounded-full text-sm bg-primary/10 text-primary font-medium hover:bg-primary/20 transition-colors">
                {activity}
              </div>
            ))
          }
        </div>
      </div>
      <div>
        <div
          class="rounded-lg overflow-hidden h-96 shadow-glow border border-border/20 transform hover:scale-[1.02] transition-transform duration-500"
        >
          <img
            src={imageUrl}
            alt={`${name} Experience`}
            class="w-full h-full object-cover"
          />
        </div>
      </div>
    </div>
  </div>
</section>
