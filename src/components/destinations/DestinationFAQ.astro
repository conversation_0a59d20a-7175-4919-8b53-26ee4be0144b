---
import { Accordion } from "../ui/accordion";

interface FAQ {
  id: string;
  question: string;
  answer: string;
  destination_id: string;
  created_at: string;
  updated_at: string;
  deleted_at?: string | null;
}

interface Props {
  destinationName: string;
  faqs: FAQ[];
}

const { destinationName, faqs } = Astro.props;

// Only show the section if there are FAQs
if (!faqs || faqs.length === 0) {
  return null;
}

// Transform FAQs to the format expected by the Accordion component
const accordionItems = faqs.map(faq => ({
  question: faq.question,
  answer: faq.answer
}));
---

<section class="py-12 sm:py-16 lg:py-20 bg-gradient-to-b from-background to-accent/5">
  <div class="container-custom max-w-5xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Decorative Elements -->
    <div class="absolute inset-0 overflow-hidden pointer-events-none">
      <div class="absolute top-20 left-4 sm:left-10 w-24 h-24 sm:w-32 sm:h-32 bg-[#285DA6]/5 rounded-full blur-3xl"></div>
      <div class="absolute bottom-20 right-4 sm:right-10 w-32 h-32 sm:w-40 sm:h-40 bg-accent/10 rounded-full blur-3xl"></div>
    </div>

    <!-- Section Header -->
    <div class="relative z-10 text-center mb-12 sm:mb-16">
      <p class="section-micro-headline font-karla tracking-wider uppercase text-[#285DA6] mb-4">
        Frequently Asked Questions
      </p>
      <h2 class="section-title mb-6">
        Everything You Need to Know About {destinationName}
      </h2>
      <p class="text-lg text-foreground/70 font-karla max-w-3xl mx-auto">
        Get answers to common questions about visiting {destinationName}, from travel tips to local insights.
      </p>
    </div>

    <!-- FAQ Content -->
    <div class="relative z-10 flex justify-center">
      <div class="w-full max-w-5xl">
        <Accordion items={accordionItems} client:load />
      </div>
    </div>

    <!-- CTA Section -->
    <div class="relative z-10 mt-12 sm:mt-16">
      <div class="bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] rounded-xl sm:rounded-2xl p-6 sm:p-8 lg:p-12 text-center shadow-xl">
        <h3 class="text-xl sm:text-2xl md:text-3xl font-baskervville mb-3 sm:mb-4 text-white">
          Still have questions about {destinationName}?
        </h3>
        <p class="text-white/90 font-karla mb-6 sm:mb-8 max-w-2xl mx-auto text-base sm:text-lg">
          Our expert concierge team has extensive knowledge of {destinationName} and can help with any specific questions about your stay.
        </p>

        <div class="flex flex-col gap-3 sm:gap-4 sm:flex-row justify-center">
          <a
            href="/contact"
            class="inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 bg-white text-[#285DA6] rounded-lg sm:rounded-xl font-karla font-bold text-xs sm:text-sm uppercase tracking-[0.05em] hover:bg-white/90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 w-4 h-4 sm:w-5 sm:h-5">
              <path d="M4 4h16c1.1 0 2 .9 2 2v12c0 1.1-.9 2-2 2H4c-1.1 0-2-.9-2-2V6c0-1.1.9-2 2-2z"/>
              <polyline points="22,6 12,13 2,6"/>
            </svg>
            Contact Our Team
          </a>
          <a
            href="/ai-search?query=I%20have%20questions%20about%20ski%20destinations%20and%20would%20like%20personalized%20recommendations%20for%20my%20trip&user_message=I%20have%20questions%20about%20ski%20destinations%20and%20would%20like%20personalized%20recommendations%20for%20my%20trip&ai_search=true"
            class="inline-flex items-center justify-center px-6 sm:px-8 py-3 sm:py-4 border-2 border-white text-white rounded-lg sm:rounded-xl font-karla font-bold text-xs sm:text-sm uppercase tracking-[0.05em] hover:bg-white hover:text-[#285DA6] transition-all duration-300 transform hover:-translate-y-1"
          >
            <svg xmlns="http://www.w3.org/2000/svg" width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 w-4 h-4 sm:w-5 sm:h-5">
              <path d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.582a.5.5 0 0 1 0 .962L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"/>
              <path d="M20 3v4"/>
              <path d="M22 5h-4"/>
              <path d="M4 17v2"/>
              <path d="M5 18H3"/>
            </svg>
            Ask AI Concierge
          </a>
        </div>
      </div>
    </div>
  </div>
</section>
