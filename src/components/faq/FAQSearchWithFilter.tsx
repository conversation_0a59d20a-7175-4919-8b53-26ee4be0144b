import React, { useState, useMemo } from "react";
import { Search, X } from "lucide-react";
import { Accordion } from "../ui/accordion";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";
import { getFAQsByLanguage, getAllFAQsByLanguage } from "../../data/faqData";

interface FAQSearchWithFilterProps {
  lang: Lang;
}

const FAQSearchWithFilter: React.FC<FAQSearchWithFilterProps> = ({ lang }) => {
  const [searchQuery, setSearchQuery] = useState("");
  const t = useTranslations(lang);

  // Get FAQ data for the current language
  const faqCategories = getFAQsByLanguage(lang);
  const allFAQs = getAllFAQsByLanguage(lang);

  // Filter FAQs based on search query
  const filteredFAQs = useMemo(() => {
    if (!searchQuery.trim()) {
      // Return all categories with their FAQs
      const result: Record<string, typeof allFAQs> = {};
      faqCategories.forEach((category) => {
        result[category.name] = category.faqs;
      });
      return result;
    }

    const query = searchQuery.toLowerCase();
    const filtered = allFAQs.filter(
      (faq) =>
        faq.question.toLowerCase().includes(query) ||
        faq.answer.toLowerCase().includes(query)
    );

    // Group filtered results by category
    const groupedResults: Record<string, typeof allFAQs> = {};
    filtered.forEach((faq) => {
      if (!groupedResults[faq.category]) {
        groupedResults[faq.category] = [];
      }
      groupedResults[faq.category].push(faq);
    });

    return groupedResults;
  }, [searchQuery, faqCategories, allFAQs]);

  const clearSearch = () => {
    setSearchQuery("");
  };

  const totalResults = Object.values(filteredFAQs).reduce(
    (total, faqs) => total + faqs.length,
    0
  );

  return (
    <div className="space-y-12 sm:space-y-16">
      {/* Search Component */}
      <div className="relative max-w-2xl mx-auto px-4 sm:px-0">
        <div className="relative">
          <div className="absolute inset-y-0 left-0 pl-3 sm:pl-4 flex items-center pointer-events-none">
            <Search className="h-4 w-4 sm:h-5 sm:w-5 text-[#285DA6]/60" />
          </div>
          <input
            type="text"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            placeholder={t("faq.search.placeholder")}
            className="w-full pl-10 sm:pl-12 pr-10 sm:pr-12 py-3 sm:py-4 bg-white/80 backdrop-blur-sm border border-border/30 rounded-lg sm:rounded-xl shadow-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 font-karla text-sm sm:text-base text-foreground placeholder:text-foreground/50"
          />
          {searchQuery && (
            <button
              onClick={clearSearch}
              className="absolute inset-y-0 right-0 pr-3 sm:pr-4 flex items-center text-[#285DA6]/60 hover:text-[#285DA6] transition-colors duration-200"
              aria-label="Clear search"
            >
              <X className="h-4 w-4 sm:h-5 sm:w-5" />
            </button>
          )}
        </div>

        {searchQuery && (
          <div className="mt-3 text-xs sm:text-sm text-foreground/60 font-karla text-center px-2">
            {totalResults > 0 ? (
              <>
                {t("faq.search.found")}{" "}
                <span className="font-semibold text-[#285DA6]">
                  {totalResults}
                </span>{" "}
                {totalResults === 1
                  ? t("faq.search.results")
                  : t("faq.search.resultsPlural")}{" "}
                {t("faq.search.for")}{" "}
                <span className="font-semibold text-[#285DA6]">
                  "{searchQuery}"
                </span>
              </>
            ) : (
              <>
                {t("faq.search.noResultsFor")}{" "}
                <span className="font-semibold text-[#285DA6]">
                  "{searchQuery}"
                </span>
              </>
            )}
          </div>
        )}
      </div>

      {/* FAQ Results */}
      {totalResults > 0 ? (
        <div className="space-y-8 sm:space-y-12 lg:space-y-16">
          {Object.entries(filteredFAQs).map(([category, faqs]) => {
            if (faqs.length === 0) return null;

            const getCategoryIcon = (categoryName: string) => {
              // Find the category by name to get its ID
              const category = faqCategories.find(
                (cat) => cat.name === categoryName
              );
              const categoryId = category?.id || "";

              switch (categoryId) {
                case "booking-payments":
                  return (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <rect width="20" height="14" x="2" y="5" rx="2" />
                      <line x1="2" y1="10" x2="22" y2="10" />
                    </svg>
                  );
                case "properties-amenities":
                  return (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M3 21h18" />
                      <path d="M5 21V7l8-4v18" />
                      <path d="M19 21V11l-6-4" />
                    </svg>
                  );
                case "services-support":
                  return (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2" />
                      <circle cx="9" cy="7" r="4" />
                      <path d="M22 21v-2a4 4 0 0 0-3-3.87" />
                      <path d="M16 3.13a4 4 0 0 1 0 7.75" />
                    </svg>
                  );
                case "travel-destinations":
                  return (
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="24"
                      height="24"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="white"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z" />
                      <polyline points="14,2 14,8 20,8" />
                      <line x1="16" y1="13" x2="8" y2="13" />
                      <line x1="16" y1="17" x2="8" y2="17" />
                      <polyline points="10,9 9,9 8,9" />
                    </svg>
                  );
                default:
                  return null;
              }
            };

            return (
              <div
                key={category}
                className="bg-white/80 backdrop-blur-sm rounded-xl sm:rounded-2xl p-4 sm:p-6 lg:p-8 shadow-lg border border-border/20 hover:shadow-xl transition-all duration-300"
              >
                <div className="flex items-center mb-6 sm:mb-8">
                  <div className="w-10 h-10 sm:w-12 sm:h-12 bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] rounded-lg sm:rounded-xl flex items-center justify-center mr-3 sm:mr-4 shadow-lg flex-shrink-0">
                    {getCategoryIcon(category)}
                  </div>
                  <div className="flex-1 min-w-0">
                    <h2 className="text-lg sm:text-xl lg:text-2xl font-baskervville text-foreground truncate">
                      {category}
                    </h2>
                    {searchQuery && (
                      <span className="text-xs sm:text-sm text-[#285DA6] font-karla font-semibold block sm:hidden">
                        {faqs.length}{" "}
                        {faqs.length === 1
                          ? t("faq.search.results")
                          : t("faq.search.resultsPlural")}
                      </span>
                    )}
                  </div>
                  {searchQuery && (
                    <span className="hidden sm:block text-sm text-[#285DA6] font-karla font-semibold ml-4">
                      {faqs.length}{" "}
                      {faqs.length === 1
                        ? t("faq.search.results")
                        : t("faq.search.resultsPlural")}
                    </span>
                  )}
                </div>
                <Accordion
                  items={faqs.map((faq) => ({
                    question: faq.question,
                    answer: faq.answer,
                  }))}
                />
              </div>
            );
          })}
        </div>
      ) : searchQuery ? (
        <div className="text-center py-8 sm:py-12 px-4">
          <div className="w-12 h-12 sm:w-16 sm:h-16 bg-[#285DA6]/10 rounded-full flex items-center justify-center mx-auto mb-4">
            <Search className="h-6 w-6 sm:h-8 sm:w-8 text-[#285DA6]/60" />
          </div>
          <h3 className="text-lg sm:text-xl font-baskervville text-foreground mb-2">
            {t("faq.search.noResults")}
          </h3>
          <p className="text-sm sm:text-base text-foreground/60 font-karla mb-4 max-w-md mx-auto">
            {t("faq.search.noResultsDescription")}
          </p>
          <button
            onClick={clearSearch}
            className="inline-flex items-center justify-center px-4 sm:px-6 py-2 sm:py-3 bg-[#285DA6] text-white rounded-lg sm:rounded-xl font-karla font-bold text-xs sm:text-sm uppercase tracking-[0.05em] hover:bg-[#285DA6]/90 transition-colors"
          >
            {t("faq.search.showAllFAQs")}
          </button>
        </div>
      ) : null}
    </div>
  );
};

export default FAQSearchWithFilter;
