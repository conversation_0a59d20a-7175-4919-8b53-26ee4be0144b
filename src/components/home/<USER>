---
import "../../styles/magical-experience.css";
import "../../styles/ai-search-hero.css";
import ExpandableHeaderSearch from "../search/ExpandableHeaderSearch";
---

<div class="container-custom hero-section-container">
  <section
    class="relative overflow-hidden rounded-lg"
    aria-label="Welcome to our collection of luxury hotels"
  >
    <div class="hero-container">
      <div class="hero-content">
        <h1 class="hero-title">
          Find your <span class="luxury-accent">Perfect Piste</span>
        </h1>
        <p class="hero-subtitle">
          Let our Perfect Piste AI the best ski accommodation recommendations
          tailored for you.
        </p>
        <div class="hero-search-container">
          <ExpandableHeaderSearch client:load isExpandedView={true} />
        </div>
      </div>
    </div>
  </section>
</div>
