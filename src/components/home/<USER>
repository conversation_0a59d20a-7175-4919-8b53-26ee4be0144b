---
import DestinationCard from "../destinations/DestinationCard.astro";
import { useTranslations } from "../../i18n/utils";
import type { <PERSON> } from "../../i18n/ui";

interface Props {
  lang: Lang;
}

const { lang } = Astro.props;
const t = useTranslations(lang);

// Helper function to get image URLs (keeping original images)
function getCategoryImageUrl(id: string): string {
  const imageMap: Record<string, string> = {
    beginners:
      "https://wallpaperbat.com/img/270245-image-men-helmet-sun-sports-jacket-winter-snow-skiing-1920x1080.jpg",
    "beginner-family":
      "https://wallpaperbat.com/img/136977-skiing-hd-wallpaper-and-background-image.jpg",
    "off-piste": "https://wallpaperbat.com/img/49481-snow-skiing-wallpaper.jpg",
    "family-friendly":
      "https://images.unsplash.com/photo-1535640368727-187c8a674b5c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    party:
      "https://wallpaperbat.com/img/24512-skiing-wallpaper-top-free-skiing-background.jpg",
    "budget-friendly":
      "https://images.unsplash.com/photo-1520364311437-283acc84fe43?ixlib=rb-4.0.3&auto=format&fit=crop&w=1200&q=80",
    multigenerational:
      "https://wallpaperbat.com/img/341871-wallpaper-of-skiing-snow-winter-sport-background-hd-image.jpg",
    couples:
      "https://wallpaperbat.com/img/166824-ski-jacket-brands-skiing-wallpaper.jpg",
    "late-season":
      "https://wallpaperbat.com/img/146426-ski-resort-wallpaper.jpg",
    advanced:
      "https://wallpaperbat.com/img/292211-hd-wallpaper-person-skiing-on-snow-with-gear-set-person-doing.jpg",
  };
  return imageMap[id] || "";
}

// Helper function to get property counts
function getCategoryPropertyCount(id: string): number {
  const countMap: Record<string, number> = {
    beginners: 12,
    "beginner-family": 15,
    "off-piste": 10,
    "family-friendly": 18,
    party: 8,
    "budget-friendly": 14,
    multigenerational: 14,
    couples: 14,
    "late-season": 14,
    advanced: 14,
  };
  return countMap[id] || 12;
}

// Create translated categories array
const categoryIds = [
  "beginners",
  "beginner-family",
  "off-piste",
  "family-friendly",
  "party",
  "budget-friendly",
  "multigenerational",
  "couples",
  "late-season",
  "advanced",
];

const categories = categoryIds.map((id) => ({
  id,
  name: t(`categories.${id}.name`),
  category: t(`categories.${id}.category`),
  propertyCount: getCategoryPropertyCount(id),
  imageUrl: getCategoryImageUrl(id),
  description: t(`categories.${id}.description`),
  searchPrompt: t(`categories.${id}.searchPrompt`),
}));
---

<section>
  <div class="container-custom">
    <div class="flex flex-col sm:flex-row justify-between items-start">
      <div>
        <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla"
          >{t("home.categories.sectionLabel")}</span
        >
        <h2
          class="text-2xl sm:text-[28px] md:text-[32px] font-baskervville uppercase tracking-[0.1em] mt-2 sm:whitespace-nowrap"
        >
          {t("home.categories.title")}
        </h2>
        <p class="font-baskervville text-sm sm:text-base mt-2 max-w-2xl">
          {t("home.categories.subtitle")}
        </p>
      </div>
      <a
        href="/destinations"
        class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-[#285DA6] border-b border-[#285DA6] pb-1 transition-all hover:border-transparent mt-4 sm:mt-1"
      >
        {t("home.categories.searchNow")}
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          stroke-width="2"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="ml-2"
        >
          <line x1="5" y1="12" x2="19" y2="12"></line>
          <polyline points="12 5 19 12 12 19"></polyline>
        </svg>
      </a>
    </div>

    <style>
      h2 {
        font-size: clamp(1.5rem, 1.8vw, 1.8rem);
        overflow: visible;
        max-width: 100%;
      }

      @media (max-width: 1200px) {
        h2 {
          font-size: clamp(1.2rem, 1.4vw, 1.4rem);
          letter-spacing: 0.08em;
        }
      }

      /* Carousel Styles */
      .carousel-container {
        position: relative;
        overflow: hidden;
        width: 100%;
      }

      .carousel-track {
        display: flex;
        transition: transform 0.5s ease-in-out;
        width: 100%;
      }

      .carousel-item {
        flex: 0 0 100%;
        padding: 0 0.5rem;
        box-sizing: border-box;
      }

      .carousel-controls {
        display: flex;
        justify-content: center;
        margin-top: 1.5rem;
        gap: 1rem;
      }

      .carousel-indicators {
        display: flex;
        gap: 0.5rem;
        justify-content: center;
        margin-top: 1rem;
      }

      .carousel-indicator {
        width: 8px;
        height: 8px;
        border-radius: 50%;
        background-color: rgba(0, 0, 0, 0.2);
        cursor: pointer;
        transition: background-color 0.3s ease;
      }

      .carousel-indicator.active {
        background-color: #285da6;
      }

      @media (min-width: 768px) {
        .carousel-item {
          flex: 0 0 33.333%;
        }
      }
    </style>

    <!-- Carousel Container -->
    <div class="carousel-container mt-6 sm:mt-8">
      <div class="carousel-track" id="category-track">
        {
          categories.map((category) => (
            <div class="carousel-item px-2">
              <DestinationCard
                id={category.id}
                name={category.name}
                propertyCount={category.propertyCount}
                imageUrl={category.imageUrl}
                category={category.category}
                description={category.description}
                searchPrompt={category.searchPrompt}
              />
            </div>
          ))
        }
      </div>

      <!-- Carousel Controls -->
      <div class="carousel-controls mt-4">
        <button
          class="carousel-prev bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-3 shadow-md transition-all duration-300 mr-4 mb-1"
          aria-label="Previous slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M15 18l-6-6 6-6"></path>
          </svg>
        </button>
        <button
          class="carousel-next bg-white/80 backdrop-blur-sm hover:bg-white rounded-full p-3 shadow-md transition-all duration-300 mb-1"
          aria-label="Next slide"
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="24"
            height="24"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            stroke-width="2"
            stroke-linecap="round"
            stroke-linejoin="round"
          >
            <path d="M9 18l6-6-6-6"></path>
          </svg>
        </button>
      </div>

      <!-- Carousel Indicators -->
      <!-- <div class="carousel-indicators mt-4">
        {
          Array.from({ length: categories.length }, (_, i) => (
            <button
              class={`carousel-indicator ${i === 0 ? "active" : ""}`}
              data-index={i}
              aria-label={`Go to slide ${i + 1}`}
            />
          ))
        }
      </div> -->
    </div>

    <script>
      // Client-side carousel functionality
      document.addEventListener("DOMContentLoaded", () => {
        const track = document.getElementById("category-track");
        const items = track?.querySelectorAll(".carousel-item");
        const prevBtn = document.querySelector(".carousel-prev");
        const nextBtn = document.querySelector(".carousel-next");
        const indicators = document.querySelectorAll(".carousel-indicator");
        let currentIndex = 0;
        const itemCount = items?.length || 0;

        // Function to get current items per view based on screen width
        const getItemsPerView = () => (window.innerWidth >= 768 ? 3 : 1);

        // Function to calculate max index based on current view
        const getMaxIndex = () => {
          const itemsPerView = getItemsPerView();
          return Math.max(0, itemCount - itemsPerView);
        };

        // Function to update the carousel position
        const updateCarousel = (index: number) => {
          if (!track || !items) return;

          // Ensure index is within bounds
          const maxIndex = getMaxIndex();
          currentIndex = Math.max(0, Math.min(index, maxIndex));

          // Calculate the translation percentage based on item width
          const itemWidth = 100 / getItemsPerView();
          track.style.transform = `translateX(-${currentIndex * itemWidth}%)`;

          // Update indicators - highlight the indicator that corresponds to the visible items
          indicators.forEach((indicator, i) => {
            // For desktop (3 items per view), we want to highlight the indicator for each group of 3
            // For mobile (1 item per view), we highlight the indicator for each item
            if (i === currentIndex) {
              indicator.classList.add("active");
            } else {
              indicator.classList.remove("active");
            }
          });
        };

        // Event listeners for prev/next buttons
        prevBtn?.addEventListener("click", () => {
          updateCarousel(currentIndex - 1);
        });

        nextBtn?.addEventListener("click", () => {
          updateCarousel(currentIndex + 1);
        });

        // Event listeners for indicators
        indicators.forEach((indicator, index) => {
          indicator.addEventListener("click", () => {
            // When clicking an indicator, go to that specific item
            updateCarousel(index);
          });
        });

        // Initialize carousel
        updateCarousel(0);

        // Handle window resize
        window.addEventListener("resize", () => {
          // Recalculate and update carousel on resize
          updateCarousel(currentIndex);
        });
      });
    </script>
  </div>
</section>
