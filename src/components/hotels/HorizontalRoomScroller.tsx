import React, { useRef, useState, useEffect } from "react";
import { type RoomType } from "../../utils/types";
import { type MealPlanType } from "../booking/MealPlanSelector";
import RoomDetailsModal from "./RoomDetailsModal";
import RoomSearchFilters from "./RoomSearchFilters";

interface HorizontalRoomScrollerProps {
  roomTypes: RoomType[];
  hotelCurrency: string;
  hotelId: string;
  checkInDate?: string;
  checkOutDate?: string;
  hotelName: string;
  location: string;
  onSelectRoom: (roomType: RoomType, mealPlanKey?: MealPlanType) => void;
  onAvailabilityUpdate?: (availableRooms: any[]) => void;
}

const HorizontalRoomScroller: React.FC<HorizontalRoomScrollerProps> = ({
  roomTypes,
  hotelCurrency,
  hotelId,
  checkInDate,
  checkOutDate,
  onSelectRoom,
  onAvailabilityUpdate,
  hotelName,
  location,
}) => {
  const scrollContainerRef = useRef<HTMLDivElement>(null);
  const [selectedRoomForModal, setSelectedRoomForModal] =
    useState<RoomType | null>(null);
  const [isModalOpen, setIsModalOpen] = useState(false);

  // Convert string dates to Date objects
  const parseDate = (dateStr?: string): Date | null => {
    if (!dateStr) return null;
    const parts = dateStr.split("-");
    if (parts.length === 3) {
      // Format: DD-MM-YYYY
      return new Date(
        parseInt(parts[2]),
        parseInt(parts[1]) - 1,
        parseInt(parts[0])
      );
    }
    // Try to parse as a regular date string
    const date = new Date(dateStr);
    return isNaN(date.getTime()) ? null : date;
  };

  const [dates, setDates] = useState({
    checkIn: parseDate(checkInDate),
    checkOut: parseDate(checkOutDate),
  });

  const [guests, setGuests] = useState({
    adults: 1,
    children: 0,
    infants: 0,
  });

  // Update dates when props change
  useEffect(() => {
    setDates({
      checkIn: parseDate(checkInDate),
      checkOut: parseDate(checkOutDate),
    });
  }, [checkInDate, checkOutDate]);

  // Handle date changes
  const handleDateChange = (startDate: Date | null, endDate: Date | null) => {
    setDates({
      checkIn: startDate,
      checkOut: endDate,
    });
  };

  // Handle guest changes
  const handleGuestChange = (newGuests: typeof guests) => {
    setGuests(newGuests);
  };

  // Get currency code from room price details or fallback to hotel currency
  const getCurrencyCode = (room?: any) => {
    // First try to get currency from room price details (API response)
    if (room?.priceDetails?.currency_code) {
      return room.priceDetails.currency_code;
    }
    if (room?.price?.currency_code) {
      return room.price.currency_code;
    }
    // Fallback to hotel currency, but convert symbol to code if needed
    if (hotelCurrency === "$") return "USD";
    if (hotelCurrency === "€") return "EUR";
    if (hotelCurrency === "£") return "GBP";
    return hotelCurrency || "USD";
  };

  // Format currency for display
  const formatCurrency = (amount: number, room?: any) => {
    const currencyCode = getCurrencyCode(room);
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get room thumbnail image
  const getRoomThumbnail = (room: RoomType) => {
    if (room.thumbnail) return room.thumbnail;
    if (Array.isArray(room.images) && room.images.length > 0) {
      return typeof room.images[0] === "string"
        ? room.images[0]
        : room.images[0] && room.images[0].url
        ? room.images[0].url
        : "";
    }
    return "";
  };

  // Format amenities for display
  const getAmenityName = (amenity: any) => {
    return typeof amenity === "string"
      ? amenity
      : amenity && amenity.name
      ? amenity.name
      : "Amenity";
  };

  // Scroll left
  const scrollLeft = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: -300,
        behavior: "smooth",
      });
    }
  };

  // Scroll right
  const scrollRight = () => {
    if (scrollContainerRef.current) {
      scrollContainerRef.current.scrollBy({
        left: 300,
        behavior: "smooth",
      });
    }
  };

  // Open room details modal
  const openRoomDetailsModal = (room: RoomType) => {
    setSelectedRoomForModal(room);
    setIsModalOpen(true);
  };

  // Close room details modal
  const closeRoomDetailsModal = () => {
    setIsModalOpen(false);
    setSelectedRoomForModal(null);
  };

  return (
    <div className="relative">
      {/* Room Search Filters */}
      <RoomSearchFilters
        hotelId={hotelId}
        checkInDate={dates.checkIn}
        checkOutDate={dates.checkOut}
        onDatesChange={handleDateChange}
        guests={guests}
        onGuestChange={handleGuestChange}
        currencyCode={hotelCurrency}
        onAvailabilityUpdate={onAvailabilityUpdate}
        maxAdults={4}
        maxChildren={4}
        maxInfants={2}
        maxOccupancy={8}
        hotelName={hotelName}
        location={location}
      />

      {/* Room Type Heading */}
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-2xl font-baskervville">
          <span className="text-[#3566ab]">Select</span> Your Room
        </h2>

        {/* Navigation Buttons - Only show if there are multiple rooms */}
        {roomTypes.length > 1 && (
          <div className="flex space-x-2">
            <button
              onClick={scrollLeft}
              className="p-2 rounded-full bg-white border border-[#3566ab]/20 text-[#3566ab] hover:bg-[#3566ab]/5 transition-colors shadow-sm"
              aria-label="Scroll left"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M15 18l-6-6 6-6" />
              </svg>
            </button>
            <button
              onClick={scrollRight}
              className="p-2 rounded-full bg-white border border-[#3566ab]/20 text-[#3566ab] hover:bg-[#3566ab]/5 transition-colors shadow-sm"
              aria-label="Scroll right"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <path d="M9 18l6-6-6-6" />
              </svg>
            </button>
          </div>
        )}
      </div>

      {/* Horizontal Scrollable Container */}
      <div
        ref={scrollContainerRef}
        className="flex overflow-x-auto pb-6 -mx-4 px-4 space-x-6 scrollbar-hide scroll-smooth"
        style={{
          scrollbarWidth: "none" /* Firefox */,
          msOverflowStyle: "none" /* IE and Edge */,
        }}
      >
        {roomTypes.length === 0 ? (
          <div className="w-full p-8 border border-[#3566ab]/10 rounded-lg bg-white shadow-sm text-center">
            <p className="text-foreground/70 mb-2">
              No rooms are currently available for the selected dates.
            </p>
            <p className="text-sm">
              Please try different dates or contact the hotel directly for
              assistance.
            </p>
          </div>
        ) : (
          roomTypes.map((room) => (
            <div
              key={room.id}
              className="flex-none w-full sm:w-[350px] md:w-[400px] bg-white border border-[#3566ab]/10 rounded-xl shadow-md hover:shadow-lg transition-all duration-300"
            >
              {/* Room Image */}
              <div className="relative overflow-hidden rounded-t-xl">
                <img
                  src={getRoomThumbnail(room)}
                  alt={room.name}
                  className="w-full h-[200px] object-cover hover:scale-105 transition-transform duration-700 ease-out"
                />
                {/* Room Type Label */}
                {/* <div className="absolute top-3 left-3 bg-[#3566ab] text-white text-xs px-3 py-1.5 rounded-md uppercase tracking-wider font-medium">
                  {room.bedType?.includes("king") ? "King Room" :
                   room.bedType?.includes("queen") ? "Queen Room" :
                   room.name?.includes("Deluxe") ? "Deluxe Room" :
                   room.name?.includes("Suite") ? "Suite" : "Standard Room"}
                </div> */}
              </div>

              {/* Room Content */}
              <div className="p-5">
                {/* Room Name */}
                <h3 className="text-xl font-baskervville text-[#3566ab] mb-2">
                  {room.name}
                </h3>

                {/* Room Features */}
                <div className="flex flex-wrap gap-3 mb-4">
                  <div className="flex items-center text-sm bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="14"
                      height="14"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="text-[#3566ab] mr-2"
                    >
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                    </svg>
                    <span className="text-foreground/80">
                      Up to {room.maxGuests || room.maxAdults || 2} guests
                    </span>
                  </div>

                  {room.bedType && (
                    <div className="flex items-center text-sm bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab] mr-2"
                      >
                        <path d="M2 3h20v10H2z"></path>
                        <path d="M2 17h20v4H2z"></path>
                      </svg>
                      <span className="text-foreground/80">
                        {room.bedType.charAt(0).toUpperCase() +
                          room.bedType.slice(1)}
                      </span>
                    </div>
                  )}

                  {room.size && (
                    <div className="flex items-center text-sm bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab] mr-2"
                      >
                        <path d="M2 4v16"></path>
                        <path d="M22 4v16"></path>
                        <path d="M2 12h20"></path>
                        <path d="M12 2v20"></path>
                      </svg>
                      <span className="text-foreground/80">
                        {room.size} sq.m
                      </span>
                    </div>
                  )}
                </div>

                {/* Amenities */}
                <div className="mb-4">
                  <div className="flex flex-wrap gap-2">
                    {Array.isArray(room.amenities) &&
                    room.amenities.length > 0 ? (
                      room.amenities?.slice(0, 3).map((amenity, index) => (
                        <span
                          key={index}
                          className="inline-flex items-center px-3 py-1 rounded-full text-xs font-karla bg-[#3566ab]/5 text-foreground/80"
                        >
                          {getAmenityName(amenity)}
                        </span>
                      ))
                    ) : (
                      <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-karla bg-[#3566ab]/5 text-foreground/80">
                        No amenities listed
                      </span>
                    )}
                    {Array.isArray(room.amenities) &&
                      room.amenities.length > 3 && (
                        <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-karla bg-[#3566ab]/10 text-[#3566ab]">
                          +{room.amenities.length - 3} more
                        </span>
                      )}
                  </div>
                </div>

                {/* View More Details Button */}
                <button
                  onClick={() => openRoomDetailsModal(room)}
                  className="w-full py-2 px-4 mb-4 text-sm font-karla text-[#3566ab] border border-[#3566ab]/30 rounded-lg hover:bg-[#3566ab]/5 transition-colors"
                >
                  View More Details
                </button>

                {/* Price and Select Button */}
                <div className="flex items-end justify-between">
                  <div className="text-left">
                    <div className="flex items-center mb-1">
                      <span className="text-xs font-karla uppercase tracking-wider text-foreground/60 line-through mr-2">
                        {formatCurrency(room.price * 1.2)}
                      </span>
                      <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-karla rounded-full">
                        Save 20%
                      </span>
                    </div>
                    <p className="text-xl font-baskervville text-[#3566ab]">
                      {formatCurrency(room.price)}
                    </p>
                    <p className="text-xs font-karla uppercase tracking-wider text-foreground/60">
                      per night
                    </p>
                  </div>
                  <button
                    onClick={() => onSelectRoom(room)}
                    className="py-2.5 px-5 bg-[#3566ab] text-white text-sm font-medium rounded-lg hover:bg-[#3566ab]/90 transition-colors shadow-sm"
                  >
                    Select
                  </button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Room Details Modal */}
      {isModalOpen && selectedRoomForModal && (
        <RoomDetailsModal
          room={selectedRoomForModal}
          hotelCurrency={hotelCurrency}
          isOpen={isModalOpen}
          onClose={closeRoomDetailsModal}
          onSelectRoom={onSelectRoom}
        />
      )}
    </div>
  );
};

export default HorizontalRoomScroller;
