import React, { useState, useEffect } from "react";
import RoomSelection from "./RoomSelection";
import HotelInformation from "./HotelInformation";
import { getRoomAvailability } from "../../utils/dataService";
import { getCurrentUrlParams } from "../../utils/urlParams";
import { getCurrentCurrencyCode } from "../../utils/currencyHelper";
import { type RoomType } from "../../utils/types";
import type { Lang } from "../../i18n/ui";

interface HotelData {
  id: string | number;
  name: string;
  description: string;
  location: string;
  price: number;
  currency: string;
  check_in_time?: string;
  check_out_time?: string;
  images: any[];
  [key: string]: any;
}

interface HotelMainContentProps {
  hotelData: HotelData;
  roomTypes: RoomType[];
  hotelId: string | number;
  nights: number;
  urlAdults?: string | null;
  urlChildren?: string | null;
  urlInfants?: string | null;
  lang?: Lang;
}

const HotelMainContent: React.FC<HotelMainContentProps> = ({
  hotelData,
  roomTypes,
  hotelId,
  nights,
  urlAdults,
  urlChildren,
  urlInfants,
  lang = "en",
}) => {
  // State for room types and dates
  const [currentRoomTypes, setCurrentRoomTypes] = useState(roomTypes);
  const [isInitialLoading, setIsInitialLoading] = useState(true);

  // Initialize dates with defaults first, then update from URL params in useEffect
  const [checkInDate, setCheckInDate] = useState<Date | null>(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  });

  const [checkOutDate, setCheckOutDate] = useState<Date | null>(() => {
    const checkoutDate = new Date();
    checkoutDate.setDate(checkoutDate.getDate() + 1 + (nights || 2));
    return checkoutDate;
  });

  const [isLoading, setIsLoading] = useState(false);

  // Initialize with room types and load availability data
  useEffect(() => {
    setCurrentRoomTypes(roomTypes);

    // Load room availability data on initial mount if we have room types with isLoading flag
    const hasLoadingRooms = roomTypes.some((room: any) => room.isLoading);
    if (hasLoadingRooms && checkInDate && checkOutDate) {
      loadInitialRoomAvailability();
    } else {
      setIsInitialLoading(false);
    }
  }, [roomTypes]);

  // Function to load initial room availability
  const loadInitialRoomAvailability = async () => {
    try {
      setIsInitialLoading(true);
      const result = await getRoomAvailability(
        hotelId,
        checkInDate,
        checkOutDate,
        currentGuests.adults,
        currentGuests.children,
        currentGuests.infants,
        getCurrentCurrencyCode()
      );
      console.log('Initial room availability result:', result);

      if (result && result.availabilityMap) {
        // Update room types with availability data
        const updatedRoomTypes = roomTypes.map((room: any) => {
          const availabilityInfo = result.availabilityMap.get(room.id);
          if (availabilityInfo) {
            return {
              ...room,
              price: availabilityInfo.price?.per_night_amount || room.price,
              available: availabilityInfo.available || false,
              availableRooms: availabilityInfo.available_rooms || 0,
              priceDetails: availabilityInfo.price || null,
              currencyCode: availabilityInfo.price?.currency_code || "USD",
              extra_adults_beyond_capacity: availabilityInfo.extra_adults_beyond_capacity,
              isLoading: false,
            };
          }
          return { ...room, isLoading: false };
        });

        // Filter out rooms without valid pricing information
        const roomsWithPricing = updatedRoomTypes.filter((room: any) => {
          const hasPrice = room.price && room.price > 0;
          const hasPriceDetails = room.priceDetails?.per_night_amount && room.priceDetails.per_night_amount > 0;
          return hasPrice || hasPriceDetails;
        });

        setCurrentRoomTypes(roomsWithPricing);
      }
    } catch (error) {
      console.error("Error loading initial room availability:", error);
      // Remove loading state even on error
      const updatedRoomTypes = roomTypes.map((room: any) => ({
        ...room,
        isLoading: false,
      }));
      setCurrentRoomTypes(updatedRoomTypes);
    } finally {
      setIsInitialLoading(false);
    }
  };

  // State to track current guest counts - initialize with defaults first
  const [currentGuests, setCurrentGuests] = useState(() => {
    // Use URL props or defaults for initial state
    return {
      adults: urlAdults ? parseInt(urlAdults) : 2,
      children: urlChildren ? parseInt(urlChildren) : 0,
      infants: urlInfants ? parseInt(urlInfants) : 0,
    };
  });

  // Track if we've initialized from URL params
  const [hasInitializedFromUrl, setHasInitializedFromUrl] = useState(false);

  // Initialize from URL parameters on client side
  useEffect(() => {
    if (!hasInitializedFromUrl && typeof window !== "undefined") {
      const urlParams = getCurrentUrlParams();

      if (urlParams) {
        // Update dates if they exist in URL and are different from current
        if (urlParams.checkIn && urlParams.checkOut) {
          setCheckInDate(urlParams.checkIn);
          setCheckOutDate(urlParams.checkOut);
        }

        // Update guests if they are different from current
        if (
          urlParams.adults !== currentGuests.adults ||
          urlParams.children !== currentGuests.children ||
          urlParams.infants !== currentGuests.infants
        ) {
          setCurrentGuests({
            adults: urlParams.adults,
            children: urlParams.children,
            infants: urlParams.infants,
          });
        }
      }

      setHasInitializedFromUrl(true);
    }
  }, [hasInitializedFromUrl, currentGuests]);

  // Function to update dates and room types
  // This function only calls the availability API when dates change
  // It doesn't fetch hotel details again, which optimizes API usage
  // Unified function to update room types when dates or guest count changes
  const updateRoomAvailability = async (params: {
    newCheckInDate?: Date | null;
    newCheckOutDate?: Date | null;
    adults?: number;
    children?: number;
    infants?: number;
  }) => {
    // Determine which dates to use (new dates or existing dates)
    const checkIn = params.newCheckInDate || checkInDate;
    const checkOut = params.newCheckOutDate || checkOutDate;

    // If we don't have valid dates, we can't proceed
    if (!checkIn || !checkOut) {
      return;
    }

    // Determine which guest counts to use (new counts or existing counts)
    const adults =
      params.adults !== undefined ? params.adults : currentGuests.adults;
    const children =
      params.children !== undefined ? params.children : currentGuests.children;
    const infants =
      params.infants !== undefined ? params.infants : currentGuests.infants;

    // If guest counts were provided, update the current guests state
    if (
      params.adults !== undefined ||
      params.children !== undefined ||
      params.infants !== undefined
    ) {
      setCurrentGuests({
        adults,
        children,
        infants,
      });
    }

    // If new dates were provided, update the date states
    if (params.newCheckInDate) {
      setCheckInDate(params.newCheckInDate);
    }
    if (params.newCheckOutDate) {
      setCheckOutDate(params.newCheckOutDate);
    }

    setIsLoading(true);
    try {
      const result = await getRoomAvailability(
        hotelId,
        checkIn,
        checkOut,
        adults,
        children,
        infants,
        getCurrentCurrencyCode()
      );
      console.log('Updated room availability result:', result);

      if (result && result.availabilityMap) {
        // Update current room types with new availability data
        const updatedRoomTypes = currentRoomTypes.map((room: any) => {
          const availabilityInfo = result.availabilityMap.get(room.id);
          if (availabilityInfo) {
            return {
              ...room,
              price: availabilityInfo.price?.per_night_amount || room.price,
              available: availabilityInfo.available || false,
              availableRooms: availabilityInfo.available_rooms || 0,
              priceDetails: availabilityInfo.price || null,
              currencyCode: availabilityInfo.price?.currency_code || "USD",
              extra_adults_beyond_capacity: availabilityInfo.extra_adults_beyond_capacity,
              isLoading: false,
            };
          }
          return room;
        });

        // Filter out rooms without valid pricing information
        const roomsWithPricing = updatedRoomTypes.filter((room: any) => {
          const hasPrice = room.price && room.price > 0;
          const hasPriceDetails = room.priceDetails?.per_night_amount && room.priceDetails.per_night_amount > 0;
          return hasPrice || hasPriceDetails;
        });

        setCurrentRoomTypes(roomsWithPricing);
      }
      setIsLoading(false);
    } catch (error) {
      console.error("Error updating room availability:", error);
      setIsLoading(false);
    }
  };

  // Wrapper functions to maintain backward compatibility
  const updateDatesAndRoomTypes = async (
    newCheckInDate: Date | null,
    newCheckOutDate: Date | null
  ) => {
    await updateRoomAvailability({
      newCheckInDate,
      newCheckOutDate,
    });
  };

  const updateRoomTypesForGuestChange = async (
    adults: number,
    children: number,
    infants: number
  ) => {
    await updateRoomAvailability({
      adults,
      children,
      infants,
    });
  };

  return (
    <div className="container-custom py-8 mb-8">
      <div className="mt-8">
        {/* Room Selection Component */}
        <RoomSelection
          roomTypes={currentRoomTypes}
          hotelData={hotelData}
          isLoading={isLoading}
          checkInDate={checkInDate}
          checkOutDate={checkOutDate}
          onRoomTypesUpdate={setCurrentRoomTypes}
          lang={lang}
        />

        {/* Hotel Information Component */}
        <HotelInformation hotelData={hotelData} hotelId={hotelId} />
      </div>
    </div>
  );
};

export default HotelMainContent;
