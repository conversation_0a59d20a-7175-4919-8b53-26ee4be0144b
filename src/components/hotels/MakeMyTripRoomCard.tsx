import React, { useRef, useEffect, useState, useCallback } from "react";
import { type RoomType } from "../../utils/types";
import { type MealPlanType } from "../booking/MealPlanSelector";
import RoomDetailsModal from "./RoomDetailsModal";
import { getCurrencySymbol } from "../../utils/currencyUtils";
import "../../styles/makemytrip-room-card.css";
import "../../styles/makemytrip-date-picker.css";

// Scroll phase enum for better readability
enum ScrollPhase {
  PHASE_1 = "phase-1", // Initial phase - room category column sticky, meal plans scroll
  PHASE_2 = "phase-2", // Transition phase - preparing to allow full page scroll
  PHASE_3 = "phase-3", // Final phase - entire page scrolls normally
}

interface MakeMyTripRoomCardProps {
  roomType: RoomType;
  hotelCurrency: string;
  checkInDate?: string;
  checkOutDate?: string;
  onAddToCart: (roomType: RoomType, mealPlanKey: MealPlanType) => void;
  hotelData?: {
    id: string | number;
    name: string;
    location?: string;
    images?: any[];
    stars?: number;
    rating?: number;
    tags?: string[];
    cancellation_policies?: Array<{
      id: string;
      name: string;
      description: string;
      days_before_checkin: number;
      refund_type: string;
      refund_amount: number;
    }>;
    [key: string]: any;
  };
  guests?: {
    adults: number;
    children: number;
    infants: number;
  };
}

// Standard meal plan descriptions
const mealPlanDescriptions = {
  bb: ["Breakfast included"],
  hb: ["Breakfast included", "One additional meal (lunch or dinner)"],
  fb: ["All meals included (breakfast, lunch, and dinner)"],
  none: ["No meals included"],
};

const MakeMyTripRoomCard: React.FC<MakeMyTripRoomCardProps> = ({
  roomType,
  hotelCurrency,
  checkInDate,
  checkOutDate,
  onAddToCart,
  hotelData,
  guests,
}) => {


  // Get currency code from room price details or fallback to hotel currency
  const getCurrencyCode = () => {
    // First try to get currency from room price details (API response)
    if (roomType.price?.currency_code) {
      return roomType.price.currency_code;
    }
    if (roomType.priceDetails?.currency_code) {
      return roomType.priceDetails.currency_code;
    }
    // Fallback to hotel currency, but convert symbol to code if needed
    if (hotelCurrency === "$") return "USD";
    if (hotelCurrency === "€") return "EUR";
    if (hotelCurrency === "£") return "GBP";
    return hotelCurrency || "USD";
  };
  // Refs for specialized scroll behavior
  const cardRef = useRef<HTMLDivElement>(null);
  const mealPlanSectionRef = useRef<HTMLDivElement>(null);
  const phaseDetectorStartRef = useRef<HTMLDivElement>(null);
  const phaseDetectorMiddleRef = useRef<HTMLDivElement>(null);
  const phaseDetectorEndRef = useRef<HTMLDivElement>(null);

  // State for tracking scroll phases
  const [scrollPhase, setScrollPhase] = useState<ScrollPhase>(
    ScrollPhase.PHASE_1
  );
  const [isRoomDetailsModalOpen, setIsRoomDetailsModalOpen] = useState(false);

  // Get room name
  const getRoomName = () => {
    return roomType.name || roomType.title || "Room";
  };

  // Get room images
  const getRoomImages = () => {
    const images: string[] = [];

    // First, add images from the images array
    if (Array.isArray(roomType.images) && roomType.images.length > 0) {
      roomType.images.forEach((image) => {
        if (typeof image === "string") {
          images.push(image);
        } else if (image && typeof image === "object" && image.url) {
          images.push(image.url);
        }
      });
    }

    // Then add thumbnail only if it's not already in the images array
    if (roomType.thumbnail && !images.includes(roomType.thumbnail)) {
      images.unshift(roomType.thumbnail); // Add to beginning as primary image
    }

    // If no images found, use a placeholder
    if (images.length === 0) {
      images.push("/images/room-placeholder.jpg");
    }

    return images;
  };

  // Format amenities for display
  const formatAmenities = () => {
    if (!roomType.amenities || roomType.amenities.length === 0) {
      return [];
    }

    // Ensure we only have string values for amenities
    const stringAmenities = roomType.amenities.map((amenity) =>
      typeof amenity === "string"
        ? amenity
        : (amenity as any)?.name || "Amenity"
    );

    return stringAmenities;
  };

  // Get meal plan descriptions
  const getMealPlanFeatures = (mealPlanKey: string) => {
    return (
      mealPlanDescriptions[mealPlanKey as keyof typeof mealPlanDescriptions] ||
      mealPlanDescriptions.none
    );
  };

  // Check if extra adults beyond capacity are available
  const hasExtraAdultsAvailable = () => {
    // First check if we have the new API structure
    if (roomType.extra_adults_beyond_capacity &&
        roomType.extra_adults_beyond_capacity.count > 0) {

      return true;
    }

    // Fallback: check if any meal plan has extra adult pricing defined (even if 0)
    const mealPlans = roomType.priceDetails?.meal_plans || roomType.price?.meal_plans;
    if (mealPlans) {
      const hasExtraAdults = Object.values(mealPlans).some((plan: any) =>
        plan && typeof plan === 'object' &&
        plan.hasOwnProperty('extra_adults_beyond_capacity_amount')
      );
        return hasExtraAdults;
    }
    return false;
  };

  // Get extra adult pricing display text
  const getExtraAdultPricingText = () => {
    if (!hasExtraAdultsAvailable()) return null;

    // First try the new API structure
    if (roomType.extra_adults_beyond_capacity) {
      const { count, per_night_amount, currency_code } = roomType.extra_adults_beyond_capacity;
      const currencySymbol = getCurrencySymbol(currency_code);
      if (per_night_amount > 0) {
        return `+${count} extra adult${count > 1 ? 's' : ''} added (${currencySymbol}${per_night_amount}/night each)`;
      } else {
        return `+${count} extra adult${count > 1 ? 's' : ''} available (free)`;
      }
    }

    // Fallback: use meal plan pricing
    const mealPlans = roomType.priceDetails?.meal_plans || roomType.price?.meal_plans;
    if (mealPlans) {
      const extraAdultPrices = Object.values(mealPlans)
        .map((plan: any) => plan?.extra_adults_beyond_capacity_amount)
        .filter((price: any) => typeof price === 'number');

      if (extraAdultPrices.length > 0) {
          return ``;
      }
    }

    return `Extra adults available`;
  };

  // Get available meal plans for this room type from API data
  const getAvailableMealPlans = () => {
    try {
      let availablePlans: string[] = [];

      // First check priceDetails structure (old API format)
      if (roomType.priceDetails?.meal_plans) {
        const mealPlans = roomType.priceDetails.meal_plans;

        // Filter to only include meal plans that have pricing data
        const plansFromPriceDetails = Object.keys(mealPlans).filter((plan) => {
          const planData = mealPlans[plan as any];
          return (
            planData &&
            typeof planData === "object" &&
            planData.per_night_amount > 0
          );
        });

        availablePlans = [...plansFromPriceDetails];
      }

      // Then check price structure (new API format)
      if (roomType.price?.meal_plans && availablePlans.length === 0) {
        const mealPlans = roomType.price.meal_plans;

        // Filter to only include meal plans that have pricing data
        const plansFromPrice = Object.keys(mealPlans).filter((plan) => {
          const planData = mealPlans[plan as keyof typeof mealPlans];
          return (
            planData &&
            typeof planData === "object" &&
            planData.per_night_amount > 0
          );
        });

        availablePlans = [...plansFromPrice];
      }

      // If no meal plans are available from the API, provide default options
      if (availablePlans.length === 0) {
        // Check if we at least have a base price for the room
        if (
          (roomType.priceDetails &&
            roomType.priceDetails.per_night_amount > 0) ||
          (roomType.price &&
            ((typeof roomType.price === "object" &&
              roomType.price.per_night_amount > 0) ||
              (typeof roomType.price === "number" && roomType.price > 0)))
        ) {
          return ["none"]; // At minimum, offer room-only option
        }
        return []; // No valid pricing available
      }

      return availablePlans;
    } catch (error) {
      console.error("Error getting available meal plans:", error);
      // Fallback to basic options if there's an error
      return (roomType.priceDetails &&
        roomType.priceDetails.per_night_amount > 0) ||
        (roomType.price &&
          ((typeof roomType.price === "object" &&
            roomType.price.per_night_amount > 0) ||
            (typeof roomType.price === "number" && roomType.price > 0)))
        ? ["none"]
        : [];
    }
  };

  // Get meal plan label from API response or fallback to default
  const getMealPlanLabel = (mealPlanKey: string): string => {
    try {
      // First check price structure (new API format)
      if (roomType.price?.meal_plans) {
        const mealPlan = roomType.price.meal_plans[mealPlanKey as keyof typeof roomType.price.meal_plans];
        if (mealPlan && typeof mealPlan === "object" && mealPlan.label) {
          return mealPlan.label;
        }
      }

      // Then check priceDetails structure (old API format)
      if (roomType.priceDetails?.meal_plans) {
        const mealPlan = roomType.priceDetails.meal_plans[mealPlanKey as any];
        if (mealPlan && typeof mealPlan === "object" && mealPlan.label) {
          return mealPlan.label;
        }
      }

      // Fallback to hardcoded labels
      const fallbackLabels: Record<string, string> = {
        none: "No Meals",
        bb: "Bed & Breakfast",
        hb: "Half Board",
        fb: "Full Board",
      };

      return fallbackLabels[mealPlanKey] || `Meal Plan ${mealPlanKey}`;
    } catch (error) {
      console.error("Error getting meal plan label:", error);
      return `Meal Plan ${mealPlanKey}`;
    }
  };

  // Get price for a specific meal plan from API data
  const getMealPlanPrice = (mealPlanKey: string) => {
    try {
      // First check priceDetails structure (old API format)
      if (
        roomType.priceDetails?.meal_plans &&
        roomType.priceDetails.meal_plans[mealPlanKey as any]
      ) {
        const mealPlanPrice =
          roomType.priceDetails.meal_plans[mealPlanKey as any];
        if (
          mealPlanPrice &&
          typeof mealPlanPrice === "object" &&
          mealPlanPrice.per_night_amount
        ) {
          return mealPlanPrice.per_night_amount;
        }
      }

      // Then check price structure (new API format)
      if (
        roomType.price?.meal_plans &&
        roomType.price.meal_plans[
          mealPlanKey as keyof typeof roomType.price.meal_plans
        ]
      ) {
        const mealPlanPrice =
          roomType.price.meal_plans[
            mealPlanKey as keyof typeof roomType.price.meal_plans
          ];
        if (
          mealPlanPrice &&
          typeof mealPlanPrice === "object" &&
          mealPlanPrice.per_night_amount
        ) {
          return mealPlanPrice.per_night_amount;
        }
      }

      // If no meal plan price is available, use the room's base price
      return (
        roomType.priceDetails?.per_night_amount ||
        roomType.price?.per_night_amount ||
        roomType.price ||
        0
      );
    } catch (error) {
      console.error("Error getting meal plan price:", error);
      return (
        roomType.priceDetails?.per_night_amount ||
        roomType.price?.per_night_amount ||
        roomType.price ||
        0
      );
    }
  };

  // Get original price from API data
  const getOriginalPrice = (mealPlanKey: string) => {
    try {
      const price = getMealPlanPrice(mealPlanKey);
      return Math.round(price * 1.2); // 20% markup as fallback
    } catch (error) {
      console.error("Error getting original price:", error);
      const price = getMealPlanPrice(mealPlanKey);
      return Math.round(price * 1.2);
    }
  };

  // Calculate taxes and fees based on tax_amount or amount_with_tax
  const getTaxesAndFees = (mealPlanKey: string) => {
    try {
      const price = getMealPlanPrice(mealPlanKey);

      // First check priceDetails structure (old API format)
      if (
        roomType.priceDetails?.meal_plans &&
        roomType.priceDetails.meal_plans[mealPlanKey as any]
      ) {
        const mealPlanPrice =
          roomType.priceDetails.meal_plans[mealPlanKey as any];
        if (mealPlanPrice && typeof mealPlanPrice === "object") {
          // If tax_amount is available, use it directly
          if (mealPlanPrice.tax_amount !== undefined) {
            return mealPlanPrice.tax_amount;
          }

          // If amount_with_tax is available, calculate the difference
          if (
            mealPlanPrice.amount_with_tax !== undefined &&
            mealPlanPrice.amount_without_tax !== undefined
          ) {
            return (
              mealPlanPrice.amount_with_tax - mealPlanPrice.amount_without_tax
            );
          }

          // Calculate taxes as the difference between total and per night amount
          if (mealPlanPrice.total_amount && mealPlanPrice.per_night_amount) {
            const taxDifference =
              mealPlanPrice.total_amount -
              mealPlanPrice.per_night_amount * (mealPlanPrice.nights || 1);
            return Math.round(taxDifference / (mealPlanPrice.nights || 1)); // Per night tax
          }
        }
      }

      // Then check price structure (new API format)
      if (
        roomType.price?.meal_plans &&
        roomType.price.meal_plans[
          mealPlanKey as keyof typeof roomType.price.meal_plans
        ]
      ) {
        const mealPlanPrice =
          roomType.price.meal_plans[
            mealPlanKey as keyof typeof roomType.price.meal_plans
          ];
        if (mealPlanPrice && typeof mealPlanPrice === "object") {
          // If tax_amount is available, use it directly
          if (mealPlanPrice.tax_amount !== undefined) {
            return mealPlanPrice.tax_amount;
          }

          // If amount_with_tax is available, calculate the difference
          if (
            mealPlanPrice.amount_with_tax !== undefined &&
            mealPlanPrice.amount_without_tax !== undefined
          ) {
            return (
              mealPlanPrice.amount_with_tax - mealPlanPrice.amount_without_tax
            );
          }

          // Calculate taxes as the difference between total and per night amount
          if (mealPlanPrice.total_amount && mealPlanPrice.per_night_amount) {
            const taxDifference =
              mealPlanPrice.total_amount -
              mealPlanPrice.per_night_amount * (mealPlanPrice.nights || 1);
            return Math.round(taxDifference / (mealPlanPrice.nights || 1)); // Per night tax
          }
        }
      }

      // Fallback: calculate taxes as a percentage of the price
      return Math.round(price * 0.18); // 18% tax as fallback
    } catch (error) {
      console.error("Error calculating taxes and fees:", error);
      const price = getMealPlanPrice(mealPlanKey);
      return Math.round(price * 0.18);
    }
  };

  // Handle meal plan selection and redirect to booking summary
  const handleMealPlanSelect = (mealPlanKey: MealPlanType) => {
    // First, add the room to the cart
    onAddToCart(roomType, mealPlanKey);

    // Calculate total amount based on meal plan
    const totalAmount = getMealPlanPrice(mealPlanKey);

    // Calculate nights
    const calculateNights = () => {
      if (checkInDate && checkOutDate) {
        const checkIn = new Date(checkInDate);
        const checkOut = new Date(checkOutDate);
        const diffTime = checkOut.getTime() - checkIn.getTime();
        const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
        return diffDays > 0 ? diffDays : 1;
      }
      return 1;
    };

    // Get hotel image URL
    const getHotelImageUrl = () => {
      if (hotelData?.images && hotelData.images.length > 0) {
        const firstImage = hotelData.images[0];
        if (typeof firstImage === "string") {
          return firstImage;
        } else if (
          firstImage &&
          typeof firstImage === "object" &&
          firstImage.url
        ) {
          return firstImage.url;
        }
      }
      return "/images/hotel-placeholder.jpg";
    };

    // Create booking data object to store in localStorage
    const bookingData = {
      hotelId: roomType.hotelId || hotelData?.id?.toString() || "",
      roomId: roomType.id?.toString() || "",
      checkIn: checkInDate || "",
      checkOut: checkOutDate || "",
      checkInTime: roomType.checkInTime || "14:00",
      checkOutTime: roomType.checkOutTime || "11:00",
      totalAmount: totalAmount,
      basePrice: getMealPlanPrice(mealPlanKey),
      originalPrice: getOriginalPrice(mealPlanKey),
      taxesAndFees: getTaxesAndFees(mealPlanKey),
      currencyCode: getCurrencyCode(),
      guestCount: guests?.adults || roomType.maxAdults || 1,
      childrenCount: guests?.children || 0,
      infantCount: guests?.infants || 0,
      mealPlan: mealPlanKey,
      roomQuantity: 1,
      regionId: roomType.regionId || "",
      nights: calculateNights(),
      // Hotel details
      hotel: {
        id: hotelData?.id || roomType.hotelId || "",
        name: hotelData?.name || roomType.hotelName || "",
        location: hotelData?.location || "",
        imageUrl: getHotelImageUrl(),
        rating: hotelData?.stars || hotelData?.rating || 4,
        tags: hotelData?.tags || [],
        rules: hotelData?.rules || [],
        cancellation_policies: hotelData?.cancellation_policies || [],
      },
      // Room details
      room: {
        id: roomType.id,
        name: getRoomName(),
        images: getRoomImages(),
        thumbnail: roomImages[0],
        description: roomType.description || "",
        amenities: formatAmenities(),
        size: roomType.room_size || roomType.size || "",
        bedType: roomType.bed_type || roomType.bedType || "",
        maxAdults: roomType.maxAdults || 2,
        maxChildren: roomType.maxChildren || 0,
        maxInfants: roomType.maxInfants || 0,
        availableMealPlans: getAvailableMealPlans(),
        mealPlanPrices:
          roomType.price?.meal_plans || roomType.priceDetails?.meal_plans || {},
        extra_adults_beyond_capacity: roomType.extra_adults_beyond_capacity,
      },
    };

    // Store booking data in localStorage
    try {
      localStorage.setItem("bookingData", JSON.stringify(bookingData));
    } catch (error) {
      console.error("Error storing booking data in localStorage:", error);
    }

    // Redirect to the review booking page
    window.location.href = "/review-booking";
  };

  const roomImages = getRoomImages();
  const amenities = formatAmenities();
  const availableMealPlans = getAvailableMealPlans();

  return (
    <>
      <div
        ref={cardRef}
        className={`mmt-room-card mmt-clearfix ${
          scrollPhase === ScrollPhase.PHASE_1
            ? "phase-1"
            : scrollPhase === ScrollPhase.PHASE_2
            ? "phase-2"
            : "phase-3"
        }`}
        data-scroll-phase={scrollPhase}
      >
        {/* Phase detection elements */}
        <div
          ref={phaseDetectorStartRef}
          className="mmt-phase-detector mmt-phase-detector-start"
        ></div>
        <div
          ref={phaseDetectorMiddleRef}
          className="mmt-phase-detector mmt-phase-detector-middle"
        ></div>
        <div
          ref={phaseDetectorEndRef}
          className="mmt-phase-detector mmt-phase-detector-end"
        ></div>

        <div className="mmt-three-col-layout">
          {/* First column - Room category (33.33% width) */}
          <div className="mmt-room-category">
            {/* Room image */}
            <div className="overflow-hidden rounded-lg aspect-[4/3] shadow-sm mb-4">
              <img
                src={roomImages[0]}
                alt={roomType.name}
                className="w-full h-full object-cover"
              />
            </div>

            {/* Photo count badge */}
            <div
              className="mmt-photos-badge mb-4"
              role="button"
              onClick={() => setIsRoomDetailsModalOpen(true)}
            >
              {roomImages.length} PHOTOS
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="10"
                height="10"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </div>

            {/* Room name */}
            <h3 className="mmt-room-name">{getRoomName()}</h3>

            {/* Room specifications */}
            <div className="mmt-room-specs">
              <span>
                {roomType.room_size || roomType.size
                  ? `${roomType.room_size || roomType.size} sq.ft`
                  : ""}
              </span>
              {(roomType.room_size || roomType.size) &&
                (roomType.bed_type || roomType.bedType) && (
                  <span className="mmt-room-specs-divider">|</span>
                )}
              <span>
                {" "}
                {(roomType.bed_type || roomType.bedType || "")
                  .split(" ")
                  .map(
                    (word: string) =>
                      word.charAt(0).toUpperCase() + word.slice(1)
                  )
                  .join(" ")}
              </span>
            </div>

            <div className="mmt-room-specs">
              <span>
                {roomType.maxGuests ||
                  (roomType.maxAdults || 1) +
                    (roomType.maxChildren || 0) +
                    (roomType.maxInfants || 0)}{" "}
                guests
              </span>
            </div>

            {/* Extra adults availability */}
            {hasExtraAdultsAvailable() && (
              <div className="mmt-room-specs mt-2">
                <span className="text-sm text-green-600 font-medium">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="14"
                    height="14"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="inline mr-1 mb-1"
                  >
                    <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                    <circle cx="9" cy="7" r="4"></circle>
                    <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                    <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                  </svg>
                  <span>{getExtraAdultPricingText()}</span>
                </span>
              </div>
            )}

            {/* Room description */}
            <div className="text-sm text-gray-600 my-4">
              {roomType.description || ""}
            </div>

            {/* Room amenities */}
            {amenities.length > 0 && (
              <>
                <h4 className="text-sm font-bold mb-3 text-gray-700">
                  Room Amenities
                </h4>
                <div className="grid grid-cols-2 gap-2 mb-4">
                  {amenities.slice(0, 4).map((amenity, index) => (
                    <div key={index} className="mmt-amenity-feature">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="20 6 9 17 4 12"></polyline>
                      </svg>
                      {amenity}
                    </div>
                  ))}
                </div>
              </>
            )}

            {/* More details button */}
            <button
              onClick={() => setIsRoomDetailsModalOpen(true)}
              className="mmt-more-details mt-4 block"
            >
              View Room Details
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="12"
                height="12"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <polyline points="9 18 15 12 9 6"></polyline>
              </svg>
            </button>
          </div>

          {/* Columns 2 & 3 container (66.67% width) */}
          <div
            ref={mealPlanSectionRef}
            className="mmt-details-pricing-container"
          >
            {availableMealPlans.length > 0 ? (
              // Map through available meal plans from the API
              availableMealPlans.map((mealPlanKey, index) => (
                <div key={mealPlanKey} className="mmt-meal-plan-row">
                  {/* Column 2: Room details for this meal plan (33.33% width) */}
                  <div className="mmt-meal-plan-details">
                    <h5 className="mmt-meal-plan-title">
                      {getMealPlanLabel(mealPlanKey)}
                    </h5>
                    <div className="mmt-meal-plan-features">
                      {/* Use the features from API or default features */}
                      {getMealPlanFeatures(mealPlanKey).map(
                        (feature: string, featureIndex: number) => (
                          <div
                            key={featureIndex}
                            className="mmt-meal-plan-feature"
                          >
                            <svg
                              xmlns="http://www.w3.org/2000/svg"
                              width="14"
                              height="14"
                              viewBox="0 0 24 24"
                              fill="none"
                              stroke="currentColor"
                              strokeWidth="2"
                              strokeLinecap="round"
                              strokeLinejoin="round"
                            >
                              {mealPlanKey === "bb" ? (
                                <path d="M12 2L2 7l10 5 10-5-10-5z"></path>
                              ) : mealPlanKey === "fb" ? (
                                <path d="M22 12h-4l-3 9L9 3l-3 9H2"></path>
                              ) : (
                                <circle cx="12" cy="12" r="10"></circle>
                              )}
                            </svg>
                            {feature}
                          </div>
                        )
                      )}
                    </div>
                  </div>

                  {/* Column 3: Pricing for this meal plan (33.33% width) */}
                  <div className="mmt-meal-plan-pricing">
                    {/* Super Package badge - only for first option */}
                    {/* {index === 0 && (
                      <div className="mmt-super-package">Super Package</div>
                    )} */}

                    {/* Price display */}
                    <div className="mmt-price-display">
                      <div className="mmt-original-price">
                        {getCurrencySymbol(getCurrencyCode())}{" "}
                        {Math.round(getOriginalPrice(mealPlanKey))}
                      </div>
                      <div className="mmt-discounted-price">
                        {getCurrencySymbol(getCurrencyCode())}{" "}
                        {Math.round(getMealPlanPrice(mealPlanKey))}
                      </div>
                      {/* Only show taxes if they exist */}
                      {getTaxesAndFees(mealPlanKey) > 0 && (
                        <div className="mmt-taxes-fees">
                          +{getCurrencySymbol(getCurrencyCode())}{" "}
                          {Math.round(getTaxesAndFees(mealPlanKey))} Taxes &
                          Fees per night
                        </div>
                      )}
                    </div>

                    {/* Select button */}
                    <button
                      onClick={() =>
                        handleMealPlanSelect(mealPlanKey as MealPlanType)
                      }
                      className="mmt-select-button"
                    >
                      SELECT ROOM
                    </button>

                    {/* Credit card offer - only for first option */}
                    {/* {index === 0 && (
                      <div className="mmt-credit-card-offer">
                        <div className="mmt-credit-card-offer-text">
                          Credit Card Offer - Get {getCurrencySymbol(hotelCurrency)}{" "}
                          {Math.round(getMealPlanPrice(mealPlanKey) * 0.1)} Off
                        </div>
                        <a href="#" className="mmt-avail-link">
                          SELECT TO AVAIL
                        </a>
                      </div>
                    )} */}
                  </div>
                </div>
              ))
            ) : (
              // No meal plans available
              <div className="mmt-meal-plan-row">
                <div className="mmt-meal-plan-details">
                  <h5 className="mmt-meal-plan-title">Room Only</h5>
                  <div className="mmt-meal-plan-features">
                    {getMealPlanFeatures("none").map(
                      (feature: string, featureIndex: number) => (
                        <div
                          key={featureIndex}
                          className="mmt-meal-plan-feature"
                        >
                          <svg
                            xmlns="http://www.w3.org/2000/svg"
                            width="14"
                            height="14"
                            viewBox="0 0 24 24"
                            fill="none"
                            stroke="currentColor"
                            strokeWidth="2"
                            strokeLinecap="round"
                            strokeLinejoin="round"
                          >
                            <circle cx="12" cy="12" r="10"></circle>
                          </svg>
                          {feature}
                        </div>
                      )
                    )}
                  </div>
                </div>
                <div className="mmt-meal-plan-pricing">
                  <div className="mmt-price-display">
                    <div className="mmt-original-price">
                      {getCurrencySymbol(getCurrencyCode())}{" "}
                      {Math.round(getOriginalPrice("none"))}
                    </div>
                    <div className="mmt-discounted-price">
                      {getCurrencySymbol(getCurrencyCode())}{" "}
                      {Math.round(getMealPlanPrice("none"))}
                    </div>
                    {/* Only show taxes if they exist */}
                    {getTaxesAndFees("none") > 0 && (
                      <div className="mmt-taxes-fees">
                        +{getCurrencySymbol(getCurrencyCode())}{" "}
                        {Math.round(getTaxesAndFees("none"))} Taxes & Fees per
                        night
                      </div>
                    )}
                  </div>

                  <button
                    onClick={() => handleMealPlanSelect("none" as MealPlanType)}
                    className="mmt-select-button"
                  >
                    SELECT ROOM
                  </button>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>

      {/* Room Details Modal */}
      {isRoomDetailsModalOpen && (
        <RoomDetailsModal
          room={roomType}
          hotelCurrency={hotelCurrency}
          isOpen={isRoomDetailsModalOpen}
          onClose={() => setIsRoomDetailsModalOpen(false)}
          onSelectRoom={(_room, mealPlanKey) => {
            if (mealPlanKey) {
              handleMealPlanSelect(mealPlanKey);
            } else {
              // Default to 'none' meal plan if not specified
              handleMealPlanSelect("none" as MealPlanType);
            }
            setIsRoomDetailsModalOpen(false);
          }}
        />
      )}
    </>
  );
};

export default MakeMyTripRoomCard;
