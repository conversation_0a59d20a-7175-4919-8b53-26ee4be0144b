import React, { useEffect } from "react";

interface ReactStickyHeaderProps {
  name: string;
  location: string;
  rating: number;
  onSectionClick?: (sectionId: string) => void;
  hotelId?: string;
}

const ReactStickyHeader: React.FC<ReactStickyHeaderProps> = ({
  name,
  location,
  rating,
  onSectionClick,
  hotelId,
}) => {
  // State is managed through DOM manipulation for consistency with other components

  // Check if hotel is in wishlist and set up event handlers
  useEffect(() => {
    // Get hotel data from the URL or use the prop
    const currentHotelId = hotelId || window.location.pathname.split("/").pop();

    // Check if hotel is already in wishlist
    const updateWishlistButtonState = () => {
      const wishlistButton = document.getElementById("sticky-add-to-wishlist");
      const wishlistIcon = wishlistButton?.querySelector(
        ".sticky-wishlist-icon"
      );
      const wishlistText = wishlistButton?.querySelector(
        ".sticky-wishlist-text"
      );

      if (!wishlistButton || !wishlistIcon || !wishlistText) return;

      try {
        const wishlistData = localStorage.getItem("wishlist");
        if (wishlistData) {
          const wishlist = JSON.parse(wishlistData);
          const isInWishlist = wishlist.some(
            (item: { id: string }) => item.id === currentHotelId
          );

          if (isInWishlist) {
            wishlistIcon.setAttribute("fill", "currentColor");
            wishlistText.textContent = "Saved";
          } else {
            wishlistIcon.setAttribute("fill", "none");
            wishlistText.textContent = "Save";
          }
        }
      } catch (error) {
        console.error("Error checking wishlist:", error);
      }
    };

    // Toggle wishlist item
    const toggleWishlist = () => {
      try {
        let wishlist = [];
        const wishlistData = localStorage.getItem("wishlist");

        if (wishlistData) {
          wishlist = JSON.parse(wishlistData);
        }

        const isInWishlist = wishlist.some(
          (item: { id: string }) => item.id === currentHotelId
        );

        if (isInWishlist) {
          // Remove from wishlist
          wishlist = wishlist.filter(
            (item: { id: string }) => item.id !== currentHotelId
          );
        } else {
          // Add to wishlist
          wishlist.push({
            id: currentHotelId,
            name: name,
            location: location,
            image:
              document.querySelector("#gallery img")?.getAttribute("src") || "",
            addedAt: new Date().toISOString(),
          });
        }

        localStorage.setItem("wishlist", JSON.stringify(wishlist));
        updateWishlistButtonState();

        // Dispatch custom event to notify other components about wishlist changes
        window.dispatchEvent(new CustomEvent("wishlistUpdated"));
      } catch (error) {
        console.error("Error updating wishlist:", error);
      }
    };

    // Add click event listener for wishlist button
    const wishlistButton = document.getElementById("sticky-add-to-wishlist");
    wishlistButton?.addEventListener("click", toggleWishlist);

    // Add click event listener for share button
    const shareButton = document.getElementById("sticky-share-button");
    shareButton?.addEventListener("click", () => {
      // Check if the openShareModal function exists (from ShareModal component)
      if (typeof (window as any).openShareModal === "function") {
        (window as any).openShareModal();
      }
    });

    // Initialize wishlist button state
    updateWishlistButtonState();

    // Listen for wishlist updates from other components
    window.addEventListener("wishlistUpdated", updateWishlistButtonState);

    // Cleanup event listeners
    return () => {
      wishlistButton?.removeEventListener("click", toggleWishlist);
      shareButton?.removeEventListener("click", () => {});
      window.removeEventListener("wishlistUpdated", updateWishlistButtonState);
    };
  }, [name, location, hotelId]);

  // Add smooth scrolling behavior for anchor links
  useEffect(() => {
    const handleAnchorClick = (e: MouseEvent) => {
      const target = e.target as HTMLElement;
      if (
        target.tagName === "A" &&
        target.getAttribute("href")?.startsWith("#")
      ) {
        e.preventDefault();
        const targetId = target.getAttribute("href");
        if (!targetId) return;

        const targetElement = document.querySelector(targetId);
        if (!targetElement) return;

        // Calculate offset based on target
        let offset = 80; // Default offset

        // Special handling for different sections
        if (targetId === "#booking") {
          offset = 100; // Extra offset for booking section
        } else if (targetId === "#rooms") {
          offset = 70; // Less offset for rooms section
        }

        window.scrollTo({
          top:
            targetElement.getBoundingClientRect().top + window.scrollY - offset,
          behavior: "smooth",
        });
      }
    };

    // Add event listeners to all anchor links in the sticky header
    const anchorLinks = document.querySelectorAll(
      '.sticky-header-container a[href^="#"]'
    );
    anchorLinks.forEach((link) => {
      link.addEventListener("click", handleAnchorClick as EventListener);
    });

    // Cleanup
    return () => {
      anchorLinks.forEach((link) => {
        link.removeEventListener("click", handleAnchorClick as EventListener);
      });
    };
  }, []);

  return (
    <div className="sticky-header-container bg-white shadow-md py-3 border-b">
      <div className="container-custom">
        <div className="flex items-center justify-between">
          <div className="flex items-center space-x-4">
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#rooms");
                } else {
                  const roomsSection = document.getElementById("rooms");
                  if (roomsSection) {
                    roomsSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -70);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              Rooms
            </button>
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#about");
                } else {
                  const aboutSection = document.getElementById("about");
                  if (aboutSection) {
                    aboutSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -80);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              About
            </button>
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#amenities");
                } else {
                  const amenitiesSection = document.getElementById("amenities");
                  if (amenitiesSection) {
                    amenitiesSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -80);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              Amenities
            </button>
            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#location");
                } else {
                  const locationSection = document.getElementById("location");
                  if (locationSection) {
                    locationSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -80);
                    }, 10);
                  }
                }
              }}
              className="text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors hover:border-b-2 hover:border-[#285DA6] pb-1 cursor-pointer bg-transparent border-0"
            >
              Location
            </button>
          </div>
          <div className="flex items-center space-x-4">
            {/* Save Button */}
            <button
              id="sticky-add-to-wishlist"
              className="hidden md:flex items-center text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors cursor-pointer bg-transparent border-0"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1 sticky-wishlist-icon"
              >
                <path d="M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z"></path>
              </svg>
              <span className="sticky-wishlist-text">Save</span>
            </button>

            {/* Share Button */}
            <button
              id="sticky-share-button"
              className="hidden md:flex items-center text-sm font-karla uppercase tracking-wider text-[#285DA6] hover:text-[#285DA6]/80 transition-colors cursor-pointer bg-transparent border-0"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-1"
              >
                <path d="M7 11v13l7-7 7 7V11"></path>
                <rect x="3" y="3" width="18" height="8" rx="1" ry="1"></rect>
              </svg>
              Share
            </button>

            <button
              onClick={() => {
                if (onSectionClick) {
                  onSectionClick("#room-selection");
                } else {
                  const bookingSection =
                    document.getElementById("room-selection");
                  if (bookingSection) {
                    bookingSection.scrollIntoView({
                      behavior: "smooth",
                      block: "start",
                    });
                    // Add offset
                    setTimeout(() => {
                      window.scrollBy(0, -100);
                    }, 10);
                  }
                }
              }}
              className="hidden md:flex items-center px-4 py-2 bg-[#285DA6] text-white rounded-md text-sm font-karla uppercase tracking-wider hover:bg-[#285DA6]/90 transition-colors shadow-sm cursor-pointer border-0"
            >
              Book Now
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ReactStickyHeader;
