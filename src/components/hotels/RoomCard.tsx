import React, { useState } from "react";
import RoomDetails from "./RoomDetails";
import { type RoomType } from "../../utils/types";

interface RoomCardProps {
  room: RoomType;
  hotelCurrency: string;
  isSelected: boolean;
  onSelectRoom: (room: RoomType) => void;
  hotelCheckIn?: string;
  hotelCheckOut?: string;
}

const RoomCard: React.FC<RoomCardProps> = ({
  room,
  hotelCurrency,
  isSelected,
  onSelectRoom,
  hotelCheckIn,
  hotelCheckOut,
}) => {
  const [detailsExpanded, setDetailsExpanded] = useState(false);

  const toggleDetails = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    setDetailsExpanded(!detailsExpanded);
  };

  const handleSelectRoom = () => {
    // Call the original onSelectRoom handler to maintain existing functionality
    onSelectRoom(room);

    // Check if we have all the necessary data to redirect to review-booking
    if (room && room.id && hotelCheckIn && hotelCheckOut) {
      // Create URL for review booking page with all necessary parameters
      const summaryUrl = new URL("/review-booking", window.location.origin);

      // Extract hotel ID from room if available, or use a fallback
      const hotelId =
        room.hotelId ||
        (typeof room.id === "string" ? room.id.split("_")[0] : "");

      // Add all required parameters
      summaryUrl.searchParams.append("hotelId", hotelId);
      summaryUrl.searchParams.append("roomId", room.id.toString());
      summaryUrl.searchParams.append("checkIn", hotelCheckIn);
      summaryUrl.searchParams.append("checkOut", hotelCheckOut);
      summaryUrl.searchParams.append(
        "checkInTime",
        room.checkInTime || "14:00"
      );
      summaryUrl.searchParams.append(
        "checkOutTime",
        room.checkOutTime || "11:00"
      );

      // Calculate total amount based on room price
      const price = room.price || 0;
      summaryUrl.searchParams.append("totalAmount", price.toString());

      summaryUrl.searchParams.append("currencyCode", getCurrencyCode());
      summaryUrl.searchParams.append(
        "guestCount",
        (room.maxAdults || 2).toString()
      );
      summaryUrl.searchParams.append(
        "infantCount",
        (room.maxInfants || 0).toString()
      );
      summaryUrl.searchParams.append("mealPlan", "none"); // Default to no meal plan
      summaryUrl.searchParams.append("roomQuantity", "1");

      // Add region ID if available
      if (room.regionId) {
        summaryUrl.searchParams.append("regionId", room.regionId);
      }

      // Redirect to the booking summary page
      window.location.href = summaryUrl.toString();
    } else {
      // If we don't have all the necessary data, just scroll to the booking section
      document
        .getElementById("booking")
        ?.scrollIntoView({ behavior: "smooth" });
    }
  };

  // Get currency code from room price details or fallback to hotel currency
  const getCurrencyCode = () => {
    // First try to get currency from room price details (API response)
    if (room.priceDetails?.currency_code) {
      return room.priceDetails.currency_code;
    }
    if (room.price?.currency_code) {
      return room.price.currency_code;
    }
    // Fallback to hotel currency, but convert symbol to code if needed
    if (hotelCurrency === "$") return "USD";
    if (hotelCurrency === "€") return "EUR";
    if (hotelCurrency === "£") return "GBP";
    return hotelCurrency || "USD";
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    const currencyCode = getCurrencyCode();
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get room thumbnail image
  const getRoomThumbnail = () => {
    if (room.thumbnail) return room.thumbnail;
    if (Array.isArray(room.images) && room.images.length > 0) {
      return typeof room.images[0] === "string"
        ? room.images[0]
        : room.images[0] && room.images[0].url
        ? room.images[0].url
        : "";
    }
    return "";
  };

  // Format amenities for display
  const getAmenityName = (amenity: any) => {
    return typeof amenity === "string"
      ? amenity
      : amenity && amenity.name
      ? amenity.name
      : "Amenity";
  };

  const calcMaxGuests = () => {
    return (
      room.maxGuests ?? room.maxChildren + room.maxAdults + room.maxInfants
    );
  };

  return (
    <div className="space-y-4">
      <div
        className={`bg-white border border-[#3566ab]/10 rounded-xl p-6 shadow-md hover:shadow-lg transition-all duration-300 room-card group ${
          detailsExpanded ? "border-b-0 rounded-b-none" : ""
        } ${
          isSelected
            ? "ring-2 ring-[#3566ab] bg-[#3566ab]/[0.02]"
            : "hover:border-[#3566ab]/30"
        }`}
      >
        {/* Room Summary Bar - Only visible when selected */}
        {isSelected && (
          <div className="mb-6 p-3 bg-[#3566ab]/10 rounded-lg flex flex-col md:flex-row justify-between items-start md:items-center">
            <div className="flex items-center">
              <div className="bg-[#3566ab] rounded-full p-1.5 mr-3">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-white"
                >
                  <polyline points="20 6 9 17 4 12"></polyline>
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-[#3566ab]">
                  Selected Room
                </p>
                <p className="text-xs text-foreground/70">
                  {hotelCheckIn && hotelCheckOut
                    ? `${hotelCheckIn} - ${hotelCheckOut}`
                    : "Dates not selected"}
                </p>
              </div>
            </div>
            <button
              className="mt-3 md:mt-0 px-4 py-1.5 bg-[#3566ab] text-white text-sm rounded-lg hover:bg-[#3566ab]/90 transition-colors shadow-sm"
              onClick={handleSelectRoom}
            >
              Book Now
            </button>
          </div>
        )}

        <div className="grid grid-cols-1 md:grid-cols-12 gap-6">
          {/* Room Image */}
          <div className="md:col-span-3">
            <div className="overflow-hidden rounded-xl aspect-[4/3] shadow-md relative">
              <img
                src={getRoomThumbnail()}
                alt={room.name}
                className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-700 ease-out"
              />
              {isSelected && (
                <div className="absolute top-3 left-3 bg-[#3566ab] text-white text-xs font-karla uppercase tracking-wider px-3 py-1.5 rounded-full shadow-md">
                  Selected
                </div>
              )}
            </div>
          </div>

          {/* Room Info */}
          <div className="md:col-span-6">
            <div className="flex items-center mb-3">
              <h3 className="text-xl font-baskervville text-[#3566ab]">
                {room.name}
              </h3>
            </div>

            <div className="flex flex-wrap gap-3 mb-5">
              <div className="flex items-center text-sm bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab] mr-2"
                >
                  <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                  <circle cx="9" cy="7" r="4"></circle>
                </svg>
                <span className="text-foreground/80">
                  Up to {room.maxGuests ?? calcMaxGuests() ?? ""} guests
                </span>
              </div>

              <div className="flex items-center text-sm bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab] mr-2"
                >
                  <path d="M2 3h20v10H2z"></path>
                  <path d="M2 17h20v4H2z"></path>
                </svg>
                <span className="text-foreground/80">
                  {(room.bedType &&
                    room.bedType.charAt(0).toUpperCase() +
                      room.bedType.slice(1)) ||
                    "King-size"}
                </span>
              </div>

              <div className="flex items-center text-sm bg-[#3566ab]/5 px-3 py-1.5 rounded-full">
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="14"
                  height="14"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  className="text-[#3566ab] mr-2"
                >
                  <path d="M2 4v16"></path>
                  <path d="M22 4v16"></path>
                  <path d="M2 12h20"></path>
                  <path d="M12 2v20"></path>
                </svg>
                <span className="text-foreground/80">
                  {room.size || "65"} sq.m
                </span>
              </div>
            </div>

            <div className="flex flex-wrap gap-2">
              {Array.isArray(room.amenities) && room.amenities.length > 0 ? (
                room.amenities?.slice(0, 5).map((amenity, index) => (
                  <span
                    key={index}
                    className="inline-flex items-center px-3 py-1 rounded-full text-xs font-karla bg-[#3566ab]/5 text-foreground/80"
                  >
                    {getAmenityName(amenity)}
                  </span>
                ))
              ) : (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-karla bg-[#3566ab]/5 text-foreground/80">
                  No amenities listed
                </span>
              )}
              {Array.isArray(room.amenities) && room.amenities.length > 5 && (
                <span className="inline-flex items-center px-3 py-1 rounded-full text-xs font-karla bg-[#3566ab]/10 text-[#3566ab]">
                  +{room.amenities.length - 5} more
                </span>
              )}
            </div>
          </div>

          {/* Price and Select Button */}
          <div className="md:col-span-3 flex flex-col items-end justify-between">
            <div className="text-right mb-4">
              <div className="flex items-center justify-end mb-1">
                <span className="text-xs font-karla uppercase tracking-wider text-foreground/60 line-through mr-2">
                  {formatCurrency(room.price * 1.2)}
                </span>
                <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-karla rounded-full">
                  Save 20%
                </span>
              </div>

              <p className="text-2xl font-baskervville text-[#3566ab]">
                <span className="text-sm font-karla uppercase tracking-wider text-foreground/60">
                  From
                </span>{" "}
                {formatCurrency(room.price)}
              </p>
              <p className="text-xs font-karla uppercase tracking-wider text-foreground/60">
                per night
              </p>
            </div>

            <button
              className={`w-full py-3 px-4 text-sm font-karla uppercase tracking-wider rounded-lg transition-all duration-300 shadow-md ${
                isSelected
                  ? "bg-gradient-to-r from-[#3566ab] to-[#2a5089] text-white hover:shadow-lg transform hover:-translate-y-0.5"
                  : "bg-white border border-[#3566ab] text-[#3566ab] hover:bg-[#3566ab]/5"
              }`}
              onClick={handleSelectRoom}
            >
              {isSelected ? "SELECTED" : "SELECT ROOM"}
            </button>
          </div>
        </div>

        {/* Room Details Expansion Trigger */}
        <div className="mt-6 pt-4 border-t border-[#3566ab]/10 details-divider">
          <button
            className="w-full flex items-center justify-center text-sm font-karla uppercase tracking-wider text-[#3566ab] hover:text-[#3566ab]/80 transition-colors"
            onClick={toggleDetails}
          >
            <span>
              {detailsExpanded ? "HIDE ROOM DETAILS" : "VIEW ROOM DETAILS"}
            </span>
            {detailsExpanded ? (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2"
              >
                <polyline points="18 15 12 9 6 15"></polyline>
              </svg>
            ) : (
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="ml-2"
              >
                <polyline points="6 9 12 15 18 9"></polyline>
              </svg>
            )}
          </button>
        </div>
      </div>

      {/* Collapsible Room Details */}
      {detailsExpanded && <RoomDetails room={room} isSelected={isSelected} />}
    </div>
  );
};

export default RoomCard;
