import React, { useState } from "react";
import { type RoomType } from "../../utils/types";
import { type MealPlanType } from "../booking/MealPlanSelector";

interface RoomDetailsModalProps {
  room: RoomType;
  hotelCurrency: string;
  isOpen: boolean;
  onClose: () => void;
  onSelectRoom: (room: RoomType, mealPlanKey?: MealPlanType) => void;
}

const RoomDetailsModal: React.FC<RoomDetailsModalProps> = ({
  room,
  hotelCurrency,
  isOpen,
  onClose,
  onSelectRoom,
}) => {
  const [currentImageIndex, setCurrentImageIndex] = useState(0);

  // Get currency code from room price details or fallback to hotel currency
  const getCurrencyCode = () => {
    // First try to get currency from room price details (API response)
    if (room.priceDetails?.currency_code) {
      return room.priceDetails.currency_code;
    }
    if (room.price?.currency_code) {
      return room.price.currency_code;
    }
    // Fallback to hotel currency, but convert symbol to code if needed
    if (hotelCurrency === "$") return "USD";
    if (hotelCurrency === "€") return "EUR";
    if (hotelCurrency === "£") return "GBP";
    return hotelCurrency || "USD";
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    const currencyCode = getCurrencyCode();
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get room images
  const getRoomImages = () => {
    const images: string[] = [];

    if (room.thumbnail) {
      images.push(room.thumbnail);
    }

    if (Array.isArray(room.images) && room.images.length > 0) {
      room.images.forEach((image) => {
        if (typeof image === "string") {
          images.push(image);
        } else if (image && typeof image === "object" && image.url) {
          images.push(image.url);
        }
      });
    }

    // If no images found, use a placeholder
    if (images.length === 0) {
      images.push("/images/room-placeholder.jpg");
    }

    // Remove duplicates
    return [...new Set(images)];
  };

  // Format amenities for display
  const getAmenityName = (amenity: any) => {
    return typeof amenity === "string"
      ? amenity
      : amenity && amenity.name
      ? amenity.name
      : "Amenity";
  };

  // Navigate to previous image
  const prevImage = () => {
    const images = getRoomImages();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === 0 ? images.length - 1 : prevIndex - 1
    );
  };

  // Navigate to next image
  const nextImage = () => {
    const images = getRoomImages();
    setCurrentImageIndex((prevIndex) =>
      prevIndex === images.length - 1 ? 0 : prevIndex + 1
    );
  };

  // Handle click outside modal to close
  const handleOutsideClick = (e: React.MouseEvent<HTMLDivElement>) => {
    if (e.target === e.currentTarget) {
      onClose();
    }
  };

  const roomImages = getRoomImages();

  if (!isOpen) return null;

  return (
    <div
      className="fixed inset-0 bg-black/50 backdrop-blur-sm z-[1200] flex justify-center items-center overflow-y-auto modal-backdrop" style={{marginTop:0}}
      onClick={handleOutsideClick}
    >
      <div
        className="w-[95%] max-w-4xl max-h-[90vh] bg-white shadow-2xl rounded-xl overflow-y-auto animate-fade-in"
        style={{ boxShadow: "0 25px 50px -12px rgba(0, 0, 0, 0.25)" }}
      >
        <div className="sticky top-0 z-10 bg-white p-4 border-b flex justify-between items-center rounded-t-xl">
          <h3 className="text-xl font-baskervville text-[#3566ab]">
            {room.name}
          </h3>
          <button
            onClick={onClose}
            className="p-2 rounded-full hover:bg-gray-100 transition-colors"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="24"
              height="24"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>

        <div className="p-6 rounded-b-xl">
          {/* Image Gallery */}
          <div className="mb-8">
            <div className="relative rounded-xl overflow-hidden">
              {roomImages.length > 0 && (
                <div className="aspect-[16/9] relative">
                  <img
                    src={roomImages[currentImageIndex]}
                    alt={`${room.name} - Image ${currentImageIndex + 1}`}
                    className="w-full h-full object-cover"
                  />

                  {/* Image counter */}
                  <div className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
                    {currentImageIndex + 1} / {roomImages.length}
                  </div>
                </div>
              )}

              {/* Navigation buttons */}
              {roomImages.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-[#3566ab] rounded-full p-2.5 shadow-lg transition-all duration-300 border border-white/20"
                    aria-label="Previous image"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 18l-6-6 6-6"></path>
                    </svg>
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 -translate-y-1/2 bg-white/90 backdrop-blur-sm hover:bg-white text-[#3566ab] rounded-full p-2.5 shadow-lg transition-all duration-300 border border-white/20"
                    aria-label="Next image"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="20"
                      height="20"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M9 18l6-6-6-6"></path>
                    </svg>
                  </button>
                </>
              )}
            </div>

            {/* Thumbnail Gallery */}
            {roomImages.length > 1 && (
              <div className="flex mt-4 space-x-2 overflow-x-auto pb-2">
                {roomImages.map((image, index) => (
                  <div
                    key={index}
                    className={`flex-none w-20 h-20 rounded-md overflow-hidden cursor-pointer border-2 ${
                      currentImageIndex === index
                        ? "border-[#3566ab]"
                        : "border-transparent"
                    }`}
                    onClick={() => setCurrentImageIndex(index)}
                  >
                    <img
                      src={image}
                      alt={`Thumbnail ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                ))}
              </div>
            )}
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            {/* Left Column - Room Description and Amenities */}
            <div>
              {/* Room Description */}
              <div className="mb-6">
                <h4 className="text-lg font-baskervville mb-3 text-[#3566ab] flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M2 3h6a4 4 0 0 1 4 4v14a3 3 0 0 0-3-3H2z"></path>
                    <path d="M22 3h-6a4 4 0 0 0-4 4v14a3 3 0 0 1 3-3h7z"></path>
                  </svg>
                  Room Description
                </h4>
                <p className="text-sm text-foreground/80 leading-relaxed">
                  {room.description ||
                    "No description available for this room."}
                </p>
              </div>

              {/* Room Amenities */}
              <div>
                <h4 className="text-lg font-baskervville mb-3 text-[#3566ab] flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <path d="M3 3v18h18"></path>
                    <path d="M3 15L9 9l4 4 8-8"></path>
                  </svg>
                  Room Amenities
                </h4>
                <div className="flex flex-wrap gap-2">
                  {Array.isArray(room.amenities) &&
                  room.amenities.length > 0 ? (
                    room.amenities.map((amenity, index) => (
                      <span
                        key={index}
                        className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-karla bg-white text-foreground/80 shadow-sm border border-[#3566ab]/5"
                      >
                        {getAmenityName(amenity)}
                      </span>
                    ))
                  ) : (
                    <span className="inline-flex items-center px-3 py-1.5 rounded-full text-xs font-karla bg-white text-foreground/80 shadow-sm border border-[#3566ab]/5">
                      No amenities listed
                    </span>
                  )}
                </div>
              </div>
            </div>

            {/* Right Column - Room Details and Booking */}
            <div>
              {/* Room Specifications */}
              <div className="bg-gradient-to-br from-[#3566ab]/5 to-[#3566ab]/10 rounded-xl p-6 shadow-sm mb-6">
                <h4 className="text-lg font-baskervville mb-3 text-[#3566ab] flex items-center">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2"
                  >
                    <rect
                      x="3"
                      y="3"
                      width="18"
                      height="18"
                      rx="2"
                      ry="2"
                    ></rect>
                    <line x1="3" y1="9" x2="21" y2="9"></line>
                    <line x1="9" y1="21" x2="9" y2="9"></line>
                  </svg>
                  Room Specifications
                </h4>
                <ul className="space-y-3">
                  <li className="flex items-center text-sm">
                    <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab]"
                      >
                        <path d="M2 4v16"></path>
                        <path d="M22 4v16"></path>
                        <path d="M2 12h20"></path>
                        <path d="M12 2v20"></path>
                      </svg>
                    </div>
                    <span className="text-foreground/80">
                      Room Size:{" "}
                      <span className="font-medium text-foreground">
                        {room.size || "Not specified"}
                      </span>
                    </span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab]"
                      >
                        <path d="M2 3h20v10H2z"></path>
                        <path d="M2 17h20v4H2z"></path>
                      </svg>
                    </div>
                    <span className="text-foreground/80">
                      Bed Type:{" "}
                      <span className="font-medium text-foreground">
                        {(room.bedType &&
                          room.bedType.charAt(0).toUpperCase() +
                            room.bedType.slice(1)) ||
                          "Not specified"}
                      </span>
                    </span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab]"
                      >
                        <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                      </svg>
                    </div>
                    <span className="text-foreground/80">
                      Max Adults:{" "}
                      <span className="font-medium text-foreground">
                        {room.maxAdults || "2"}
                      </span>
                    </span>
                  </li>
                  <li className="flex items-center text-sm">
                    <div className="w-8 h-8 rounded-full bg-white flex items-center justify-center mr-3 shadow-sm">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                        className="text-[#3566ab]"
                      >
                        <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
                        <circle cx="9" cy="7" r="4"></circle>
                        <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
                        <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                      </svg>
                    </div>
                    <span className="text-foreground/80">
                      Max Children:{" "}
                      <span className="font-medium text-foreground">
                        {room.maxChildren || "0"}
                      </span>
                    </span>
                  </li>
                </ul>
              </div>

              {/* Booking Section */}
              <div className="bg-white border border-[#3566ab]/10 rounded-xl p-6 shadow-sm">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <p className="text-sm text-foreground/60 uppercase tracking-wider">
                      Price per night
                    </p>
                    <div className="flex items-center">
                      <span className="text-xs font-karla uppercase tracking-wider text-foreground/60 line-through mr-2">
                        {formatCurrency(room.price * 1.2)}
                      </span>
                      <span className="px-2 py-0.5 bg-green-100 text-green-800 text-xs font-karla rounded-full">
                        Save 20%
                      </span>
                    </div>
                    <p className="text-2xl font-baskervville text-[#3566ab]">
                      {formatCurrency(room.price)}
                    </p>
                  </div>
                </div>
                <button
                  onClick={() => {
                    onSelectRoom(room);
                    onClose();
                  }}
                  className="w-full py-3 px-4 bg-[#3566ab] text-white text-sm font-medium rounded-lg hover:bg-[#3566ab]/90 transition-colors shadow-sm"
                >
                  Select This Room
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default RoomDetailsModal;
