import React, { useState } from "react";
import { type RoomType } from "../../utils/types";
import MealPlanOption from "./MealPlanOption";

interface RoomTypeCardProps {
  roomType: RoomType;
  hotelCurrency: string;
  checkInDate?: string;
  checkOutDate?: string;
}

const RoomTypeCard: React.FC<RoomTypeCardProps> = ({
  roomType,
  hotelCurrency,
  checkInDate,
  checkOutDate,
}) => {
  // State for image gallery
  const [currentImageIndex, setCurrentImageIndex] = useState(0);
  const [showGalleryModal, setShowGalleryModal] = useState(false);

  // Get room images
  const getRoomImages = () => {
    const images: string[] = [];

    if (roomType.images && roomType.images.length > 0) {
      // Handle both string and object image formats
      roomType.images.forEach((image) => {
        if (typeof image === "string") {
          images.push(image);
        } else if (typeof image === "object" && image !== null) {
          const url = (image as any).url;
          if (url) images.push(url);
        }
      });
    }

    // Add thumbnail if it exists and isn't already in images
    if (roomType.thumbnail && !images.includes(roomType.thumbnail)) {
      images.unshift(roomType.thumbnail);
    }

    // Add fallback image if no images found
    if (images.length === 0) {
      images.push(
        "https://images.unsplash.com/photo-1566665797739-1674de7a421a?ixlib=rb-4.0.3&auto=format&fit=crop&w=800&q=80"
      );
    }

    return images;
  };

  // Format amenities for display
  const formatAmenities = () => {
    if (!roomType.amenities || roomType.amenities.length === 0) {
      return ["Free WiFi", "Air Conditioning", "Flat-screen TV"];
    }

    // Ensure we only have string values for amenities
    const stringAmenities = roomType.amenities.map((amenity) =>
      typeof amenity === "string"
        ? amenity
        : (amenity as any)?.name || "Amenity"
    );

    return stringAmenities;
  };

  // Get available meal plans for this room type
  const getAvailableMealPlans = () => {
    const mealPlans = roomType.priceDetails?.meal_plans || {};
    return Object.keys(mealPlans).filter(
      (plan) => mealPlans[plan as keyof typeof mealPlans]
    );
  };

  // Get currency code from room price details or fallback to hotel currency
  const getCurrencyCode = () => {
    // First try to get currency from room price details (API response)
    if (roomType.priceDetails?.currency_code) {
      return roomType.priceDetails.currency_code;
    }
    if (roomType.price?.currency_code) {
      return roomType.price.currency_code;
    }
    // Fallback to hotel currency, but convert symbol to code if needed
    if (hotelCurrency === "$") return "USD";
    if (hotelCurrency === "€") return "EUR";
    if (hotelCurrency === "£") return "GBP";
    return hotelCurrency || "USD";
  };

  // Format currency for display
  const formatCurrency = (amount: number) => {
    const currencyCode = getCurrencyCode();
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: currencyCode,
      minimumFractionDigits: 0,
    }).format(amount);
  };

  // Get room features
  const getRoomFeatures = () => {
    const features = [];

    if (roomType.size) {
      features.push({
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <rect x="3" y="3" width="18" height="18" rx="2" ry="2"></rect>
          </svg>
        ),
        text: `${roomType.size}`,
      });
    }

    if (roomType.bedType) {
      features.push({
        icon: (
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="16"
            height="16"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <path d="M2 4v16"></path>
            <path d="M22 4v16"></path>
            <path d="M2 12h20"></path>
            <path d="M2 8h20"></path>
            <path d="M2 16h20"></path>
          </svg>
        ),
        text: roomType.bedType,
      });
    }

    features.push({
      icon: (
        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="16"
          height="16"
          viewBox="0 0 24 24"
          fill="none"
          stroke="currentColor"
          strokeWidth="2"
          strokeLinecap="round"
          strokeLinejoin="round"
        >
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
          <circle cx="12" cy="7" r="4"></circle>
        </svg>
      ),
      text: `Max ${roomType.maxGuests || 2} guests`,
    });

    return features;
  };

  const availableMealPlans = getAvailableMealPlans();
  const roomImages = getRoomImages();
  const roomFeatures = getRoomFeatures();

  return (
    <div className="mb-8 bg-white border border-[#3566ab]/10 rounded-xl shadow-md hover:shadow-lg transition-all duration-300">
      {/* Main Room Card Content - Two Column Layout */}
      <div className="p-6">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
          {/* Left Column - Room Information */}
          <div className="flex flex-col">
            {/* Room Header */}
            <div className="flex justify-between items-start mb-4">
              <div>
                <h3 className="text-2xl font-baskervville text-[#3566ab] font-semibold">
                  {roomType.name}
                </h3>
              </div>

              {/* Room Price Preview */}
              <div className="text-right">
                <div className="text-lg font-medium text-[#3566ab]">
                  {formatCurrency(
                    (typeof roomType.price === 'number' ? roomType.price : roomType.price?.per_night_amount) || 0
                  )}
                </div>
                <div className="text-xs text-gray-500">per night</div>
              </div>
            </div>

            {/* Room Image with Gallery Navigation */}
            <div className="relative mb-5">
              <div
                className="overflow-hidden rounded-xl aspect-[4/3] shadow-md relative group cursor-pointer"
                onClick={() => setShowGalleryModal(true)}
              >
                <img
                  src={roomImages[currentImageIndex]}
                  alt={roomType.name}
                  className="w-full h-full object-cover hover:scale-105 transition-transform duration-700 ease-out"
                />

                {/* Image Counter Badge */}
                {roomImages.length > 1 && (
                  <div className="absolute bottom-3 left-3 bg-black/60 text-white text-xs px-2 py-1 rounded-full">
                    {currentImageIndex + 1} / {roomImages.length}
                  </div>
                )}

                {/* View All Photos Button */}
                <div className="absolute bottom-3 right-3">
                  <button
                    className="bg-white/90 hover:bg-white text-[#3566ab] text-xs px-3 py-1.5 rounded-md flex items-center shadow-sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setShowGalleryModal(true);
                    }}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1"
                    >
                      <rect
                        x="3"
                        y="3"
                        width="18"
                        height="18"
                        rx="2"
                        ry="2"
                      ></rect>
                      <circle cx="8.5" cy="8.5" r="1.5"></circle>
                      <polyline points="21 15 16 10 5 21"></polyline>
                    </svg>
                    View Photos
                  </button>
                </div>

                {/* Navigation Arrows - Only show if more than one image */}
                {roomImages.length > 1 && (
                  <>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentImageIndex((prev) =>
                          prev === 0 ? roomImages.length - 1 : prev - 1
                        );
                      }}
                      className="absolute left-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-[#3566ab] p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      aria-label="Previous image"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="15 18 9 12 15 6"></polyline>
                      </svg>
                    </button>
                    <button
                      onClick={(e) => {
                        e.stopPropagation();
                        setCurrentImageIndex((prev) =>
                          prev === roomImages.length - 1 ? 0 : prev + 1
                        );
                      }}
                      className="absolute right-2 top-1/2 -translate-y-1/2 bg-white/80 hover:bg-white text-[#3566ab] p-1.5 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                      aria-label="Next image"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <polyline points="9 18 15 12 9 6"></polyline>
                      </svg>
                    </button>
                  </>
                )}
              </div>

              {/* Room Type Label */}
              <div className="absolute top-3 left-3 bg-[#3566ab] text-white text-xs px-3 py-1.5 rounded-md uppercase tracking-wider font-medium">
                {roomType.category ||
                  (roomType.name?.includes("Deluxe")
                    ? "Deluxe Room"
                    : roomType.name?.includes("Suite")
                    ? "Suite"
                    : roomType.name?.includes("Premium")
                    ? "Premium Room"
                    : "Luxury Room")}
              </div>
            </div>

            {/* Room Description */}
            <div className="mb-4">
              <p className="text-sm text-gray-600">
                {roomType.description ||
                  "Experience luxury and comfort in this beautifully appointed room with premium amenities and stunning views."}
              </p>
            </div>

            {/* Room Key Details */}
            <div className="flex flex-wrap gap-4 mb-4">
              {roomFeatures.map((feature, index) => (
                <div
                  key={index}
                  className="flex items-center text-sm text-gray-700"
                >
                  <span className="mr-2 text-[#3566ab]">{feature.icon}</span>
                  <span>{feature.text}</span>
                </div>
              ))}
            </div>

            {/* Amenities Section */}
            <div className="mb-4">
              <h4 className="text-sm font-karla uppercase tracking-wider mb-3 text-[#3566ab]">
                Room Amenities
              </h4>
              <div className="flex flex-wrap gap-2">
                {formatAmenities().map((amenity, index) => (
                  <div
                    key={index}
                    className="px-3 py-1.5 bg-gray-100 text-gray-700 text-xs rounded-full flex items-center shadow-sm"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="12"
                      height="12"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                      className="mr-1.5 text-[#3566ab]"
                    >
                      <polyline points="20 6 9 17 4 12"></polyline>
                    </svg>
                    {amenity}
                  </div>
                ))}
              </div>
            </div>

            {/* Room Services */}
            <div className="mt-auto">
              <div className="flex flex-wrap gap-x-6 gap-y-2">
                <div className="flex items-center text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 text-green-600"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  Complimentary Breakfast Included
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 text-green-600"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  20% off on Food & Beverage services
                </div>
                <div className="flex items-center text-sm text-gray-600">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 text-green-600"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  10% Off on Laundry service
                </div>
              </div>
            </div>
          </div>

          {/* Right Column - Meal Plan Options */}
          <div className="flex flex-col">
            <h4 className="text-xl font-baskervville text-[#3566ab] mb-5 flex items-center">
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="mr-2"
              >
                <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2"></path>
                <path d="M7 2v20"></path>
                <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7"></path>
              </svg>
              Select Your Meal Plan
            </h4>

            {/* Meal Plan Options - Vertical Layout */}
            <div className="space-y-3">
              {availableMealPlans.length > 0 ? (
                availableMealPlans.map((planKey) => (
                  <MealPlanOption
                    key={planKey}
                    roomType={roomType}
                    mealPlanKey={planKey as string}
                    hotelCurrency={hotelCurrency}
                    checkInDate={checkInDate}
                    checkOutDate={checkOutDate}
                  />
                ))
              ) : (
                <div className="bg-white p-4 rounded-lg border border-[#3566ab]/10 text-center w-full">
                  <p className="text-foreground/70">
                    No meal plans available for this room type.
                  </p>
                </div>
              )}
            </div>

            {/* Additional Information */}
            <div className="mt-6 p-4 bg-[#3566ab]/5 rounded-lg border border-[#3566ab]/10">
              <h5 className="text-sm font-karla uppercase tracking-wider mb-3 text-[#3566ab]">
                Important Information
              </h5>
              <ul className="text-sm text-gray-600 space-y-2">
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 mt-0.5 text-[#3566ab]"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  Select your preferred meal plan option from the choices above
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 mt-0.5 text-[#3566ab]"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  Meal plan prices are per person per night
                </li>
                <li className="flex items-start">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="16"
                    height="16"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    className="mr-2 mt-0.5 text-[#3566ab]"
                  >
                    <polyline points="20 6 9 17 4 12"></polyline>
                  </svg>
                  Special dietary requirements can be accommodated with advance
                  notice
                </li>
              </ul>
            </div>
          </div>
        </div>
      </div>

      {/* Image Gallery Modal */}
      {showGalleryModal && (
        <div
          className="fixed inset-0 z-50 flex items-center justify-center bg-black/80"
          onClick={() => setShowGalleryModal(false)}
        >
          <div
            className="relative max-w-4xl w-full p-4"
            onClick={(e) => e.stopPropagation()}
          >
            <button
              className="absolute top-4 right-4 z-10 bg-white/80 hover:bg-white text-gray-800 p-2 rounded-full"
              onClick={() => setShowGalleryModal(false)}
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="20"
                height="20"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>

            <div className="relative">
              <img
                src={roomImages[currentImageIndex]}
                alt={`${roomType.name} - Image ${currentImageIndex + 1}`}
                className="w-full h-auto max-h-[80vh] object-contain rounded-lg"
              />

              {/* Gallery Navigation */}
              <div className="absolute left-0 right-0 bottom-0 p-4 flex justify-between">
                <button
                  onClick={() =>
                    setCurrentImageIndex((prev) =>
                      prev === 0 ? roomImages.length - 1 : prev - 1
                    )
                  }
                  className="bg-white/80 hover:bg-white text-[#3566ab] p-2 rounded-full shadow-lg"
                  aria-label="Previous image"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="15 18 9 12 15 6"></polyline>
                  </svg>
                </button>

                <div className="bg-black/60 text-white px-3 py-1.5 rounded-full text-sm">
                  {currentImageIndex + 1} / {roomImages.length}
                </div>

                <button
                  onClick={() =>
                    setCurrentImageIndex((prev) =>
                      prev === roomImages.length - 1 ? 0 : prev + 1
                    )
                  }
                  className="bg-white/80 hover:bg-white text-[#3566ab] p-2 rounded-full shadow-lg"
                  aria-label="Next image"
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="20"
                    height="20"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  >
                    <polyline points="9 18 15 12 9 6"></polyline>
                  </svg>
                </button>
              </div>
            </div>

            {/* Thumbnail Strip */}
            <div className="mt-4 flex overflow-x-auto gap-2 pb-2">
              {roomImages.map((image, index) => (
                <div
                  key={index}
                  className={`flex-shrink-0 cursor-pointer rounded-md overflow-hidden border-2 ${
                    currentImageIndex === index
                      ? "border-[#3566ab]"
                      : "border-transparent"
                  }`}
                  onClick={() => setCurrentImageIndex(index)}
                >
                  <img
                    src={image}
                    alt={`Thumbnail ${index + 1}`}
                    className="w-20 h-14 object-cover"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default RoomTypeCard;
