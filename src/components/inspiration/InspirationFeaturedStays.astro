---
import HotelCard from "../hotels/HotelCard.astro";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface Props {
  hotels: any[];
  categoryName: string;
  searchPrompt: string;
  lang: Lang;
}

const { hotels, categoryName, searchPrompt, lang } = Astro.props;
const t = useTranslations(lang);
---

<section id="featured-stays" class="py-16 lg:py-24">
  <div class="container-custom">
    <!-- Section Header -->
    <div class="text-center mb-12 lg:mb-16">
      <span class="text-sm uppercase tracking-wider text-[#285DA6] font-karla">
        {t("inspiration.featuredStays.exploreStays")}
      </span>
      <h2 class="text-3xl md:text-4xl lg:text-5xl font-baskervville uppercase tracking-[0.1em] mt-4 mb-6">
        Perfect for {categoryName}
      </h2>
      <p class="font-karla text-base md:text-lg max-w-3xl mx-auto text-foreground/80 leading-relaxed">
        Discover handpicked accommodations that perfectly embody the essence of {categoryName.toLowerCase()},
        each offering unique experiences tailored to this skiing style.
      </p>
    </div>

    <!-- Hotels Grid -->
    {hotels && hotels.length > 0 ? (
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8">
        {hotels.map((hotel) => (
          <HotelCard
            id={hotel.id}
            name={hotel.name}
            location={hotel.location}
            rating={hotel.rating}
            price={hotel.price}
            currency={hotel.currency}
            imageUrl={hotel.imageUrl}
            poeticDesc={hotel.description}
            tags={hotel.amenities}
          />
        ))}
      </div>
    ) : (
      <!-- No Hotels Found -->
      <div class="text-center py-20">
        <div class="max-w-lg mx-auto">
          <div class="w-20 h-20 bg-[#285DA6]/10 rounded-full flex items-center justify-center mx-auto mb-8">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="40"
              height="40"
              viewBox="0 0 24 24"
              fill="none"
              stroke="#285DA6"
              stroke-width="1.5"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="text-[#285DA6]"
            >
              <path d="M21 10c0 7-9 13-9 13s-9-6-9-13a9 9 0 0 1 18 0z"></path>
              <circle cx="12" cy="10" r="3"></circle>
            </svg>
          </div>
          <h3 class="text-2xl font-baskervville mb-4 text-foreground">
            Curating Perfect Stays
          </h3>
          <p class="font-karla text-foreground/70 mb-8 text-lg leading-relaxed">
            We're currently curating the perfect accommodations for {categoryName.toLowerCase()}.
            In the meantime, let our AI help you find exactly what you're looking for.
          </p>
          <a
            href={`/ai-search?query=${encodeURIComponent(searchPrompt)}`}
            class="inline-flex items-center font-karla font-bold text-sm uppercase tracking-[0.05em] text-white bg-[#285DA6] hover:bg-[#1e4a8c] px-8 py-4 rounded-full transition-all duration-300 hover:transform hover:scale-105 shadow-lg hover:shadow-xl"
          >
            {t("inspiration.findPerfectStays")}
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
              class="ml-3 transition-transform duration-300 group-hover:translate-x-1"
            >
              <line x1="5" y1="12" x2="19" y2="12"></line>
              <polyline points="12 5 19 12 12 19"></polyline>
            </svg>
          </a>
        </div>
      </div>
    )}


  </div>
</section>
