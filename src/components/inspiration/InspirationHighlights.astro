---
import { getLangFromUrl, useTranslations } from "../../i18n/utils";

interface Props {
  highlights: string[];
  idealFor: string[];
  categoryName: string;
}

const { highlights, idealFor, categoryName } = Astro.props;
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<section class="py-16 lg:py-24 bg-gradient-to-br from-gray-50 to-gray-100/50">
  <div class="container-custom">
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-12 lg:gap-20">
      <!-- What Makes This Special -->
      <div>
        <div class="mb-10">
          <div class="relative">
            <span
              class="text-sm uppercase tracking-wider text-[#285DA6] font-karla font-medium"
            >
              {t("inspiration.highlights.title")}
            </span>
            <div class="absolute -bottom-1 left-0 w-8 h-0.5 bg-[#285DA6]"></div>
          </div>
          <h2
            class="text-3xl md:text-4xl font-baskervville uppercase tracking-[0.1em] mt-6 mb-3 leading-tight"
          >
            {categoryName} Highlights
          </h2>
          <p class="font-karla text-foreground/60 text-sm">
            {t("inspiration.highlights.subtitle")}
          </p>
        </div>

        <div class="space-y-4">
          {
            highlights.map((highlight) => (
              <div class="group relative pl-6">
                <div class="absolute left-0 top-2 w-3 h-3 border-2 border-[#285DA6] rounded-full bg-white group-hover:bg-[#285DA6] transition-colors duration-300">
                  <div class="absolute inset-1 bg-[#285DA6] rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
                </div>
                <div class="absolute left-1.5 top-5 w-px h-full bg-gray-200 last:hidden" />
                <div class="pb-6">
                  <p class="font-karla text-base text-foreground leading-relaxed group-hover:text-foreground/90 transition-colors duration-300">
                    {highlight}
                  </p>
                </div>
              </div>
            ))
          }
        </div>
      </div>

      <!-- Ideal For -->
      <div>
        <div class="mb-10">
          <div class="relative">
            <span
              class="text-sm uppercase tracking-wider text-[#285DA6] font-karla font-medium"
            >
              {t("inspiration.idealFor.title")}
            </span>
            <div class="absolute -bottom-1 left-0 w-8 h-0.5 bg-[#285DA6]"></div>
          </div>
          <h2
            class="text-3xl md:text-4xl font-baskervville uppercase tracking-[0.1em] mt-6 mb-3 leading-tight"
          >
            Ideal Travelers
          </h2>
          <p class="font-karla text-foreground/60 text-sm">
            {t("inspiration.idealFor.subtitle")}
          </p>
        </div>

        <div class="grid gap-4">
          {
            idealFor.map((ideal) => (
              <div class="group relative overflow-hidden">
                <div class="flex items-center space-x-4 p-4 bg-white/60 rounded-lg border border-gray-200/50 group-hover:border-[#285DA6]/30 group-hover:bg-white/80 transition-all duration-300">
                  <div class="flex-shrink-0">
                    <div class="w-6 h-6 border border-[#285DA6]/40 rounded-md flex items-center justify-center group-hover:border-[#285DA6] group-hover:bg-[#285DA6]/5 transition-all duration-300">
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="14"
                        height="14"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="#285DA6"
                        stroke-width="2"
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        class="text-[#285DA6]"
                      >
                        <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2" />
                        <circle cx="12" cy="7" r="4" />
                      </svg>
                    </div>
                  </div>
                  <p class="font-karla text-base text-foreground leading-relaxed group-hover:text-foreground/90 transition-colors duration-300">
                    {ideal}
                  </p>
                </div>
                <div class="absolute left-0 top-0 bottom-0 w-1 bg-[#285DA6]/20 group-hover:bg-[#285DA6] transition-colors duration-300" />
              </div>
            ))
          }
        </div>
      </div>
    </div>
  </div>
</section>
