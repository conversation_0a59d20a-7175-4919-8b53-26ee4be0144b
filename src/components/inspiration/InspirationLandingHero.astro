---
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface Props {
  name: string;
  category: string;
  description: string;
  imageUrl: string;
  lang?: Lang;
}

const { name, category, description, imageUrl, lang = "en" } = Astro.props;
const t = useTranslations(lang);
---

<div class="container-custom">
  <section class="relative h-[50vh] md:h-[60vh] overflow-hidden rounded-lg">
    <img
      src={imageUrl}
      alt={name}
      class="w-full h-full object-cover rounded-lg"
    />
    <div
      class="absolute inset-0 bg-gradient-to-b from-black/20 via-black/40 to-black/70 flex items-center"
    >
      <!-- Content -->
      <div class="container-custom">
        <div class="max-w-4xl">
          <!-- Category Badge -->
          <div class="mb-6 animate-fade-in">
            <span
              class="inline-block px-6 py-3 bg-[#285DA6]/90 backdrop-blur-sm rounded-full text-white text-sm font-karla font-semibold uppercase tracking-wider shadow-lg"
            >
              {category}
            </span>
          </div>

          <!-- Main Title -->
          <h1
            class="text-3xl md:text-4xl lg:text-5xl text-white font-baskervville mb-6 animate-fade-in animate-delay-200"
          >
            {name}
          </h1>

          <!-- Description -->
          <p
            class="text-lg md:text-xl text-white/90 mb-8 leading-relaxed animate-fade-in animate-delay-400 max-w-3xl"
          >
            {description}
          </p>

          <!-- CTA Button -->
          <div class="animate-fade-in animate-delay-600">
            <a
              href="#featured-stays"
              class="inline-flex items-center font-karla font-bold text-sm uppercase tracking-[0.05em] text-white bg-[#285DA6]/90 hover:bg-[#285DA6] backdrop-blur-sm px-8 py-4 rounded-full transition-all duration-300 hover:transform hover:scale-105 shadow-lg hover:shadow-xl"
            >
              {t("inspiration.featuredStays.exploreStays")}
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="18"
                height="18"
                viewBox="0 0 24 24"
                fill="none"
                stroke="white"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
                class="ml-3 transition-transform duration-300 group-hover:translate-x-1"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
                <polyline points="12 5 19 12 12 19"></polyline>
              </svg>
            </a>
          </div>
        </div>
      </div>
    </div>
  </section>

  <style>
    @keyframes fadeIn {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }

    .animate-fade-in {
      animation: fadeIn 1s ease-out forwards;
      opacity: 0;
    }

    .animate-delay-200 {
      animation-delay: 0.2s;
    }

    .animate-delay-400 {
      animation-delay: 0.4s;
    }

    .animate-delay-600 {
      animation-delay: 0.6s;
    }
  </style>
</div>
