import React, { useState, useRef, useEffect } from "react";
import { initializeDefaultCurrency } from "../../utils/currencyHelper";
import "../../styles/language-selector.css";

interface Currency {
  code: string;
  name: string;
  symbol: string;
  decimal_digits: number;
  rounding: number;
  is_default: boolean;
}

const CurrencySelector: React.FC = () => {
  const [isClient, setIsClient] = useState(false);
  const [isOpen, setIsOpen] = useState(false);
  const [currencies, setCurrencies] = useState<Currency[]>([]);
  const [selectedCurrency, setSelectedCurrency] = useState<Currency | null>(null);
  const [loading, setLoading] = useState(true);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Check if we're on the client side
  useEffect(() => {
    setIsClient(true);
  }, []);

  // Fetch currencies when component mounts on client side
  useEffect(() => {
    if (!isClient) return;

    const fetchCurrencies = async () => {
      try {
        setLoading(true);

        const response = await fetch(
          `${import.meta.env.PUBLIC_BACKEND_URL}/store/currencies`,
          {
            method: "GET",
            headers: {
              "Content-Type": "application/json",
              "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY || "",
            },
          }
        );

        if (!response.ok) {
          throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();
        console.log("CurrencySelector: Fetched currencies:", data);

        if (data.currencies && Array.isArray(data.currencies)) {
          setCurrencies(data.currencies);

          // Initialize default currency cache for other components
          await initializeDefaultCurrency();

          // Set selected currency from localStorage or default
          const savedCurrencyCode = localStorage.getItem("selected_currency");
          let currencyToSelect: Currency | null = null;

          if (savedCurrencyCode) {
            currencyToSelect = data.currencies.find(
              (c: Currency) => c.code === savedCurrencyCode
            );
          }

          if (!currencyToSelect) {
            currencyToSelect = data.currencies.find((c: Currency) => c.is_default);
          }

          if (currencyToSelect) {
            setSelectedCurrency(currencyToSelect);
          }
        }
      } catch (error) {
        console.error("CurrencySelector: Error fetching currencies:", error);

        // Fallback to default currencies
        const fallbackCurrencies: Currency[] = [
          {
            code: "GBP",
            name: "British Pound Sterling",
            symbol: "£",
            decimal_digits: 2,
            rounding: 0,
            is_default: true
          },
          {
            code: "USD",
            name: "US Dollar",
            symbol: "$",
            decimal_digits: 2,
            rounding: 0,
            is_default: false
          },
          {
            code: "CHF",
            name: "Swiss Franc",
            symbol: "CHF",
            decimal_digits: 2,
            rounding: 0.05,
            is_default: false
          }
        ];

        setCurrencies(fallbackCurrencies);
        setSelectedCurrency(fallbackCurrencies[0]);

        // Cache the default currency for other components
        const defaultCurrency = fallbackCurrencies.find(c => c.is_default) || fallbackCurrencies[0];
        sessionStorage.setItem("default_currency", defaultCurrency.code);
      } finally {
        setLoading(false);
      }
    };

    fetchCurrencies();
  }, [isClient]);

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Handle currency change
  const handleCurrencyChange = (currency: Currency) => {
    setSelectedCurrency(currency);
    localStorage.setItem("selected_currency", currency.code);
    setIsOpen(false);

    // Trigger a custom event to notify other components of currency change
    window.dispatchEvent(new CustomEvent('currencyChanged', {
      detail: { currency }
    }));

    // Refresh the page to update all pricing information
    setTimeout(() => {
      window.location.reload();
    }, 100); // Small delay to ensure localStorage is updated
  };

  // Get appropriate CSS class based on symbol length
  const getSymbolSizeClass = (symbol: string) => {
    if (symbol.length >= 3) {
      return 'currency-symbol-small'; // For CHF, etc.
    } else if (symbol.length === 2) {
      return 'currency-symbol-medium'; // For A$, C$, etc.
    } else {
      return 'currency-symbol-large'; // For £, $, €, etc.
    }
  };

  // Don't render during SSR or if loading
  if (!isClient || loading || !selectedCurrency || currencies.length === 0) {
    return null;
  }

  return (
    <div className="language-selector currency-selector" ref={dropdownRef}>
      <button
        className="currency-selector-button"
        onClick={() => setIsOpen(!isOpen)}
        aria-label={`Select Currency - Current: ${selectedCurrency.name}`}
        aria-expanded={isOpen}
        title={`${selectedCurrency.name} (${selectedCurrency.code})`}
      >
        <span className={`currency-symbol-display ${getSymbolSizeClass(selectedCurrency.symbol)}`}>
          {selectedCurrency.symbol}
        </span>
      </button>

      {isOpen && (
        <div className="currency-selector-dropdown">
          {currencies.map((currency) => (
            <button
              key={currency.code}
              className={`currency-selector-option ${
                selectedCurrency.code === currency.code ? "active" : ""
              }`}
              onClick={() => handleCurrencyChange(currency)}
            >
              <div className="currency-option-content">
                <span className={`currency-symbol-dropdown ${getSymbolSizeClass(currency.symbol)}`}>
                  {currency.symbol}
                </span>
                <div className="currency-option-text">
                  <span className="currency-code-text">{currency.name}</span>
                  {/* <span className="currency-name-text">{currency.code}</span> */}
                </div>
              </div>
              {/* {selectedCurrency.code === currency.code && (
                <svg
                  className="currency-check-icon"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                >
                  <path
                    d="M20 6L9 17L4 12"
                    stroke="currentColor"
                    strokeWidth="2"
                    strokeLinecap="round"
                    strokeLinejoin="round"
                  />
                </svg>
              )} */}
            </button>
          ))}
        </div>
      )}
    </div>
  );
};

export default CurrencySelector;
