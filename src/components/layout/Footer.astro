---
import { getLangFromUrl, useTranslations, buildUrl } from "../../i18n/utils";

const currentYear = new Date().getFullYear();
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<footer class="bg-background border-t border-border/30 py-16">
  <div class="container-custom">
    <div
      class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-x-8 gap-y-10"
    >
      <!-- ABOUT Column -->
      <div>
        <h3
          class="text-sm font-karla uppercase tracking-wider mb-6 text-foreground"
        >
          {t("footer.about")}
        </h3>
        <ul class="space-y-3">
          <li>
            <a
              href={buildUrl("/booking-conditions", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              {t("footer.bookingConditions")}
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/faq", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              {t("footer.faqs")}
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/privacy-policy", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              {t("footer.privacyPolicy")}
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/partnership", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              {t("footer.partnership")}
            </a>
          </li>
        </ul>
      </div>

      <!-- DESTINATIONS Column -->
      <div>
        <h3
          class="text-sm font-karla uppercase tracking-wider mb-6 text-foreground"
        >
          {t("footer.destinations")}
        </h3>
        <ul class="space-y-3 uppercase">
          <li>
            <a
              href={buildUrl("/destinations/arosa", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              AROSA
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/andermatt", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              ANDERMATT
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/grindelwald", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              GRINDELWALD
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/villars-sur-ollon", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              VILLARS SUR OLLON
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/flims", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              FLIMS LAAX
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/st-moritz-pontresina", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              ST. MORITZ-PONTRESINA
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/zermatt", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              ZERMATT
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/klosters", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              KLOSTERS
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/destinations/san-cassiano", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              SAN CASSIANO
            </a>
          </li>
        </ul>
      </div>

      <!-- STAYS Column -->
      <div>
        <h3
          class="text-sm font-karla uppercase tracking-wider mb-6 text-foreground"
        >
          {t("footer.inspiration")}
        </h3>
        <ul class="space-y-3 uppercase">
          <li>
            <a
              href={buildUrl("/inspiration/alpine-icons", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Alpine Icons
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/family-focused-skiing", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Family-Focused Skiing
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/first-time-skiing", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              First-Time Skiing
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/powder-freeride", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Powder & Freeride
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/luxury-wellness", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Luxury & Wellness
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/gourmet-gastronomy", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Gourmet & Gastronomy on Snow
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/scenic-traditional", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Scenic & Traditional Alpine Charm
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/sunny-relaxed", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Sunny & Relaxed Skiing
            </a>
          </li>
          <li>
            <a
              href={buildUrl("/inspiration/teen-freestyle", lang)}
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              Teen & Freestyle Friendly
            </a>
          </li>
        </ul>
      </div>

      <!-- CONTACT Column -->
      <div>
        <h3
          class="text-sm font-karla uppercase tracking-wider mb-6 text-foreground"
        >
          {t("footer.contact")}
        </h3>
        <ul class="space-y-3">
          <li>
            <a
              href="tel:+442082465300"
              class="text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
              style="text-transform: uppercase; font-family: 'Karla', sans-serif !important; font-size: 12px !important; letter-spacing: 0.05em !important; font-weight: 400 !important;"
            >
              +44 (0) ************
            </a>
          </li>
          <li>
            <a
              href="#"
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              W111, VOX STUDIOS, LONDON SE11 5JH, UK
            </a>
          </li>
          <li>
            <a
              href="mailto:<EMAIL>"
              class="text-xs font-karla text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
            >
              <EMAIL>
            </a>
          </li>
          <!-- <li class="mt-10">
            <div class="flex space-x-4">
              <a
                href="#"
                class="text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
                aria-label="Facebook"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M18 2h-3a5 5 0 0 0-5 5v3H7v4h3v8h4v-8h3l1-4h-4V7a1 1 0 0 1 1-1h3z"
                  ></path>
                </svg>
              </a>
              <a
                href="#"
                class="text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
                aria-label="Instagram"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <rect x="2" y="2" width="20" height="20" rx="5" ry="5"></rect>
                  <path d="M16 11.37A4 4 0 1 1 12.63 8 4 4 0 0 1 16 11.37z"
                  ></path>
                  <line x1="17.5" y1="6.5" x2="17.51" y2="6.5"></line>
                </svg>
              </a>
              <a
                href="#"
                class="text-foreground/70 hover:text-[#285DA6] relative hover:after:absolute hover:after:bottom-[-4px] hover:after:left-0 hover:after:right-0 hover:after:h-[1px] hover:after:bg-[#285DA6] transition-all"
                aria-label="LinkedIn"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                >
                  <path
                    d="M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z"
                  ></path>
                  <rect x="2" y="9" width="4" height="12"></rect>
                  <circle cx="4" cy="4" r="2"></circle>
                </svg>
              </a>
            </div>
          </li> -->
        </ul>
      </div>
    </div>

    <div
      class="mt-16 pt-8 border-t border-border/30 flex flex-col md:flex-row justify-between items-center"
    >
      <p class="text-xs font-karla text-foreground/60">
        &copy; {currentYear} Perfect Piste. {t("footer.copyright")}
      </p>
      <div class="mt-6 md:mt-0">
        <!-- Empty div to maintain layout -->
      </div>
    </div>
  </div>
</footer>
