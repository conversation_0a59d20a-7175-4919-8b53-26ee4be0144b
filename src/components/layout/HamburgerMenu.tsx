import { useState, useEffect, useRef } from "react";
import "../../styles/hamburger-menu.css";

interface NavItem {
  label: string;
  href: string;
}

interface HamburgerMenuProps {
  navItems: NavItem[];
  currentPath: string;
}

const HamburgerMenu = ({ navItems, currentPath }: HamburgerMenuProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const menuRef = useRef<HTMLDivElement>(null);

  const toggleMenu = () => {
    setIsOpen(!isOpen);
    // Toggle body scroll
    if (!isOpen) {
      document.body.style.overflow = "hidden";
      // Lower the z-index of header when menu is open
      const headerWrapper = document.querySelector(".header-wrapper");
      if (headerWrapper) {
        headerWrapper.setAttribute("style", "z-index: 99 !important");
      }
    } else {
      document.body.style.overflow = "";
      // Restore the z-index of header when menu is closed
      const headerWrapper = document.querySelector(".header-wrapper");
      if (headerWrapper) {
        headerWrapper.removeAttribute("style");
      }
    }
  };

  // Close menu when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (menuRef.current && !menuRef.current.contains(event.target as Node)) {
        setIsOpen(false);
        document.body.style.overflow = "";
        // Restore header z-index
        const headerWrapper = document.querySelector(".header-wrapper");
        if (headerWrapper) {
          headerWrapper.removeAttribute("style");
        }
      }
    };

    // Add event listener when menu is open
    if (isOpen) {
      document.addEventListener("mousedown", handleClickOutside);
    }

    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      // Ensure header z-index is restored on unmount
      if (isOpen) {
        document.body.style.overflow = "";
        const headerWrapper = document.querySelector(".header-wrapper");
        if (headerWrapper) {
          headerWrapper.removeAttribute("style");
        }
      }
    };
  }, [isOpen]);

  // Close menu on escape key
  useEffect(() => {
    const handleEscKey = (event: KeyboardEvent) => {
      if (event.key === "Escape") {
        setIsOpen(false);
        document.body.style.overflow = "";
        // Restore header z-index
        const headerWrapper = document.querySelector(".header-wrapper");
        if (headerWrapper) {
          headerWrapper.removeAttribute("style");
        }
      }
    };

    if (isOpen) {
      document.addEventListener("keydown", handleEscKey);
    }

    return () => {
      document.removeEventListener("keydown", handleEscKey);
    };
  }, [isOpen]);

  // Add event listener to the hamburger button
  useEffect(() => {
    const hamburgerButton = document.getElementById("hamburger-button");

    if (hamburgerButton) {
      hamburgerButton.addEventListener("click", toggleMenu);
    }

    return () => {
      if (hamburgerButton) {
        hamburgerButton.removeEventListener("click", toggleMenu);
      }
    };
  }, [isOpen]);

  return (
    <div ref={menuRef} style={{ position: "relative", zIndex: 200 }}>
      {/* Full-screen menu overlay */}
      <div
        className={`hamburger-menu-overlay ${isOpen ? "open" : ""}`}
        aria-hidden={!isOpen}
      >
        <div className="hamburger-menu-container">
          <div className="hamburger-menu-header">
            <button
              className="hamburger-close-button"
              onClick={toggleMenu}
              aria-label="Close menu"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="1.5"
                strokeLinecap="round"
                strokeLinejoin="round"
              >
                <line x1="18" y1="6" x2="6" y2="18"></line>
                <line x1="6" y1="6" x2="18" y2="18"></line>
              </svg>
            </button>
          </div>

          <nav className="hamburger-menu-nav">
            <ul>
              {navItems.map((item) => (
                <li key={item.href}>
                  <a
                    href={item.href}
                    className={currentPath === item.href ? "active" : ""}
                  >
                    {item.label}
                  </a>
                </li>
              ))}
            </ul>
          </nav>

          <div className="hamburger-menu-footer">
            <div className="hamburger-menu-contact">
              <a href="tel:+1234567890">+1 (234) 567-890</a>
              <a href="mailto:<EMAIL>"><EMAIL></a>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default HamburgerMenu;
