import React, { useState, useRef, useEffect } from "react";
import "../../styles/language-selector.css";
import {
  UKFlag,
  FranceFlag,
  GermanyFlag,
  ItalyFlag,
  JapanFlag,
} from "./FlagIcons";
import { languages, defaultLang } from "../../i18n/ui";

interface Language {
  code: string;
  label: string;
  flagComponent: React.ReactNode;
}

// Helper function to get current language from URL
function getCurrentLanguageFromURL(): string {
  const path = window.location.pathname;
  const segments = path.split("/").filter(Boolean);

  // Check if first segment is a language code
  if (segments.length > 0 && Object.keys(languages).includes(segments[0])) {
    return segments[0];
  }

  return defaultLang;
}

// Helper function to navigate to language URL
function navigateToLanguage(langCode: string, currentPath: string) {
  const segments = currentPath.split("/").filter(Boolean);

  // Remove current language from path if it exists
  if (segments.length > 0 && Object.keys(languages).includes(segments[0])) {
    segments.shift();
  }

  // Build new path
  let newPath = "/";
  if (langCode !== defaultLang) {
    newPath += langCode + "/";
  }
  if (segments.length > 0) {
    newPath += segments.join("/");
  }

  // Navigate to new URL
  window.location.href = newPath;
}

const LanguageSelector: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState("en");
  const dropdownRef = useRef<HTMLDivElement>(null);

  const supportedLanguages: Language[] = [
    {
      code: "en",
      label: "English",
      flagComponent: (
        <div className="w-6 h-6 rounded-full overflow-hidden flex items-center justify-center">
          <UKFlag className="w-8 h-8 object-cover" />
        </div>
      ),
    },
    {
      code: "de",
      label: "Deutsch",
      flagComponent: (
        <div className="w-6 h-6 rounded-full overflow-hidden flex items-center justify-center">
          <GermanyFlag className="w-8 h-8 object-cover" />
        </div>
      ),
    },
    {
      code: "ja",
      label: "日本語",
      flagComponent: (
        <div className="w-6 h-6 rounded-full overflow-hidden flex items-center justify-center">
          <JapanFlag className="w-8 h-8 object-cover" />
        </div>
      ),
    },
  ];

  // Set current language from URL on component mount
  useEffect(() => {
    const currentLang = getCurrentLanguageFromURL();
    setSelectedLanguage(currentLang);
  }, []);

  // Handle language change
  const handleLanguageChange = (langCode: string) => {
    setSelectedLanguage(langCode);
    setIsOpen(false);

    // Navigate to the new language URL
    navigateToLanguage(langCode, window.location.pathname);
  };

  // Close dropdown when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Find the selected language
  const selectedOption =
    supportedLanguages.find((lang) => lang.code === selectedLanguage) ||
    supportedLanguages[0];

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        type="button"
        className="premium-icon-container w-10 h-10 flex items-center justify-center"
        onClick={() => setIsOpen(!isOpen)}
        aria-haspopup="listbox"
        aria-expanded={isOpen}
        aria-label="Select language"
      >
        <div className="flex items-center justify-center">
          <div className="flex items-center justify-center">
            {selectedOption.flagComponent}
          </div>
        </div>
      </button>

      {isOpen && (
        <div className="absolute right-0 mt-2 w-44 bg-white/95 backdrop-blur-xl rounded-xl z-10 animate-slide-down">
          <div className="absolute inset-0 bg-gradient-to-b from-white/10 to-white/0 rounded-xl pointer-events-none"></div>
          <ul role="listbox" className="py-2 relative z-10">
            {supportedLanguages.map((language) => (
              <li
                key={language.code}
                role="option"
                aria-selected={selectedLanguage === language.code}
                className={`px-3 py-2 text-sm cursor-pointer transition-all hover:bg-blue-50/50 ${
                  selectedLanguage === language.code
                    ? "text-[#285DA6] font-medium bg-blue-50/30"
                    : "hover:text-[#285DA6]"
                }`}
                onClick={() => handleLanguageChange(language.code)}
              >
                <div className="flex items-center">
                  <div className="mr-4 flex items-center">
                    <div className="w-6 h-6 rounded-full overflow-hidden flex items-center justify-center">
                      {language.code === "en" && (
                        <UKFlag className="w-8 h-8 object-cover" />
                      )}
                      {language.code === "fr" && (
                        <FranceFlag className="w-8 h-8 object-cover" />
                      )}
                      {language.code === "it" && (
                        <ItalyFlag className="w-8 h-8 object-cover" />
                      )}
                      {language.code === "de" && (
                        <GermanyFlag className="w-8 h-8 object-cover" />
                      )}
                      {language.code === "ja" && (
                        <JapanFlag className="w-8 h-8 object-cover" />
                      )}
                    </div>
                  </div>
                  <span className="font-karla tracking-wide text-sm">
                    {language.label}
                  </span>
                </div>
              </li>
            ))}
          </ul>
        </div>
      )}
    </div>
  );
};

export default LanguageSelector;
