import React, { useState } from "react";
import { Send, CheckCircle, AlertCircle } from "lucide-react";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface FormData {
  firstName: string;
  lastName: string;
  email: string;
  telephone: string;
  howDidYouHear: string;
  message: string;
}

interface Props {
  lang: Lang;
}

const PartnershipApplicationForm: React.FC<Props> = ({ lang }) => {
  const t = useTranslations(lang);
  const [formData, setFormData] = useState<FormData>({
    firstName: "",
    lastName: "",
    email: "",
    telephone: "",
    howDidYouHear: "",
    message: "",
  });

  const [isSubmitting, setIsSubmitting] = useState(false);
  const [submitStatus, setSubmitStatus] = useState<
    "idle" | "success" | "error"
  >("idle");
  const [errors, setErrors] = useState<Partial<FormData>>({});

  const handleInputChange = (
    e: React.ChangeEvent<
      HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement
    >
  ) => {
    const { name, value, type } = e.target;
    const checked = (e.target as HTMLInputElement).checked;

    setFormData((prev) => ({
      ...prev,
      [name]: type === "checkbox" ? checked : value,
    }));

    // Clear error when user starts typing
    if (errors[name as keyof FormData]) {
      setErrors((prev) => ({
        ...prev,
        [name]: undefined,
      }));
    }
  };

  const validateForm = (): boolean => {
    const newErrors: Partial<FormData> = {};

    // Required fields validation
    if (!formData.firstName.trim())
      newErrors.firstName = t("partnership.form.validation.firstNameRequired");
    if (!formData.lastName.trim())
      newErrors.lastName = t("partnership.form.validation.lastNameRequired");
    if (!formData.email.trim())
      newErrors.email = t("partnership.form.validation.emailRequired");
    if (!formData.telephone.trim())
      newErrors.telephone = t("partnership.form.validation.telephoneRequired");
    if (!formData.message.trim())
      newErrors.message = t("partnership.form.validation.messageRequired");

    // Email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (formData.email && !emailRegex.test(formData.email)) {
      newErrors.email = t("partnership.form.validation.emailInvalid");
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);
    setSubmitStatus("idle");

    try {
      // Simulate API call - replace with actual submission logic
      await new Promise((resolve) => setTimeout(resolve, 2000));

      // For now, we'll create a mailto link with the form data
      const emailBody = `
Partnership Application - ${formData.firstName} ${formData.lastName}

CONTACT INFORMATION:
Name: ${formData.firstName} ${formData.lastName}
Email: ${formData.email}
Telephone: ${formData.telephone}
How did you hear about us: ${formData.howDidYouHear || "Not specified"}

MESSAGE:
${formData.message}
      `.trim();

      const mailtoLink = `mailto:<EMAIL>?subject=Partnership Application - ${encodeURIComponent(
        formData.firstName + " " + formData.lastName
      )}&body=${encodeURIComponent(emailBody)}`;
      window.location.href = mailtoLink;

      setSubmitStatus("success");
    } catch (error) {
      setSubmitStatus("error");
    } finally {
      setIsSubmitting(false);
    }
  };

  if (submitStatus === "success") {
    return (
      <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg border border-border/20 text-center">
        <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-6">
          <CheckCircle className="h-8 w-8 text-green-600" />
        </div>
        <h3 className="text-2xl font-baskervville mb-4 text-foreground">
          {t("partnership.form.successTitle")}
        </h3>
        <p className="text-foreground/80 font-karla mb-6">
          {t("partnership.form.successMessage")}
        </p>
        <p className="text-foreground/80 font-karla mb-6">
          {t("partnership.form.successFollowUp")}
        </p>
        <button
          onClick={() => {
            setSubmitStatus("idle");
            setFormData({
              firstName: "",
              lastName: "",
              email: "",
              telephone: "",
              howDidYouHear: "",
              message: "",
            });
          }}
          className="inline-flex items-center justify-center px-6 py-3 bg-[#285DA6] text-white rounded-lg font-karla font-bold text-sm normal-case tracking-[0.05em] hover:bg-[#285DA6]/90 transition-colors"
        >
          {t("partnership.form.submitAnother")}
        </button>
      </div>
    );
  }

  return (
    <div className="bg-white/80 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg border border-border/20">
      <div className="mb-8">
        <h3 className="text-2xl font-baskervville mb-4 text-foreground">
          {t("partnership.form.title")}
        </h3>
        <p className="text-foreground/80 font-karla">
          {t("partnership.form.description")}
        </p>
      </div>

      <form onSubmit={handleSubmit} className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* First Name */}
          <div>
            <label
              htmlFor="firstName"
              className="block text-sm font-karla font-semibold text-foreground mb-2"
            >
              {t("partnership.form.firstName")} {t("partnership.form.required")}
            </label>
            <input
              type="text"
              id="firstName"
              name="firstName"
              value={formData.firstName}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg font-karla text-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 ${
                errors.firstName
                  ? "border-red-300 bg-red-50"
                  : "border-border/30 bg-white/60"
              }`}
              placeholder={t("partnership.form.firstNamePlaceholder")}
            />
            {errors.firstName && (
              <p className="mt-1 text-xs text-red-600 font-karla">
                {errors.firstName}
              </p>
            )}
          </div>

          {/* Last Name */}
          <div>
            <label
              htmlFor="lastName"
              className="block text-sm font-karla font-semibold text-foreground mb-2"
            >
              {t("partnership.form.lastName")} {t("partnership.form.required")}
            </label>
            <input
              type="text"
              id="lastName"
              name="lastName"
              value={formData.lastName}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg font-karla text-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 ${
                errors.lastName
                  ? "border-red-300 bg-red-50"
                  : "border-border/30 bg-white/60"
              }`}
              placeholder={t("partnership.form.lastNamePlaceholder")}
            />
            {errors.lastName && (
              <p className="mt-1 text-xs text-red-600 font-karla">
                {errors.lastName}
              </p>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Email */}
          <div>
            <label
              htmlFor="email"
              className="block text-sm font-karla font-semibold text-foreground mb-2"
            >
              {t("partnership.form.email")} {t("partnership.form.required")}
            </label>
            <input
              type="email"
              id="email"
              name="email"
              value={formData.email}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg font-karla text-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 ${
                errors.email
                  ? "border-red-300 bg-red-50"
                  : "border-border/30 bg-white/60"
              }`}
              placeholder={t("partnership.form.emailPlaceholder")}
            />
            {errors.email && (
              <p className="mt-1 text-xs text-red-600 font-karla">
                {errors.email}
              </p>
            )}
          </div>

          {/* Telephone */}
          <div>
            <label
              htmlFor="telephone"
              className="block text-sm font-karla font-semibold text-foreground mb-2"
            >
              {t("partnership.form.telephone")} {t("partnership.form.required")}
            </label>
            <input
              type="tel"
              id="telephone"
              name="telephone"
              value={formData.telephone}
              onChange={handleInputChange}
              className={`w-full px-4 py-3 border rounded-lg font-karla text-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 ${
                errors.telephone
                  ? "border-red-300 bg-red-50"
                  : "border-border/30 bg-white/60"
              }`}
              placeholder={t("partnership.form.telephonePlaceholder")}
            />
            {errors.telephone && (
              <p className="mt-1 text-xs text-red-600 font-karla">
                {errors.telephone}
              </p>
            )}
          </div>
        </div>

        {/* How did you hear about us */}
        <div>
          <label
            htmlFor="howDidYouHear"
            className="block text-sm font-karla font-semibold text-foreground mb-2"
          >
            {t("partnership.form.howDidYouHear")}
          </label>
          <select
            id="howDidYouHear"
            name="howDidYouHear"
            value={formData.howDidYouHear}
            onChange={handleInputChange}
            className="w-full px-4 py-3 border border-border/30 rounded-lg font-karla text-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 bg-white/60"
          >
            <option value="">{t("partnership.form.pleaseSelect")}</option>
            <option value="google-search">
              {t("partnership.form.googleSearch")}
            </option>
            <option value="social-media">
              {t("partnership.form.socialMedia")}
            </option>
            <option value="referral">{t("partnership.form.referral")}</option>
            <option value="existing-client">
              {t("partnership.form.existingClient")}
            </option>
            <option value="industry-event">
              {t("partnership.form.industryEvent")}
            </option>
            <option value="website">{t("partnership.form.website")}</option>
            <option value="advertisement">
              {t("partnership.form.advertisement")}
            </option>
            <option value="other">{t("partnership.form.other")}</option>
          </select>
        </div>

        {/* Message */}
        <div>
          <label
            htmlFor="message"
            className="block text-sm font-karla font-semibold text-foreground mb-2"
          >
            {t("partnership.form.message")} {t("partnership.form.required")}
          </label>
          <textarea
            id="message"
            name="message"
            value={formData.message}
            onChange={handleInputChange}
            rows={5}
            className={`w-full px-4 py-3 border rounded-lg font-karla text-sm focus:outline-none focus:ring-2 focus:ring-[#285DA6]/20 focus:border-[#285DA6]/40 transition-all duration-200 resize-vertical ${
              errors.message
                ? "border-red-300 bg-red-50"
                : "border-border/30 bg-white/60"
            }`}
            placeholder={t("partnership.form.messagePlaceholder")}
          />
          {errors.message && (
            <p className="mt-1 text-xs text-red-600 font-karla">
              {errors.message}
            </p>
          )}
        </div>

        {submitStatus === "error" && (
          <div className="bg-red-50 border border-red-200 rounded-lg p-4 flex items-center space-x-3">
            <AlertCircle className="h-5 w-5 text-red-600 flex-shrink-0" />
            <p className="text-sm font-karla text-red-700">
              {t("partnership.form.errorMessage")}
            </p>
          </div>
        )}

        <div className="flex justify-center pt-6">
          <button
            type="submit"
            disabled={isSubmitting}
            className="inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-[#285DA6] to-[#1e4a8c] text-white rounded-lg font-karla font-bold text-sm normal-case tracking-[0.05em] hover:from-[#285DA6]/90 hover:to-[#1e4a8c]/90 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 disabled:opacity-50 disabled:cursor-not-allowed disabled:transform-none"
          >
            {isSubmitting ? (
              <>
                <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white mr-2"></div>
                {t("partnership.form.submitting")}
              </>
            ) : (
              <>
                <Send className="h-4 w-4 mr-2" />
                {t("partnership.form.submit")}
              </>
            )}
          </button>
        </div>
      </form>
    </div>
  );
};

export default PartnershipApplicationForm;
