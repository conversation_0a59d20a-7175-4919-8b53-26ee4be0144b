---
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

// Props for the component
interface Props {
  title?: string;
  subtitle?: string;
  imageUrl?: string;
  lang: Lang;
}

const {
  title,
  subtitle,
  imageUrl = "https://images.unsplash.com/photo-1551524164-6cf2ac2d8c8c?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80",
  lang,
} = Astro.props;

const t = useTranslations(lang);
---

<!-- Hero Section -->
<div class="container-custom">
  <section class="relative h-[50vh] sm:h-[60vh] overflow-hidden rounded-lg">
    <img
      src={imageUrl}
      alt={title}
      class="w-full h-full object-cover rounded-lg"
    />
    <div
      class="absolute inset-0 bg-gradient-to-r from-black/70 to-black/50 flex items-center"
    >
      <div class="container-custom">
        <div>
          <div class="max-w-4xl">
            <h1
              class="text-2xl sm:text-4xl md:text-5xl lg:text-6xl text-white font-baskervville mb-4 sm:mb-6 animate-fade-in leading-tight"
            >
              {title}
            </h1>
            <p
              class="text-base sm:text-xl text-white/90 mb-6 sm:mb-8 animate-fade-in animate-delay-200 max-w-2xl"
            >
              {subtitle}
            </p>
            <div
              class="flex flex-col sm:flex-row gap-4 sm:gap-6 animate-fade-in animate-delay-400"
            >
              <a
                href="#partnership-info"
                class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
              >
                {t("partnership.hero.learnMore")}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="ml-2"
                >
                  <line x1="5" y1="12" x2="19" y2="12"></line>
                  <polyline points="12 5 19 12 12 19"></polyline>
                </svg>
              </a>
              <a
                href="#apply-now"
                class="inline-flex items-center font-karla font-bold text-xs uppercase tracking-[0.05em] text-white border-b border-white pb-1 transition-all hover:border-transparent whitespace-nowrap"
              >
                {t("partnership.hero.applyNow")}
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="16"
                  height="16"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="white"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="ml-2"
                >
                  <path
                    d="M9.937 15.5A2 2 0 0 0 8.5 14.063l-6.135-1.582a.5.5 0 0 1 0-.962L8.5 9.936A2 2 0 0 0 9.937 8.5l1.582-6.135a.5.5 0 0 1 .963 0L14.063 8.5A2 2 0 0 0 15.5 9.937l6.135 1.582a.5.5 0 0 1 0 .962L15.5 14.063a2 2 0 0 0-1.437 1.437l-1.582 6.135a.5.5 0 0 1-.963 0z"
                  ></path>
                  <path d="M20 3v4"></path>
                  <path d="M22 5h-4"></path>
                  <path d="M4 17v2"></path>
                  <path d="M5 18H3"></path>
                </svg>
              </a>
            </div>
          </div>
        </div>
      </div>
    </div>
  </section>
</div>

<style>
  @keyframes fadeIn {
    from {
      opacity: 0;
      transform: translateY(20px);
    }
    to {
      opacity: 1;
      transform: translateY(0);
    }
  }

  .animate-fade-in {
    animation: fadeIn 0.8s ease-out forwards;
    opacity: 0;
  }

  .animate-delay-200 {
    animation-delay: 0.2s;
  }

  .animate-delay-400 {
    animation-delay: 0.4s;
  }
</style>
