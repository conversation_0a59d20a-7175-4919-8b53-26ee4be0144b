---
import { useTranslations } from "../../i18n/utils";
import type { <PERSON> } from "../../i18n/ui";

interface Props {
  lang: <PERSON>;
}

const { lang } = Astro.props;
const t = useTranslations(lang);
---

<section class="py-12 sm:py-16 lg:py-20 bg-background">
  <div class="container-custom max-w-6xl mx-auto px-4 sm:px-6 lg:px-8">
    <!-- Section Header -->
    <div class="text-center mb-12 lg:mb-16">
      <span
        class="text-sm uppercase tracking-wider text-[#285DA6] font-karla mb-4 block"
      >
        {t("partnership.testimonials.subtitle")}
      </span>
      <h2
        class="text-3xl sm:text-4xl lg:text-5xl font-baskervville mb-6 text-foreground"
      >
        {t("partnership.testimonials.title")}
      </h2>
      <p class="text-lg text-foreground/70 font-karla max-w-3xl mx-auto">
        {t("partnership.testimonials.description")}
      </p>
    </div>

    <!-- Testimonials Grid -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-8 lg:gap-12">
      <!-- Testimonial 1 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg border border-border/20 hover:shadow-xl transition-all duration-300"
      >
        <div class="mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="opacity-60"
          >
            <path
              d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"
            ></path>
            <path
              d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"
            ></path>
          </svg>
        </div>

        <blockquote
          class="text-lg font-baskervville text-foreground mb-6 italic leading-relaxed"
        >
          "I have been working as a Perfect Piste partner for many years. I
          enjoy being able to offer my clients exclusive travel incentives and
          ideas and the team at Perfect Piste make it very easy for this to
          work. The rewards are just a bonus for me being able to deliver a
          unique offering to my clients."
        </blockquote>

        <div class="flex items-center">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M3 21h18"></path>
              <path d="M5 21V7l8-4v18"></path>
              <path d="M19 21V11l-6-4"></path>
            </svg>
          </div>
          <div>
            <p class="font-karla font-semibold text-foreground">
              Corporate Partner
            </p>
            <p class="font-karla text-sm text-foreground/60">
              Fortune 500 Company
            </p>
          </div>
        </div>
      </div>

      <!-- Testimonial 2 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg border border-border/20 hover:shadow-xl transition-all duration-300"
      >
        <div class="mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="opacity-60"
          >
            <path
              d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"
            ></path>
            <path
              d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"
            ></path>
          </svg>
        </div>

        <blockquote
          class="text-lg font-baskervville text-foreground mb-6 italic leading-relaxed"
        >
          "I have tried other networks and programmes but Perfect Piste has made
          it easy and simple for (ultra) high-end client offerings. I don't lose
          my clients but they benefit from Perfect Piste expertise and
          experiences. This helps me offer something different and exceptional."
        </blockquote>

        <div class="flex items-center">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div>
            <p class="font-karla font-semibold text-foreground">
              Luxury Concierge Service
            </p>
            <p class="font-karla text-sm text-foreground/60">
              Private Client Network
            </p>
          </div>
        </div>
      </div>

      <!-- Testimonial 3 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg border border-border/20 hover:shadow-xl transition-all duration-300"
      >
        <div class="mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="opacity-60"
          >
            <path
              d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"
            ></path>
            <path
              d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"
            ></path>
          </svg>
        </div>

        <blockquote
          class="text-lg font-baskervville text-foreground mb-6 italic leading-relaxed"
        >
          "The partnership with Perfect Piste has elevated our member offerings
          significantly. Their attention to detail and understanding of luxury
          travel expectations perfectly aligns with our club's standards. Our
          members consistently provide exceptional feedback."
        </blockquote>

        <div class="flex items-center">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div>
            <p class="font-karla font-semibold text-foreground">
              Exclusive Members Club
            </p>
            <p class="font-karla text-sm text-foreground/60">
              London Private Club
            </p>
          </div>
        </div>
      </div>

      <!-- Testimonial 4 -->
      <div
        class="bg-white/80 backdrop-blur-sm rounded-2xl p-8 lg:p-10 shadow-lg border border-border/20 hover:shadow-xl transition-all duration-300"
      >
        <div class="mb-6">
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="40"
            height="40"
            viewBox="0 0 24 24"
            fill="none"
            stroke="#285DA6"
            stroke-width="1.5"
            stroke-linecap="round"
            stroke-linejoin="round"
            class="opacity-60"
          >
            <path
              d="M3 21c3 0 7-1 7-8V5c0-1.25-.756-2.017-2-2H4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2 1 0 1 0 1 1v1c0 1-1 2-2 2s-1 .008-1 1.031V20c0 1 0 1 1 1z"
            ></path>
            <path
              d="M15 21c3 0 7-1 7-8V5c0-1.25-.757-2.017-2-2h-4c-1.25 0-2 .75-2 1.972V11c0 1.25.75 2 2 2h.75c0 2.25.25 4-2.75 4v3c0 1 0 1 1 1z"
            ></path>
          </svg>
        </div>

        <blockquote
          class="text-lg font-baskervville text-foreground mb-6 italic leading-relaxed"
        >
          "Working with Perfect Piste has transformed how we approach luxury
          travel for our executive clients. Their expertise in ski destinations
          and seamless booking process has made us the go-to choice for winter
          corporate retreats."
        </blockquote>

        <div class="flex items-center">
          <div
            class="w-12 h-12 bg-gradient-to-br from-[#285DA6] to-[#1e4a8c] rounded-full flex items-center justify-center mr-4"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white"
              stroke-width="2"
              stroke-linecap="round"
              stroke-linejoin="round"
            >
              <path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path>
              <circle cx="9" cy="7" r="4"></circle>
              <path d="M22 21v-2a4 4 0 0 0-3-3.87"></path>
              <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
            </svg>
          </div>
          <div>
            <p class="font-karla font-semibold text-foreground">
              Executive Assistant Network
            </p>
            <p class="font-karla text-sm text-foreground/60">
              Global EA Community
            </p>
          </div>
        </div>
      </div>
    </div>

    <!-- Stats Section -->
    <div class="mt-16 lg:mt-20 grid grid-cols-2 md:grid-cols-4 gap-8">
      <div class="text-center">
        <div class="text-3xl lg:text-4xl font-baskervville text-[#285DA6] mb-2">
          50+
        </div>
        <p
          class="font-karla text-foreground/70 text-sm normal-case tracking-wider"
        >
          {t("partnership.stats.activePartners")}
        </p>
      </div>
      <div class="text-center">
        <div class="text-3xl lg:text-4xl font-baskervville text-[#285DA6] mb-2">
          95%
        </div>
        <p
          class="font-karla text-foreground/70 text-sm normal-case tracking-wider"
        >
          {t("partnership.stats.partnerSatisfaction")}
        </p>
      </div>
      <div class="text-center">
        <div class="text-3xl lg:text-4xl font-baskervville text-[#285DA6] mb-2">
          15+
        </div>
        <p
          class="font-karla text-foreground/70 text-sm normal-case tracking-wider"
        >
          {t("partnership.stats.yearsExperience")}
        </p>
      </div>
      <div class="text-center">
        <div class="text-3xl lg:text-4xl font-baskervville text-[#285DA6] mb-2">
          24/7
        </div>
        <p
          class="font-karla text-foreground/70 text-sm normal-case tracking-wider"
        >
          {t("partnership.stats.partnerSupport")}
        </p>
      </div>
    </div>
  </div>
</section>
