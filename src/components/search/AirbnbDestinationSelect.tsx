import React from "react";
import { destinations } from "../copilot/constants/destinations";

interface AirbnbDestinationSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
}

const AirbnbDestinationSelect: React.FC<AirbnbDestinationSelectProps> = ({
  value,
  onChange,
  placeholder = "Search destinations",
}) => {


  return (
    <select
      className="airbnb-destination-select"
      value={value}
      onChange={(e) => onChange(e.target.value)}
      aria-label="Select destination"
    >
      <option value="">{placeholder}</option>
      {destinations.map((dest) => (
        <option key={dest.id} value={dest.id}>
          {dest.name}, {dest.country}
        </option>
      ))}
    </select>
  );
};

export default AirbnbDestinationSelect;
