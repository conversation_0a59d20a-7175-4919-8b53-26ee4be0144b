import React, { useState, useEffect, useRef } from "react";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface DateRangePickerProps {
  startDate?: Date | null;
  endDate?: Date | null;
  onDateRangeChange?: (
    startDate: Date | null,
    endDate: Date | null,
    submitForm?: boolean
  ) => void;
  onChange?: (startDate: Date | null, endDate: Date | null) => void;
  onClose?: () => void;
  disableClickOutside?: boolean; // New prop to disable click-outside behavior
  onlyCloseOnBothDates?: boolean; // New prop to only close when both dates are selected
  lang?: Lang; // Add language prop for translations

  // These props are deprecated and will be removed in a future version
  initialStartDate?: Date;
  initialEndDate?: Date;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  startDate: propStartDate,
  endDate: propEndDate,
  onDateRangeChange,
  onChange,
  onClose, // Now used to close the picker when both dates are selected
  disableClickOutside = false, // Default to false for backward compatibility
  onlyCloseOnBothDates = false, // Default to false for backward compatibility
  lang = "en", // Default to English
  // Deprecated props - not used
  initialStartDate: __,
  initialEndDate: ___,
}) => {
  const t = useTranslations(lang);
  // Create a ref for the date picker container
  const datePickerRef = useRef<HTMLDivElement>(null);

  // Use current date as default if no dates provided
  const today = new Date();

  // Set hours, minutes, seconds, and milliseconds to 0 for clean date comparison
  today.setHours(0, 0, 0, 0);

  // Create clean date objects without time components
  const createCleanDate = (date: Date | undefined): Date => {
    if (!date) return new Date();
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
  };

  // Note: Default dates are now handled by the parent component

  // Initialize the current month view based on the start date or today
  const [currentDate, setCurrentDate] = useState<Date>(() => {
    if (propStartDate) return createCleanDate(propStartDate);
    return createCleanDate(today);
  });

  // Initialize selected dates with props if available
  const [selectedStartDate, setSelectedStartDate] = useState<Date | null>(
    () => {
      return propStartDate || null;
    }
  );
  const [selectedEndDate, setSelectedEndDate] = useState<Date | null>(() => {
    return propEndDate || null;
  });

  // If we already have both dates from props, selection is not in progress
  // If we have no dates at all, selection is also not in progress (we're starting fresh)
  // Only if we have a start date but no end date are we in progress
  const [selectionInProgress, setSelectionInProgress] = useState<boolean>(
    propStartDate !== null && propEndDate === null
  );
  const [hoverDate, setHoverDate] = useState<Date | null>(null);

  // Handle clicks outside the date picker
  useEffect(() => {
    // Skip adding click-outside handler if disabled
    if (disableClickOutside) {
      return;
    }

    const handleClickOutside = (event: MouseEvent) => {
      if (
        datePickerRef.current &&
        !datePickerRef.current.contains(event.target as Node)
      ) {
        // Only close if both dates are selected or no dates are selected
        // Don't close if we're in the middle of selecting a date range
        if (
          onClose &&
          (!selectedStartDate || (selectedStartDate && selectedEndDate))
        ) {
          onClose();
        }
      }
    };

    // Add event listener
    document.addEventListener("mousedown", handleClickOutside);

    // Clean up
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [onClose, selectedStartDate, selectedEndDate, disableClickOutside]);

  // Update internal state when props change
  useEffect(() => {
    // Sync with prop changes
    if (propStartDate) {
      setSelectedStartDate(propStartDate);
    }

    if (propEndDate) {
      setSelectedEndDate(propEndDate);
    }

    // If both dates are provided, selection is complete
    if (propStartDate && propEndDate) {
      setSelectionInProgress(false);
    }
  }, [propStartDate, propEndDate]);

  // Reset selection progress state when the component mounts or when dates change
  useEffect(() => {
    // If we have both start and end dates, selection is complete
    if (selectedStartDate && selectedEndDate) {
      setSelectionInProgress(false);
    }
  }, [selectedStartDate, selectedEndDate]);

  // Note: We use simpler date formatting in the UI

  // Navigate to previous month
  const goToPrevMonth = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() - 1);
    setCurrentDate(newDate);
  };

  // Navigate to next month
  const goToNextMonth = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();
    const newDate = new Date(currentDate);
    newDate.setMonth(newDate.getMonth() + 1);
    setCurrentDate(newDate);
  };

  // Handle date click
  const handleDateClick = (day: number) => {
    // Create a clean date object for the clicked date
    const clickedDate = new Date(
      currentDate.getFullYear(),
      currentDate.getMonth(),
      day
    );

    // Don't allow selecting dates in the past or today
    const tomorrow = new Date(today);
    tomorrow.setDate(tomorrow.getDate() + 1);

    if (clickedDate < tomorrow) {
      return;
    }

    // If we're starting a new selection or resetting after a complete selection
    if (!selectionInProgress || (selectedStartDate && selectedEndDate)) {
      // Update internal state
      setSelectedStartDate(clickedDate);
      setSelectedEndDate(null);
      setSelectionInProgress(true);

      // Notify parent components - but don't close the date picker
      if (onDateRangeChange) {
        // Pass false as the third parameter to indicate we don't want to submit the form
        onDateRangeChange(clickedDate, null, false);
      }

      if (onChange) {
        onChange(clickedDate, null);
      }
    }
    // If we're in the middle of selecting a range (start date is set)
    else if (selectionInProgress && selectedStartDate && !selectedEndDate) {
      // If clicked date is before start date, swap the dates
      if (clickedDate < selectedStartDate) {
        const newEndDate = selectedStartDate;
        const newStartDate = clickedDate;

        // Update internal state
        setSelectedStartDate(newStartDate);
        setSelectedEndDate(newEndDate);
        setSelectionInProgress(false); // Selection is complete

        // Notify parent components
        if (onDateRangeChange) {
          // Pass false as the third parameter to indicate we don't want to submit the form
          onDateRangeChange(newStartDate, newEndDate, false);
        }

        if (onChange) {
          onChange(newStartDate, newEndDate);
        }

        // Automatically close the date picker when both dates are selected
        if (
          onClose &&
          (!onlyCloseOnBothDates || (newStartDate && newEndDate))
        ) {
          // Use setTimeout to ensure the state updates are processed first
          setTimeout(() => {
            onClose();
          }, 100);
        }
      } else {
        const newEndDate = clickedDate;

        // Update internal state
        setSelectedEndDate(newEndDate);
        setSelectionInProgress(false); // Selection is complete

        // Notify parent components
        if (onDateRangeChange) {
          // Pass false as the third parameter to indicate we don't want to submit the form
          onDateRangeChange(selectedStartDate, newEndDate, false);
        }

        if (onChange) {
          onChange(selectedStartDate, newEndDate);
        }

        // Automatically close the date picker when both dates are selected
        if (
          onClose &&
          (!onlyCloseOnBothDates || (selectedStartDate && newEndDate))
        ) {
          // Use setTimeout to ensure the state updates are processed first
          setTimeout(() => {
            onClose();
          }, 100);
        }
      }
    }
  };

  // Handle mouse hover for date preview
  const handleDateHover = (day: number) => {
    if (selectionInProgress && selectedStartDate && !selectedEndDate) {
      const hoverDate = new Date(
        currentDate.getFullYear(),
        currentDate.getMonth(),
        day
      );
      setHoverDate(hoverDate);
    }
  };

  // Generate calendar days
  const renderCalendarDays = () => {
    const year = currentDate.getFullYear();
    const month = currentDate.getMonth();

    // Get first day of month and total days
    const firstDay = new Date(year, month, 1).getDay();
    const daysInMonth = new Date(year, month + 1, 0).getDate();

    // Empty cells for days before first day of month
    const emptyCells = Array.from({ length: firstDay }, (_, i) => (
      <div
        key={`empty-${i}`}
        className="h-10 w-10 flex items-center justify-center text-xs text-foreground/30"
      ></div>
    ));

    // Days of the month
    const days = Array.from({ length: daysInMonth }, (_, i) => {
      const day = i + 1;
      const currentDateCheck = new Date(year, month, day);
      currentDateCheck.setHours(0, 0, 0, 0);

      // Check if date is before tomorrow (past or today)
      const tomorrow = new Date(today);
      tomorrow.setDate(tomorrow.getDate() + 1);
      const isPastDate = currentDateCheck < tomorrow;

      const startDateCheck = selectedStartDate
        ? new Date(selectedStartDate)
        : null;
      const endDateCheck = selectedEndDate ? new Date(selectedEndDate) : null;
      const hoverDateCheck = hoverDate ? new Date(hoverDate) : null;

      if (startDateCheck) startDateCheck.setHours(0, 0, 0, 0);
      if (endDateCheck) endDateCheck.setHours(0, 0, 0, 0);
      if (hoverDateCheck) hoverDateCheck.setHours(0, 0, 0, 0);

      const isStartDate =
        startDateCheck &&
        currentDateCheck.getTime() === startDateCheck.getTime();
      const isEndDate =
        endDateCheck && currentDateCheck.getTime() === endDateCheck.getTime();

      // Check if date is in the selected range
      const isInRange =
        startDateCheck &&
        (endDateCheck || hoverDateCheck) &&
        currentDateCheck.getTime() >= startDateCheck.getTime() &&
        currentDateCheck.getTime() <=
          (endDateCheck || hoverDateCheck)!.getTime();

      // Base class for all days - larger size
      let className =
        "h-10 w-10 flex items-center justify-center text-sm font-karla transition-all duration-300 relative ";

      // Add specific styling based on date status
      if (isPastDate) {
        className += "text-foreground/30 cursor-not-allowed";
      } else if (isStartDate || isEndDate) {
        className += "bg-[#3566ab] text-white rounded-full z-10 font-medium";
      } else if (isInRange) {
        className +=
          "bg-[#3566ab]/20 text-[#3566ab] cursor-pointer font-medium";
      } else {
        className += "hover:bg-[#3566ab]/10 cursor-pointer";
      }

      return (
        <div
          key={`day-${day}`}
          className={className}
          onClick={() => !isPastDate && handleDateClick(day)}
          onMouseEnter={() => !isPastDate && handleDateHover(day)}
        >
          {day}
        </div>
      );
    });

    return [...emptyCells, ...days];
  };

  // Calculate number of nights
  const calculateNights = (): number => {
    if (selectedStartDate && selectedEndDate) {
      // Create date objects without time component to ensure accurate calculation
      const startDay = new Date(
        selectedStartDate.getFullYear(),
        selectedStartDate.getMonth(),
        selectedStartDate.getDate()
      );
      const endDay = new Date(
        selectedEndDate.getFullYear(),
        selectedEndDate.getMonth(),
        selectedEndDate.getDate()
      );

      // Calculate the difference in days
      const diffTime = endDay.getTime() - startDay.getTime();
      const diffDays = Math.round(diffTime / (1000 * 60 * 60 * 24));

      // Ensure we always return at least 1 night for valid date ranges
      return Math.max(1, diffDays);
    }
    return 0;
  };

  // Format date range for display
  const formatDateRange = (): string => {
    if (selectedStartDate && selectedEndDate) {
      // Ensure dates are displayed in chronological order
      const firstDate =
        selectedStartDate <= selectedEndDate
          ? selectedStartDate
          : selectedEndDate;
      const secondDate =
        selectedStartDate <= selectedEndDate
          ? selectedEndDate
          : selectedStartDate;

      return `${firstDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })} - ${secondDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })}`;
    } else if (selectedStartDate) {
      // Only check-in date is selected
      return `${selectedStartDate.toLocaleDateString("en-US", {
        month: "short",
        day: "numeric",
      })} - Select check-out`;
    }
    return "Select dates";
  };

  return (
    <div
      ref={datePickerRef}
      className="w-auto p-5 pb-6 bg-white rounded-lg min-w-[340px] mmt-date-picker-container"
    >
      {/* Selected Date Range Display */}
      <div className="mb-6 text-center">
        <div className="text-lg font-karla">
          <div className="flex items-center justify-center">
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="20"
              height="20"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="mr-2 text-[#3566ab]"
            >
              <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
              <line x1="16" y1="2" x2="16" y2="6"></line>
              <line x1="8" y1="2" x2="8" y2="6"></line>
              <line x1="3" y1="10" x2="21" y2="10"></line>
            </svg>
            <span className="text-[#3566ab] font-medium">
              {formatDateRange()}
            </span>
          </div>
        </div>
      </div>

      {/* Month Navigation */}
      <div className="flex justify-between items-center mb-4">
        <button
          type="button"
          className="p-2 hover:bg-[#3566ab]/10 rounded-full transition-colors duration-300 text-[#3566ab]"
          onClick={goToPrevMonth}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="15 18 9 12 15 6"></polyline>
          </svg>
        </button>
        <div className="text-base font-karla text-[#3566ab] font-medium">
          {currentDate.toLocaleString("default", {
            month: "long",
            year: "numeric",
          })}
        </div>
        <button
          type="button"
          className="p-2 hover:bg-[#3566ab]/10 rounded-full transition-colors duration-300 text-[#3566ab]"
          onClick={goToNextMonth}
        >
          <svg
            xmlns="http://www.w3.org/2000/svg"
            width="20"
            height="20"
            viewBox="0 0 24 24"
            fill="none"
            stroke="currentColor"
            strokeWidth="2"
            strokeLinecap="round"
            strokeLinejoin="round"
          >
            <polyline points="9 18 15 12 9 6"></polyline>
          </svg>
        </button>
      </div>

      {/* Calendar Grid */}
      <div className="grid grid-cols-7 gap-1 text-center">
        {/* Day Headers */}
        {[
          t("datePicker.days.su"),
          t("datePicker.days.mo"),
          t("datePicker.days.tu"),
          t("datePicker.days.we"),
          t("datePicker.days.th"),
          t("datePicker.days.fr"),
          t("datePicker.days.sa"),
        ].map((day, index) => (
          <div
            key={index}
            className="h-8 flex items-center justify-center text-xs font-karla tracking-wider uppercase text-[#3566ab]/70 font-medium"
          >
            {day}
          </div>
        ))}

        {/* Calendar Days */}
        {renderCalendarDays()}
      </div>

      {/* Instructions */}
      <div className="mt-4 text-center text-xs text-[#3566ab]/80 font-medium">
        {selectedStartDate && !selectedEndDate
          ? "Now select your check-out date"
          : !selectedStartDate
          ? "Select your check-in date"
          : `${calculateNights()} ${
              calculateNights() === 1 ? "night" : "nights"
            } selected`}
      </div>
    </div>
  );
};

export default DateRangePicker;
