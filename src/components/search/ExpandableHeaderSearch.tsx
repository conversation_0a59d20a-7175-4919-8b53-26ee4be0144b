import { useState, useEffect, useRef } from "react";
import { <PERSON>, Sparkles, Zap, HelpCircle } from "lucide-react";
import UnifiedSearchSelect from "./UnifiedSearchSelect";
import SearchSuggestions from "./SearchSuggestions";
import DateRangePicker from "./DateRangePicker";
import GuestSelector from "./GuestSelector";
import { formatDateForAPI } from "../../utils/dateUtils";

import "../../styles/expandable-header-search.css";
import "../../styles/airbnb-header-search.css";
import "../../styles/search-suggestions.css";
import { useIsMobile } from "../../hooks/use-mobile";
import type { Lang } from "../../i18n/ui";
import {
  SUPPORTED_LANGUAGES,
  isHomePage,
  useTranslations,
} from "../../i18n/utils";

interface ExpandableHeaderSearchProps {
  isExpandedView?: boolean;
}

// Helper function to get language from path
function getCurrentLanguageFromPath(path: string): Lang {
  const segments = path.split("/").filter(Boolean);

  if (
    segments.length > 0 &&
    SUPPORTED_LANGUAGES.includes(segments[0] as Lang)
  ) {
    return segments[0] as Lang;
  }
  return "en";
}

// Helper functions for localStorage operations
const SEARCH_STORAGE_KEYS = {
  AI_SEARCH: "lastAiSearchQuery",
  REGULAR_SEARCH: "lastRegularSearchQuery"
} as const;

const searchStorageUtils = {
  // Store AI search query
  storeAiSearchQuery: (query: string) => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        localStorage.setItem(SEARCH_STORAGE_KEYS.AI_SEARCH, query);
      } catch (error) {
        console.warn("Failed to store AI search query:", error);
      }
    }
  },

  // Store regular search query
  storeRegularSearchQuery: (destination: string, searchType: "destination" | "hotel", label?: string) => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        const searchData = {
          destination,
          searchType,
          label: label || destination, // Store the label if provided, otherwise use the destination ID
          timestamp: Date.now()
        };
        localStorage.setItem(SEARCH_STORAGE_KEYS.REGULAR_SEARCH, JSON.stringify(searchData));
      } catch (error) {
        console.warn("Failed to store regular search query:", error);
      }
    }
  },

  // Retrieve AI search query
  retrieveAiSearchQuery: (): string | null => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        return localStorage.getItem(SEARCH_STORAGE_KEYS.AI_SEARCH);
      } catch (error) {
        console.warn("Failed to retrieve AI search query:", error);
      }
    }
    return null;
  },

  // Retrieve regular search query
  retrieveRegularSearchQuery: (): { destination: string; searchType: "destination" | "hotel"; label?: string } | null => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        const storedData = localStorage.getItem(SEARCH_STORAGE_KEYS.REGULAR_SEARCH);
        if (storedData) {
          const searchData = JSON.parse(storedData);
          // Only return if the data is recent (within 24 hours)
          const isRecent = Date.now() - searchData.timestamp < 24 * 60 * 60 * 1000;
          if (isRecent && searchData.destination) {
            return {
              destination: searchData.destination,
              searchType: searchData.searchType || "destination",
              label: searchData.label
            };
          }
        }
      } catch (error) {
        console.warn("Failed to retrieve regular search query:", error);
      }
    }
    return null;
  },

  // Clear all stored search queries
  clearAllSearchQueries: () => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        localStorage.removeItem(SEARCH_STORAGE_KEYS.AI_SEARCH);
        localStorage.removeItem(SEARCH_STORAGE_KEYS.REGULAR_SEARCH);
      } catch (error) {
        console.warn("Failed to clear search queries:", error);
      }
    }
  }
};

const ExpandableHeaderSearch: React.FC<ExpandableHeaderSearchProps> = ({
  isExpandedView,
}) => {
  const [isExpanded, setIsExpanded] = useState(isExpandedView || false);
  const [isAiSearch, setIsAiSearch] = useState(true); // Default to AI search
  // Initialize destination with localStorage value to avoid empty state flash
  const [destination, setDestination] = useState(() => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        const storedData = searchStorageUtils.retrieveRegularSearchQuery();
        return storedData?.destination || "";
      } catch (error) {
        console.warn("Failed to retrieve stored search data:", error);
        return "";
      }
    }
    return "";
  });
  // Initialize destinationLabel with localStorage value to avoid "Loading destination..." flash
  const [destinationLabel, setDestinationLabel] = useState(() => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        const storedData = searchStorageUtils.retrieveRegularSearchQuery();
        return storedData?.label || "";
      } catch (error) {
        console.warn("Failed to retrieve stored search data:", error);
        return "";
      }
    }
    return "";
  });
  // Initialize searchType with localStorage value
  const [searchType, setSearchType] = useState<"destination" | "hotel">(() => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      try {
        const storedData = searchStorageUtils.retrieveRegularSearchQuery();
        return storedData?.searchType || "destination";
      } catch (error) {
        console.warn("Failed to retrieve stored search data:", error);
        return "destination";
      }
    }
    return "destination";
  });

  // Debug destinationLabel changes
  useEffect(() => {
    console.log("🔍 destinationLabel state changed:", destinationLabel);
  }, [destinationLabel]);
  const [conversation, setConversation] = useState("");
  const searchContainerRef = useRef<HTMLDivElement>(null);
  const headerWrapperRef = useRef<HTMLDivElement | null>(null);
  const aiInputRef = useRef<HTMLInputElement>(null);
  const [currentPath, setCurrentPath] = useState<string>("");
  const [isSearchPage, setIsSearchPage] = useState(false);
  const isMobile = useIsMobile();

  // Get current language for translations
  const currentLang = getCurrentLanguageFromPath(currentPath);
  const t = useTranslations(currentLang);

  // Date picker state - don't prepopulate for header/hero usage
  const [checkInDate, setCheckInDate] = useState<Date | null>(null);
  const [checkOutDate, setCheckOutDate] = useState<Date | null>(null);
  const [isDatePickerOpen, setIsDatePickerOpen] = useState(false);

  // Guest selector state - don't prepopulate for header/hero usage
  const [guests, setGuests] = useState({ adults: 0, children: 0, infants: 0 });
  const [isGuestSelectorOpen, setIsGuestSelectorOpen] = useState(false);
  const [isPetsAllowed, setIsPetsAllowed] = useState(false);

  // State to track if component is in header or hero section
  const [isInHeader, setIsInHeader] = useState(false);
  const [isInHero, setIsInHero] = useState(false);

  // Function to open the AI help modal
  const openAIHelpModal = () => {
    if (typeof window !== "undefined") {
      // Ensure the function exists before calling it
      if ((window as any).openAIHelpModal) {
        (window as any).openAIHelpModal();
      } else {
        console.warn("AI Help Modal function not available");
      }
    }
  };

  // Handle AI input focus
  const handleAIInputFocus = () => {
    // Focus handling can be added here if needed
  };

  // Handle AI input blur
  const handleAIInputBlur = () => {
    // Blur handling can be added here if needed
  };

  // Handle AI input change
  const handleAIInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setConversation(value);
  };

  // Handle suggestion selection with multi-select support
  const handleSuggestionSelect = (suggestion: string) => {
    setConversation(suggestion);
  };

  // Find the header wrapper element and set current path
  useEffect(() => {
    headerWrapperRef.current = document.querySelector(".header-wrapper");

    // Get current path
    if (typeof window !== "undefined") {
      const path = window.location.pathname;
      setCurrentPath(path);

      // Check if we're on the search page
      const isOnSearchPage = path === "/search";
      setIsSearchPage(isOnSearchPage);

      console.log("🔍 ExpandableHeaderSearch - Page initialization:", {
        path,
        isOnSearchPage,
        currentDestination: destination,
        currentConversation: conversation
      });

      // Check if this component is inside the header
      if (searchContainerRef.current) {
        const isInHeaderSection =
          searchContainerRef.current.closest(".header-wrapper") !== null;
        setIsInHeader(isInHeaderSection);
      }

      // Set search mode based on current page
      if (path === "/ai-search") {
        setIsAiSearch(true);

        // Try to restore AI search query from localStorage if not in URL
        const searchParams = new URLSearchParams(window.location.search);
        const urlQuery = searchParams.get("query") || searchParams.get("user_message");

        if (!urlQuery) {
          // No query in URL, try to restore from localStorage with a small delay
          setTimeout(() => {
            const storedQuery = searchStorageUtils.retrieveAiSearchQuery();
            if (storedQuery) {
              console.log("🔍 ExpandableHeaderSearch - Restoring AI search query from localStorage:", storedQuery);
              setConversation(storedQuery);
            }
          }, 100);
        }
      } else if (isOnSearchPage) {
        // Get search parameters from URL
        const searchParams = new URLSearchParams(window.location.search);
        console.log(
          "🔍 ExpandableHeaderSearch - Reading URL params on search page:",
          {
            url: window.location.href,
            searchParams: Object.fromEntries(searchParams.entries()),
          }
        );

        // Only set to false if explicitly specified in URL
        const aiSearchParam = searchParams.get("ai_search");
        if (aiSearchParam === "false") {
          setIsAiSearch(false);
        }

        // Set destination from URL if available
        const destinationId = searchParams.get("destination_id");
        const hotelId = searchParams.get("hotel_id");

        if (destinationId) {
          console.log(
            "🔍 ExpandableHeaderSearch - Setting destination from URL:",
            destinationId
          );
          setDestination(destinationId);
          setSearchType("destination");
        } else if (hotelId) {
          console.log(
            "🔍 ExpandableHeaderSearch - Setting hotel from URL:",
            hotelId
          );
          setDestination(hotelId);
          setSearchType("hotel");
        } else {
          // No destination in URL, try to restore from localStorage with a small delay
          setTimeout(() => {
            const storedSearchData = searchStorageUtils.retrieveRegularSearchQuery();
            if (storedSearchData) {
              console.log("🔍 ExpandableHeaderSearch - Restoring regular search query from localStorage:", storedSearchData);
              console.log("🔍 Setting destination to:", storedSearchData.destination);
              console.log("🔍 Setting searchType to:", storedSearchData.searchType);
              console.log("🔍 Setting destinationLabel to:", storedSearchData.label);
              setDestination(storedSearchData.destination);
              setSearchType(storedSearchData.searchType);
              setDestinationLabel(storedSearchData.label || "");
            } else {
              console.log("🔍 No stored search data found in localStorage");
            }
          }, 100);
        }

        // Only prefill dates and guests if NOT in header or hero sections
        // This allows prefilling in hotel detail pages but not in main navigation
        const isInHeaderOrHero =
          searchContainerRef.current?.closest(".header-wrapper") !== null ||
          searchContainerRef.current?.closest(".hero-content") !== null ||
          searchContainerRef.current?.closest(".hero-section") !== null;

        console.log("🔍 ExpandableHeaderSearch - Header/Hero check:", {
          isInHeaderOrHero,
          hasSearchContainer: !!searchContainerRef.current,
          headerWrapper:
            !!searchContainerRef.current?.closest(".header-wrapper"),
          heroContent: !!searchContainerRef.current?.closest(".hero-content"),
          heroSection: !!searchContainerRef.current?.closest(".hero-section"),
        });

        if (!isInHeaderOrHero) {
          console.log(
            "🔍 ExpandableHeaderSearch - Not in header/hero, prefilling dates and guests"
          );
        } else {
          console.log(
            "🔍 ExpandableHeaderSearch - In header/hero, skipping dates and guests prefill"
          );
        }

        // Always prefill dates and guests on search page regardless of location
        // Set dates from URL if available
        const checkIn = searchParams.get("check_in");
        const checkOut = searchParams.get("check_out");
        if (checkIn) {
          console.log(
            "🔍 ExpandableHeaderSearch - Setting check-in date:",
            checkIn
          );
          setCheckInDate(new Date(checkIn));
        }
        if (checkOut) {
          console.log(
            "🔍 ExpandableHeaderSearch - Setting check-out date:",
            checkOut
          );
          setCheckOutDate(new Date(checkOut));
        }

        // Set guests from URL if available
        const adultsParam = searchParams.get("adults");
        const childrenParam = searchParams.get("children");
        const infantsParam = searchParams.get("infants");
        const petsParam = searchParams.get("is_pets_allowed");

        if (adultsParam || childrenParam || infantsParam) {
          console.log("🔍 ExpandableHeaderSearch - Setting guests:", {
            adultsParam,
            childrenParam,
            infantsParam,
          });
          setGuests({
            adults: adultsParam ? parseInt(adultsParam) : 1,
            children: childrenParam ? parseInt(childrenParam) : 0,
            infants: infantsParam ? parseInt(infantsParam) : 0,
          });
        }

        if (petsParam) {
          console.log(
            "🔍 ExpandableHeaderSearch - Setting pets allowed:",
            petsParam
          );
          setIsPetsAllowed(petsParam === "true");
        }

        // Auto-expand the search on search page
        setIsExpanded(true);

        // Add expanded class to header for search page
        if (headerWrapperRef.current) {
          headerWrapperRef.current.classList.add("header-expanded");

          // Also add expanded-header class to primary header for additional styling
          const primaryHeader = document.querySelector(".primary-header");
          if (primaryHeader) {
            primaryHeader.classList.add("expanded-header");
          }
        }
      } else {
        // Check URL parameters for ai_search
        const searchParams = new URLSearchParams(window.location.search);
        const aiSearchParam = searchParams.get("ai_search");
        // Only set to false if explicitly specified
        if (aiSearchParam === "false") {
          setIsAiSearch(false);
        }

        // For non-search pages, try to restore previous search queries from localStorage
        // This helps maintain search context when users navigate back to search from other pages
        if (isInHeader) {
          // Try to restore search queries with a small delay to ensure components are ready
          setTimeout(() => {
            if (isAiSearch) {
              const storedAiQuery = searchStorageUtils.retrieveAiSearchQuery();
              if (storedAiQuery && !conversation) {
                console.log("🔍 Restoring AI query in header:", storedAiQuery);
                setConversation(storedAiQuery);
              }
            } else {
              // Try to restore regular search query if in regular mode
              const storedSearchData = searchStorageUtils.retrieveRegularSearchQuery();
              if (storedSearchData && !destination) {
                console.log("🔍 Restoring regular search in header:", storedSearchData);
                setDestination(storedSearchData.destination);
                setSearchType(storedSearchData.searchType);
                setDestinationLabel(storedSearchData.label || "");
              }
            }
          }, 100);
        }
      }
    }
  }, []);

  // Additional useEffect to restore localStorage data after component is fully mounted
  // This ensures that the UnifiedSearchSelect component is ready to receive the value
  useEffect(() => {
    // Only run this after the component has been mounted and path is set
    if (!currentPath) return;

    const restoreFromLocalStorage = () => {

      // For AI search mode
      if (isAiSearch && !conversation) {
        const storedAiQuery = searchStorageUtils.retrieveAiSearchQuery();
        if (storedAiQuery) {
          console.log("🔍 Restoring AI search query:", storedAiQuery);
          setConversation(storedAiQuery);
        }
      }

      // For regular search mode
      if (!isAiSearch && !destination) {
        const storedSearchData = searchStorageUtils.retrieveRegularSearchQuery();
        if (storedSearchData) {
          console.log("🔍 Restoring regular search query:", storedSearchData);
          setDestination(storedSearchData.destination);
          setSearchType(storedSearchData.searchType);
          setDestinationLabel(storedSearchData.label || "");
        }
      }
    };

    // Use multiple timeouts to ensure restoration works even if components load slowly
    const timeoutId1 = setTimeout(restoreFromLocalStorage, 100);
    const timeoutId2 = setTimeout(restoreFromLocalStorage, 500);
    const timeoutId3 = setTimeout(restoreFromLocalStorage, 1000);

    return () => {
      clearTimeout(timeoutId1);
      clearTimeout(timeoutId2);
      clearTimeout(timeoutId3);
    };
  }, [currentPath, isAiSearch, conversation, destination]);

  // Check if component is in header or hero section
  useEffect(() => {
    // Function to check if component is in header or hero section
    const checkComponentLocation = () => {
      if (searchContainerRef.current) {
        const isInHeaderSection =
          searchContainerRef.current.closest(".header-wrapper") !== null;
        setIsInHeader(isInHeaderSection);

        const isInHeroSection =
          searchContainerRef.current.closest(".hero-content") !== null ||
          searchContainerRef.current.closest(".hero-section") !== null;
        setIsInHero(isInHeroSection);
      }
    };

    // Check on mount
    checkComponentLocation();

    // Create a MutationObserver to detect DOM changes
    const observer = new MutationObserver(checkComponentLocation);

    // Start observing the document
    observer.observe(document.body, {
      childList: true,
      subtree: true,
    });

    // Cleanup
    return () => {
      observer.disconnect();
    };
  }, []);

  // Handle click outside to collapse the search
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchContainerRef.current &&
        !searchContainerRef.current.contains(event.target as Node) &&
        isExpanded &&
        !isExpandedView // Only collapse if isExpandedView is not true
      ) {
        resetSearchState();
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, [isExpanded, isExpandedView]);

  // Function to reset search state
  const resetSearchState = () => {
    // Only set isExpanded to false if isExpandedView is not true and not on search page
    if (!isExpandedView && !isSearchPage) {
      setIsExpanded(false);

      // Reset form values only if not on search page and in header/hero sections
      if (!isSearchPage) {
        setDestination("");
        setDestinationLabel("");
        setConversation("");

        // Clear stored search queries from localStorage when resetting
        searchStorageUtils.clearAllSearchQueries();

        // Reset dates and guests in header/hero sections
        const isInHeaderOrHero =
          searchContainerRef.current?.closest(".header-wrapper") !== null ||
          searchContainerRef.current?.closest(".hero-content") !== null ||
          searchContainerRef.current?.closest(".hero-section") !== null;

        if (isInHeaderOrHero) {
          setCheckInDate(null);
          setCheckOutDate(null);
          setGuests({ adults: 1, children: 0, infants: 0 });
          setIsPetsAllowed(false);
        }
      }

      // Remove expanded class from header
      if (headerWrapperRef.current) {
        headerWrapperRef.current.classList.remove("header-expanded");

        // Also remove expanded-header class from primary header
        const primaryHeader = document.querySelector(".primary-header");
        if (primaryHeader) {
          primaryHeader.classList.remove("expanded-header");
        }
      }
    }
  };

  // Listen for scroll events to reset search state
  useEffect(() => {
    const handleScroll = () => {
      // Only collapse on scroll if isExpandedView is not true, not on search page, and not on mobile
      if (isExpanded && !isExpandedView && !isSearchPage && !isMobile) {
        resetSearchState();
      }
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, [isExpanded, isExpandedView, isSearchPage, isMobile]);

  // Function to expand the search
  const expandSearch = (e: React.MouseEvent) => {
    e.preventDefault();
    e.stopPropagation();

    setIsExpanded(true);

    // Add expanded class to header
    if (headerWrapperRef.current) {
      headerWrapperRef.current.classList.add("header-expanded");

      // Also add expanded-header class to primary header for additional styling
      const primaryHeader = document.querySelector(".primary-header");
      if (primaryHeader) {
        primaryHeader.classList.add("expanded-header");
      }
    }
  };

  // Function to handle search submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault();

    // Build the search URL
    const params = new URLSearchParams();

    if (isAiSearch) {
      if (conversation.trim()) {
        // Store AI search query in localStorage for persistence
        console.log("🔍 Storing AI search query in localStorage:", conversation.trim());
        searchStorageUtils.storeAiSearchQuery(conversation.trim());

        params.set("query", conversation);
        params.set("user_message", conversation);
        params.set("ai_search", "true");
        window.location.href = `/ai-search?${params.toString()}`;
      }
    } else {
      if (destination) {
        // Get the current label from localStorage if available, or use destination ID as fallback
        const currentStoredData = searchStorageUtils.retrieveRegularSearchQuery();
        const label = currentStoredData?.label || destination;

        // Store regular search destination in localStorage for persistence
        console.log("🔍 Storing regular search query in localStorage:", { destination, searchType, label });
        searchStorageUtils.storeRegularSearchQuery(destination, searchType, label);

        if (searchType === "destination") {
          params.set("destination_id", destination);
        } else if (searchType === "hotel") {
          params.set("hotel_id", destination);
        }
      }

      // Add date parameters if available
      if (checkInDate) {
        params.set("check_in", formatDateForAPI(checkInDate));
      }

      if (checkOutDate) {
        params.set("check_out", formatDateForAPI(checkOutDate));
      }

      // Add guest parameters only if guests have been selected
      if (guests.adults > 0 || guests.children > 0 || guests.infants > 0) {
        params.set("adults", guests.adults.toString());
        params.set("children", guests.children.toString());
        params.set("infants", guests.infants.toString());
      }

      // Add pets parameter if enabled
      if (isPetsAllowed) {
        params.set("is_pets_allowed", "true");
      }

      params.set("ai_search", "false");

      // If searching for a specific hotel, redirect to hotel details page
      if (searchType === "hotel" && destination) {
        window.location.href = `/stays/${destination}?${params.toString()}`;
      } else {
        window.location.href = `/search?${params.toString()}`;
      }
    }
  };

  // Handle date range changes
  const handleDateRangeChange = (
    startDate: Date | null,
    endDate: Date | null,
    _submitForm?: boolean // Unused parameter, but kept for compatibility
  ) => {
    setCheckInDate(startDate);
    setCheckOutDate(endDate);

    // Only close the date picker if both dates are selected
    if (startDate && endDate) {
      setIsDatePickerOpen(false);
      // Don't submit the form automatically - wait for search button click
    }
  };

  // Handle guest changes - automatically update the state
  const handleGuestChange = (
    newGuests: {
      adults: number;
      children: number;
      infants: number;
    },
    _submitForm?: boolean, // Unused parameter, but kept for compatibility
    newIsPetsAllowed?: boolean
  ) => {
    setGuests(newGuests);
    if (newIsPetsAllowed !== undefined) {
      setIsPetsAllowed(newIsPetsAllowed);
    }
    // Don't close the selector automatically - let the user close it by clicking elsewhere
    // Don't submit the form - wait for search button click
  };

  // Handle unified search selection
  const handleUnifiedSearchChange = (
    value: string,
    type: "destination" | "hotel",
    label?: string
  ) => {
    setDestination(value);
    setSearchType(type);
    setDestinationLabel(label || "");

    // Store the label for immediate display on page refresh
    if (label) {
      console.log("🔍 Storing destination label for immediate display:", label);
      // Store immediately when selection is made, not just when search is performed
      searchStorageUtils.storeRegularSearchQuery(value, type, label);
    }
  };

  // Helper function to dispatch search mode change event
  const dispatchSearchModeEvent = (isAiMode: boolean) => {
    // Dispatch event for other components that might need to know
    if (typeof document !== "undefined") {
      const event = new CustomEvent("searchModeChanged", {
        detail: { isAiSearch: isAiMode },
      });
      document.dispatchEvent(event);
    }
  };

  // Helper function to format date display for collapsed view
  const formatDateDisplay = () => {
    if (!checkInDate || !checkOutDate) {
      return t("search.collapsed.anytime");
    }
    const checkIn = checkInDate.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    const checkOut = checkOutDate.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
    return `${checkIn} - ${checkOut}`;
  };

  // Helper function to format guest display for collapsed view
  const formatGuestDisplay = () => {
    const totalGuests = guests.adults + guests.children;
    if (totalGuests === 0) {
      return t("search.collapsed.anyone");
    }
    return `${totalGuests} guest${totalGuests !== 1 ? "s" : ""}${
      guests.infants > 0
        ? `, ${guests.infants} infant${guests.infants !== 1 ? "s" : ""}`
        : ""
    }`;
  };

  console.log({destination});

  // Debug: Log current localStorage state and destination changes
  useEffect(() => {
    if (typeof window !== "undefined" && typeof localStorage !== "undefined") {
      console.log("🔍 Current localStorage state:", {
        aiSearch: localStorage.getItem("lastAiSearchQuery"),
        regularSearch: localStorage.getItem("lastRegularSearchQuery")
      });
    }
  }, [destination, destinationLabel, conversation, searchType, isSearchPage, currentPath]);

  return (
    <div
      className="expandable-search-wrapper visible"
      data-expandable-search
      ref={searchContainerRef}
    >
      {!isExpanded ? (
        // Collapsed state - simple search bar
        <div className="collapsed-search-container" onClick={expandSearch}>
          <div className="collapsed-search-inner">
            {isMobile ? (
              // Simplified view for mobile
              <div className="search-tab">
                {t("search.collapsed.searchDestinations")}
              </div>
            ) : (
              // Full view for desktop
              <>
                <div className="search-tab">
                  {destination
                    ? t("search.collapsed.selected")
                    : t("search.collapsed.anywhere")}
                </div>
                <div className="search-divider"></div>
                <div className="search-tab">{formatDateDisplay()}</div>
                <div className="search-divider"></div>
                <div className="search-tab">{formatGuestDisplay()}</div>
              </>
            )}
            <button
              type="button"
              className="search-button-small"
              onClick={(e) => {
                e.stopPropagation();
                expandSearch(e);
              }}
            >
              <Search size={18} className="search-icon" />
            </button>
          </div>
        </div>
      ) : (
        // Expanded state - full search interface
        <div className="search-wrapper expanded-search-wrapper">
          <div className="search-toggle-container">
            <div className="search-toggle-wrapper">
              <button
                type="button"
                className={`toggle-option ai-option ${
                  isAiSearch ? "active" : ""
                }`}
                onClick={() => {
                  // On home pages (including translated), don't navigate, just toggle the mode
                  if (isHomePage(currentPath)) {
                    setIsAiSearch(true);
                    dispatchSearchModeEvent(true);
                  }
                  // On AI search page, just toggle the mode
                  else if (currentPath === "/ai-search") {
                    setIsAiSearch(true);
                    dispatchSearchModeEvent(true);
                  }
                  // On other pages, navigate to ai-search
                  else {
                    window.location.href = "/ai-search";
                  }
                }}
              >
                <Sparkles size={14} className="sparkle-icon" />
                {t("search.toggle.perfectPisteAI")}
              </button>
              <button
                type="button"
                className={`toggle-option ${!isAiSearch ? "active" : ""}`}
                onClick={() => {
                  // If on ai-search page, navigate to search page
                  if (currentPath === "/ai-search") {
                    window.location.href = "/search?ai_search=false";
                  } else {
                    // Otherwise just toggle the mode
                    setIsAiSearch(false);
                    dispatchSearchModeEvent(false);
                  }
                }}
              >
                {t("search.toggle.regularSearch")}
              </button>
              <div
                className={`toggle-slider ${isAiSearch ? "ai-active" : ""}`}
              ></div>
            </div>
          </div>

          {isAiSearch ? (
            // AI Search Mode - Show message on AI Search page, input field on other pages
            currentPath === "/ai-search" ? (
              // Message for AI Search page
              <div className="airbnb-header-search ai-mode-prompt">
                <div className="ai-prompt-container">
                  <Zap size={20} className="ai-icon" />
                  <div className="ai-prompt-text">
                    <span className="ai-prompt-title">
                      {t("search.ai.conciergeActive")}
                    </span>
                    <span className="ai-prompt-subtitle">
                      {t("search.ai.chatPrompt")}
                    </span>
                  </div>
                </div>
              </div>
            ) : (
              // Input field for other pages
              <div>
                <div>
                  <form
                    className="airbnb-header-search"
                    onSubmit={handleSearch}
                  >
                    <div className="search-section ai-search-input-section">
                      <input
                        ref={aiInputRef}
                        type="text"
                        value={conversation}
                        onChange={handleAIInputChange}
                        onFocus={handleAIInputFocus}
                        onBlur={handleAIInputBlur}
                        placeholder={t("search.placeholder.aiSearch")}
                        className="ai-search-input"
                      />
                    </div>
                    <div className="search-buttons-container">
                      {/* Only show Help button in hero section, not in header */}
                      {!isInHeader && (
                        <button
                          type="button"
                          className="info-button"
                          title="Learn how to use AI search"
                          aria-label="Learn how to use AI search"
                          onClick={(e) => {
                            e.preventDefault();
                            openAIHelpModal();
                          }}
                        >
                          <HelpCircle size={16} color="#3566ab" />
                          <span className="info-text">
                            {t("search.ai.helpLabel")}
                          </span>
                        </button>
                      )}
                      <button
                        type="submit"
                        className="flex items-center justify-center bg-[#3566ab] text-white h-[42px] w-[42px] min-w-[42px] rounded-full border-none mr-2 cursor-pointer transition-all duration-200 shadow-md hover:scale-105 hover:bg-[#2a5089] hover:shadow-lg active:scale-[0.98]"
                        disabled={!conversation.trim()}
                      >
                        <Search size={20} color="white" />
                      </button>
                    </div>
                  </form>
                </div>

                {/* WhatsApp Meta AI style suggestions - only show on home page and not in header */}
                {isHomePage(currentPath) && !isInHeader && (
                  <div className="suggestions-wrapper hero-only-suggestions">
                    <SearchSuggestions
                      onSelectSuggestion={handleSuggestionSelect}
                      allowMultiSelect={true}
                      lang={getCurrentLanguageFromPath(currentPath)}
                    />
                  </div>
                )}
              </div>
            )
          ) : (
            // Regular Search Form
            <form
              className="flex items-center bg-white rounded-[28px] border border-[rgba(53,102,171,0.15)] shadow-sm h-14 w-full max-w-[848px] transition-all duration-300 hover:shadow-md hover:border-[rgba(53,102,171,0.3)]"
              onSubmit={handleSearch}
            >
              <div className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]">
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  {t("search.form.where")}
                </div>
                <UnifiedSearchSelect
                  key={`${destination}-${destinationLabel}`}
                  value={destination}
                  onChange={handleUnifiedSearchChange}
                  placeholder={t("search.placeholder.where")}
                  openAbove={isInHero}
                  lang={currentLang}
                  storedLabel={destinationLabel}
                />
              </div>

              <div
                className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]"
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Stop event propagation
                  setIsDatePickerOpen(!isDatePickerOpen);
                }}
              >
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  {t("search.form.checkIn")}
                </div>
                {!isMobile && (
                  <div className="text-sm text-[#3566ab] font-medium font-karla text-center w-full">
                    {checkInDate
                      ? checkInDate.toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      : t("search.form.addDates")}
                  </div>
                )}
              </div>

              <div
                className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]"
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Stop event propagation
                  setIsDatePickerOpen(!isDatePickerOpen);
                }}
              >
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  {t("search.form.checkOut")}
                </div>
                {!isMobile && (
                  <div className="text-sm text-[#3566ab] font-medium font-karla text-center w-full">
                    {checkOutDate
                      ? checkOutDate.toLocaleDateString("en-US", {
                          month: "short",
                          day: "numeric",
                        })
                      : t("search.form.addDates")}
                  </div>
                )}
              </div>

              <div
                className="flex flex-col justify-center items-center px-4 flex-1 min-w-0 h-full relative box-border transition-all duration-200 hover:bg-[rgba(53,102,171,0.04)] cursor-pointer after:content-[''] after:absolute after:right-0 after:top-1/2 after:-translate-y-1/2 after:h-5 after:w-px after:bg-[rgba(53,102,171,0.15)]"
                onClick={(e) => {
                  e.preventDefault(); // Prevent form submission
                  e.stopPropagation(); // Stop event propagation
                  setIsGuestSelectorOpen(!isGuestSelectorOpen);
                }}
              >
                <div className="text-xs font-semibold text-[#3566ab] mb-0.5 font-karla text-center w-full">
                  {t("search.form.who")}
                </div>
                {!isMobile && (
                  <div className="text-sm text-[#3566ab] font-medium font-karla text-center w-full">
                    {guests.adults + guests.children > 0
                      ? `${guests.adults + guests.children} guest${
                          guests.adults + guests.children !== 1 ? "s" : ""
                        }${
                          guests.infants > 0
                            ? `, ${guests.infants} infant${
                                guests.infants !== 1 ? "s" : ""
                              }`
                            : ""
                        }`
                      : t("search.form.addGuests")}
                  </div>
                )}
              </div>

              <button
                type="submit"
                className="flex items-center justify-center bg-[#3566ab] text-white h-[42px] w-[42px] min-w-[42px] rounded-full border-none mr-2 cursor-pointer transition-all duration-200 shadow-md hover:scale-105 hover:bg-[#2a5089] hover:shadow-lg active:scale-[0.98]"
                style={{ backgroundColor: "#3566ab" }}
              >
                <Search size={20} color="white" />
              </button>

              {/* Date Range Picker Popover */}
              {isDatePickerOpen && (
                <>
                  {/* Mobile backdrop */}
                  {isMobile && (
                    <div
                      className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
                      onClick={() => setIsDatePickerOpen(false)}
                    />
                  )}
                  <div
                    className={`date-picker-popover ${
                      isInHero ? "date-picker-popover-top" : ""
                    }`}
                    onClick={(e) => {
                      e.preventDefault();
                      e.stopPropagation();
                    }}
                  >
                    <DateRangePicker
                      startDate={checkInDate}
                      endDate={checkOutDate}
                      onDateRangeChange={handleDateRangeChange}
                      onChange={(startDate, endDate) =>
                        handleDateRangeChange(startDate, endDate)
                      }
                      onClose={() => setIsDatePickerOpen(false)}
                      lang={currentLang}
                    />
                  </div>
                </>
              )}

              {/* Guest Selector Popover */}
              <div className="guest-selector-container">
                {/* Mobile backdrop for guest selector */}
                {isMobile && isGuestSelectorOpen && (
                  <div
                    className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
                    onClick={() => setIsGuestSelectorOpen(false)}
                  />
                )}
                <GuestSelector
                  adults={guests.adults}
                  children={guests.children}
                  infants={guests.infants}
                  isPetsAllowed={isPetsAllowed}
                  showPetsOption={true}
                  onGuestChange={handleGuestChange}
                  maxAdults={16}
                  maxChildren={16}
                  maxInfants={16}
                  maxOccupancy={48}
                  isOpen={isGuestSelectorOpen}
                  onClose={() => setIsGuestSelectorOpen(false)}
                  openAbove={isInHero}
                  isMobileModal={false}
                  lang={currentLang}
                />
              </div>
            </form>
          )}
        </div>
      )}
    </div>
  );
};

export default ExpandableHeaderSearch;
