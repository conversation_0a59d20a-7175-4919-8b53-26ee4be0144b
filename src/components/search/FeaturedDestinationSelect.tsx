import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, MapPin, Search } from "lucide-react";
import { useIsMobile } from "../../hooks/use-mobile";
import {
  fetchDestinationsForSearch,
  fetchDestinations,
  type Destination
} from "../../utils/store/destinations";

interface FeaturedDestinationSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  openAbove?: boolean;
}

const FeaturedDestinationSelect: React.FC<FeaturedDestinationSelectProps> = ({
  value,
  onChange,
  placeholder = "Select a destination",
  openAbove = false,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();

  // Filter featured destinations
  const featuredDestinations = destinations.filter((dest) => dest.is_featured);

  // Filter all destinations based on search query
  const filteredDestinations = destinations.filter(
    (dest) =>
      dest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
      dest.country.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Find the selected destination name from all destinations
  const selectedDestination = destinations.find((dest) => dest.id === value);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        setSearchQuery(""); // Reset search query when closing dropdown
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  return (
    <div className="relative w-full font-karla" ref={dropdownRef}>
      <div
        className={`flex items-center justify-center gap-2 py-1 cursor-pointer rounded-md transition-all duration-200 relative ${
          isOpen ? "text-[#3566ab]" : ""
        } ${value ? "text-[#3566ab]" : ""}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {!isMobile && (
          <div className="flex items-center justify-center text-[#3566ab] opacity-80">
            <MapPin size={16} />
          </div>
        )}
        <div className="flex-1 whitespace-nowrap overflow-hidden text-ellipsis text-center">
          {!isMobile && (
            <>
              {selectedDestination ? (
                <span className="text-[#3566ab] font-medium text-sm">
                  {selectedDestination.name}, {selectedDestination.country}
                </span>
              ) : (
                <span className="text-gray-500 text-sm">{placeholder}</span>
              )}
            </>
          )}
        </div>
        {!isMobile && (
          <div className="flex items-center justify-center text-gray-500">
            <ChevronDown
              size={16}
              className={`transition-transform duration-200 ${
                isOpen ? "rotate-180" : ""
              }`}
            />
          </div>
        )}
      </div>

      {isOpen && (
        <>
          {/* Mobile backdrop */}
          {isMobile && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
              onClick={() => setIsOpen(false)}
            />
          )}
          <div
            className={`${
              isMobile
                ? "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-[350px] z-[9999]"
                : `absolute ${
                    openAbove
                      ? "bottom-[calc(100%+8px)] mb-4 "
                      : "top-[calc(100%+8px)]"
                  } -left-3 w-[calc(100%+24px)] z-[1000]`
            } bg-white rounded-2xl shadow-lg max-h-[400px] overflow-y-auto border border-[rgba(53,102,171,0.1)]`}
            style={
              isMobile ? { boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" } : {}
            }
          >
            {/* Header with search input and close button */}
            <div className="sticky top-0 bg-white border-b border-[rgba(53,102,171,0.1)]">
              {/* Mobile header with close button */}
              {isMobile && (
                <div className="flex justify-between items-center p-2.5 pb-0">
                  <h3 className="text-sm font-semibold text-[#3566ab]">
                    Select Destination
                  </h3>
                  <button
                    type="button"
                    className="text-gray-500 hover:text-gray-700 p-1"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
              )}
              {/* Search input */}
              <div className="p-2.5">
                <div className="relative">
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder="Search destinations..."
                    className={`w-full pl-9 pr-3 border border-[rgba(53,102,171,0.2)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[rgba(53,102,171,0.3)] focus:border-[rgba(53,102,171,0.3)] ${
                      isMobile ? "py-3 text-base" : "py-2 text-sm"
                    }`}
                  />
                  <Search
                    size={16}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  />
                </div>
              </div>
            </div>

            <div className="p-2.5">
              {/* Featured destinations section - only show if not searching */}
              {!searchQuery && featuredDestinations.length > 0 && (
                <div className="mb-2">
                  <div className="px-3 py-2 text-xs font-semibold text-[#3566ab] border-b border-[rgba(53,102,171,0.1)]">
                    Suggested Destinations
                  </div>
                  {featuredDestinations.map((dest) => (
                    <div
                      key={dest.id}
                      className={`flex items-center gap-2.5 p-3 cursor-pointer rounded-lg transition-all duration-200 ${
                        dest.id === value
                          ? "bg-[rgba(53,102,171,0.12)]"
                          : "hover:bg-[rgba(53,102,171,0.08)]"
                      }`}
                      onClick={() => {
                        onChange(dest.id);
                        setIsOpen(false);
                        setSearchQuery("");
                      }}
                    >
                      <div className="flex items-center justify-center text-[#3566ab]">
                        <MapPin size={14} className="opacity-70" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <span className="text-sm font-medium text-gray-800">
                          {dest.name}
                        </span>
                        <span className="text-xs text-gray-500">
                          {dest.country}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Search results section */}
              {searchQuery && (
                <div>
                  <div className="px-3 py-2 text-xs font-semibold text-[#3566ab] border-b border-[rgba(53,102,171,0.1)]">
                    Search Results
                  </div>
                  {filteredDestinations.length > 0 ? (
                    filteredDestinations.map((dest) => (
                      <div
                        key={dest.id}
                        className={`flex items-center gap-2.5 p-3 cursor-pointer rounded-lg transition-all duration-200 ${
                          dest.id === value
                            ? "bg-[rgba(53,102,171,0.12)]"
                            : "hover:bg-[rgba(53,102,171,0.08)]"
                        }`}
                        onClick={() => {
                          onChange(dest.id);
                          setIsOpen(false);
                          setSearchQuery("");
                        }}
                      >
                        <div className="flex items-center justify-center text-[#3566ab]">
                          <MapPin size={14} className="opacity-70" />
                        </div>
                        <div className="flex flex-col gap-0.5">
                          <span className="text-sm font-medium text-gray-800">
                            {dest.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            {dest.country}
                          </span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      No destinations found
                    </div>
                  )}
                </div>
              )}

              {/* All destinations section - only show if not searching and there are no featured destinations */}
              {!searchQuery && featuredDestinations.length === 0 && (
                <div>
                  {destinations.length > 0 ? (
                    destinations.map((dest) => (
                      <div
                        key={dest.id}
                        className={`flex items-center gap-2.5 p-3 cursor-pointer rounded-lg transition-all duration-200 ${
                          dest.id === value
                            ? "bg-[rgba(53,102,171,0.12)]"
                            : "hover:bg-[rgba(53,102,171,0.08)]"
                        }`}
                        onClick={() => {
                          onChange(dest.id);
                          setIsOpen(false);
                          setSearchQuery("");
                        }}
                      >
                        <div className="flex items-center justify-center text-[#3566ab]">
                          <MapPin size={14} className="opacity-70" />
                        </div>
                        <div className="flex flex-col gap-0.5">
                          <span className="text-sm font-medium text-gray-800">
                            {dest.name}
                          </span>
                          <span className="text-xs text-gray-500">
                            {dest.country}
                          </span>
                        </div>
                      </div>
                    ))
                  ) : (
                    <div className="p-4 text-center text-gray-500 text-sm">
                      No destinations available
                    </div>
                  )}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default FeaturedDestinationSelect;
