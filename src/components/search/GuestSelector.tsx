import React, { useState, useEffect, useRef } from "react";
import { Switch } from "../ui/switch";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface GuestSelectorProps {
  adults: number;
  children: number;
  infants: number;
  isPetsAllowed?: boolean;
  showPetsOption?: boolean; // New prop to control when to show pets option
  onGuestChange: (
    guests: {
      adults: number;
      children: number;
      infants: number;
    },
    submitForm?: boolean,
    isPetsAllowed?: boolean
  ) => void;
  maxAdults?: number;
  maxChildren?: number;
  maxInfants?: number;
  maxOccupancy?: number;
  isOpen: boolean;
  onClose: () => void;
  openAbove?: boolean;
  isMobileModal?: boolean; // New prop to control mobile modal styling
  lang?: Lang; // Add language prop for translations
}

const GuestSelector: React.FC<GuestSelectorProps> = ({
  adults,
  children,
  infants,
  isPetsAllowed = false,
  showPetsOption = false,
  onGuestChange,
  maxAdults = 4,
  maxChildren = 4,
  maxInfants = 2,
  maxOccupancy = 8,
  isOpen,
  onClose,
  openAbove = false,
  isMobileModal = false,
  lang = "en", // Default to English
}) => {
  const t = useTranslations(lang);
  // Initialize local state with the current values from props
  const [localAdults, setLocalAdults] = useState(adults);
  const [localChildren, setLocalChildren] = useState(children);
  const [localInfants, setLocalInfants] = useState(infants);
  const [localIsPetsAllowed, setLocalIsPetsAllowed] = useState(isPetsAllowed);
  const selectorRef = useRef<HTMLDivElement>(null);

  // Reset local state when props change or component opens
  useEffect(() => {
    if (isOpen) {
      setLocalAdults(adults);
      setLocalChildren(children);
      setLocalInfants(infants);
      setLocalIsPetsAllowed(isPetsAllowed);
    }
  }, [isOpen, adults, children, infants, isPetsAllowed]);

  // Calculate total occupancy
  const calculateTotalOccupancy = () => {
    return localAdults + localChildren;
  };

  // Handle clicks outside the guest selector to close it
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        selectorRef.current &&
        !selectorRef.current.contains(event.target as Node) &&
        isOpen
      ) {
        // Close the selector when clicking outside
        onClose();
        // Stop propagation to prevent clicks on elements behind the selector
        event.stopPropagation();
      }
    };

    document.addEventListener("mousedown", handleClickOutside, true);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside, true);
    };
  }, [isOpen, onClose]);

  // Handle adult count changes
  const handleAdultChange = (increment: boolean) => {
    if (increment) {
      if (localAdults < maxAdults && calculateTotalOccupancy() < maxOccupancy) {
        const newAdults = localAdults + 1;
        setLocalAdults(newAdults);
        // Apply changes automatically
        onGuestChange(
          {
            adults: newAdults,
            children: localChildren,
            infants: localInfants,
          },
          false,
          localIsPetsAllowed
        );
      }
    } else {
      // Allow adults to go to 0 only if there are no children or infants
      // Otherwise, minimum should be 1 adult when children/infants are present
      const minAdults = localChildren > 0 || localInfants > 0 ? 1 : 0;
      if (localAdults > minAdults) {
        const newAdults = localAdults - 1;
        setLocalAdults(newAdults);
        // Apply changes automatically
        onGuestChange(
          {
            adults: newAdults,
            children: localChildren,
            infants: localInfants,
          },
          false,
          localIsPetsAllowed
        );
      }
    }
  };

  // Handle children count changes
  const handleChildrenChange = (increment: boolean) => {
    if (increment) {
      const newChildren = localChildren + 1;
      // If adding children and adults count is 0, automatically set adults to 1
      const newAdults = localAdults === 0 ? 1 : localAdults;
      // Calculate the new total occupancy including potential adult increment
      const newTotalOccupancy = newAdults + newChildren;

      if (localChildren < maxChildren && newTotalOccupancy <= maxOccupancy) {
        setLocalChildren(newChildren);
        if (newAdults !== localAdults) {
          setLocalAdults(newAdults);
        }

        // Apply changes automatically
        onGuestChange(
          {
            adults: newAdults,
            children: newChildren,
            infants: localInfants,
          },
          false,
          localIsPetsAllowed
        );
      }
    } else {
      if (localChildren > 0) {
        const newChildren = localChildren - 1;
        setLocalChildren(newChildren);
        // Apply changes automatically
        onGuestChange(
          {
            adults: localAdults,
            children: newChildren,
            infants: localInfants,
          },
          false,
          localIsPetsAllowed
        );
      }
    }
  };

  // Handle infant count changes
  const handleInfantChange = (increment: boolean) => {
    if (increment) {
      const newInfants = localInfants + 1;
      // If adding infants and adults count is 0, automatically set adults to 1
      const newAdults = localAdults === 0 ? 1 : localAdults;
      // Calculate the new total occupancy including potential adult increment
      // Note: infants don't count towards occupancy, so we only check adults + children
      const newTotalOccupancy = newAdults + localChildren;

      if (localInfants < maxInfants && newTotalOccupancy <= maxOccupancy) {
        setLocalInfants(newInfants);
        if (newAdults !== localAdults) {
          setLocalAdults(newAdults);
        }

        // Apply changes automatically
        onGuestChange(
          {
            adults: newAdults,
            children: localChildren,
            infants: newInfants,
          },
          false,
          localIsPetsAllowed
        );
      }
    } else {
      if (localInfants > 0) {
        const newInfants = localInfants - 1;
        setLocalInfants(newInfants);
        // Apply changes automatically
        onGuestChange(
          {
            adults: localAdults,
            children: localChildren,
            infants: newInfants,
          },
          false,
          localIsPetsAllowed
        );
      }
    }
  };

  // Handle pets toggle
  const handlePetsToggle = (newIsPetsAllowed: boolean) => {
    setLocalIsPetsAllowed(newIsPetsAllowed);
    // Apply changes automatically
    onGuestChange(
      {
        adults: localAdults,
        children: localChildren,
        infants: localInfants,
      },
      false,
      newIsPetsAllowed
    );
  };

  if (!isOpen) return null;

  return (
    <div
      ref={selectorRef}
      className={
        isMobileModal
          ? "w-full p-4" // Mobile modal styling - no positioning, full width, more padding
          : `absolute ${
              openAbove ? "bottom-full mb-8" : "top-full mt-8"
            } right-0 bg-white shadow-lg rounded-2xl p-3 z-50 w-72 border border-gray-200` // Desktop popover styling
      }
    >
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-xs text-[#285DA6] font-semibold">
          {t("guestPicker.guests")}
        </h3>
        {!isMobileModal && (
          <button
            type="button"
            className="text-gray-500 hover:text-gray-700"
            onClick={(e) => {
              e.preventDefault();
              e.stopPropagation();
              onClose();
            }}
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              strokeLinecap="round"
              strokeLinejoin="round"
            >
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        )}
      </div>
      <div className="space-y-3">
        {/* Adults */}
        <div className="flex justify-between items-center">
          <div>
            <div className="text-sm font-medium text-left">
              {t("guestPicker.adults")}
            </div>
            <div className="text-xs text-gray-500">18 or above</div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              type="button" // Explicitly set type to button to prevent form submission
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAdultChange(false);
              }}
              disabled={
                localAdults <= (localChildren > 0 || localInfants > 0 ? 1 : 0)
              }
              className="w-7 h-7 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
            <span className="w-8 text-center text-sm font-medium">
              {localAdults}
            </span>
            <button
              type="button" // Explicitly set type to button to prevent form submission
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAdultChange(true);
              }}
              disabled={
                localAdults >= maxAdults ||
                calculateTotalOccupancy() >= maxOccupancy
              }
              className="w-7 h-7 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
          </div>
        </div>

        {/* Children */}
        <div className="flex justify-between items-center">
          <div>
            <div className="text-sm font-medium">
              {t("guestPicker.children")}
            </div>
            <div className="text-xs text-gray-500 text-left">2 – 17</div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              type="button" // Explicitly set type to button to prevent form submission
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleChildrenChange(false);
              }}
              disabled={localChildren <= 0}
              className="w-7 h-7 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
            <span className="w-8 text-center text-sm font-medium">
              {localChildren}
            </span>
            <button
              type="button" // Explicitly set type to button to prevent form submission
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleChildrenChange(true);
              }}
              disabled={
                localChildren >= maxChildren ||
                calculateTotalOccupancy() >= maxOccupancy
              }
              className="w-7 h-7 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
          </div>
        </div>

        {/* Infants */}
        <div className="flex justify-between items-center">
          <div>
            <div className="text-sm font-medium">
              {t("guestPicker.infants")}
            </div>
            <div className="text-xs text-gray-500">Under 2</div>
          </div>
          <div className="flex items-center space-x-3">
            <button
              type="button" // Explicitly set type to button to prevent form submission
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleInfantChange(false);
              }}
              disabled={localInfants <= 0}
              className="w-7 h-7 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
            <span className="w-8 text-center text-sm font-medium">
              {localInfants}
            </span>
            <button
              type="button" // Explicitly set type to button to prevent form submission
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleInfantChange(true);
              }}
              disabled={localInfants >= maxInfants}
              className="w-7 h-7 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="14"
                height="14"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                strokeWidth="2"
                strokeLinecap="round"
                strokeLinejoin="round"
                className="text-[#285DA6]"
              >
                <line x1="12" y1="5" x2="12" y2="19"></line>
                <line x1="5" y1="12" x2="19" y2="12"></line>
              </svg>
            </button>
          </div>
        </div>

        {/* Pets - only show if showPetsOption is true */}
        {showPetsOption && (
          <div className="flex justify-between items-center">
            <div>
              <div className="flex text-sm font-medium">
                {t("guestPicker.pets")}
              </div>
              <div className="text-xs text-gray-500">
                {t("guestPicker.bringingPet")}
              </div>
            </div>
            <div className="flex items-center">
              <Switch
                checked={localIsPetsAllowed}
                onCheckedChange={handlePetsToggle}
                className="data-[state=checked]:bg-[#285DA6] data-[state=unchecked]:bg-gray-200"
              />
            </div>
          </div>
        )}

        {/* No Apply/Cancel buttons - changes are applied automatically */}
      </div>
    </div>
  );
};

export default GuestSelector;
