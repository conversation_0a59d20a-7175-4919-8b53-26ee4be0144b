import React, { useState } from "react";
import "../../styles/search-suggestions.css";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface SearchSuggestionProps {
  onSelectSuggestion: (suggestion: string) => void;
  allowMultiSelect?: boolean;
  lang?: Lang;
}

interface SuggestionItem {
  icon: string;
  text: string;
  expandedPrompt: string;
}

const SearchSuggestions: React.FC<SearchSuggestionProps> = ({
  onSelectSuggestion,
  allowMultiSelect = false,
  lang = "en",
}) => {
  const [selectedSuggestions, setSelectedSuggestions] = useState<string[]>([]);
  const t = useTranslations(lang);

  const handleSuggestionClick = (suggestion: SuggestionItem) => {
    if (allowMultiSelect) {
      const isSelected = selectedSuggestions.includes(
        suggestion.expandedPrompt
      );

      if (isSelected) {
        // Remove from selection
        const newSelection = selectedSuggestions.filter(
          (s) => s !== suggestion.expandedPrompt
        );
        setSelectedSuggestions(newSelection);

        // If no suggestions left, clear the input
        if (newSelection.length === 0) {
          onSelectSuggestion("");
        } else {
          // Combine remaining suggestions with "AND"
          const combinedPrompt = newSelection.join(" AND ");
          onSelectSuggestion(combinedPrompt);
        }
      } else {
        // Add to selection
        const newSelection = [
          ...selectedSuggestions,
          suggestion.expandedPrompt,
        ];
        setSelectedSuggestions(newSelection);

        // Combine all suggestions with "AND"
        const combinedPrompt = newSelection.join(" AND ");
        onSelectSuggestion(combinedPrompt);
      }
    } else {
      // Single selection mode
      onSelectSuggestion(suggestion.expandedPrompt);
    }
  };
  // First row suggestions (more premium/luxury focused)
  const firstRowSuggestions: SuggestionItem[] = [
    {
      icon: "👨‍👩‍👧‍👦",
      text: t("search.suggestions.familyResort"),
      expandedPrompt: t("search.suggestions.familyResortPrompt"),
    },
    {
      icon: "👴👵",
      text: t("search.suggestions.multigenerational"),
      expandedPrompt: t("search.suggestions.multigenerationalPrompt"),
    },
    {
      icon: "⛷️",
      text: t("search.suggestions.familyFriendlyLearning"),
      expandedPrompt: t("search.suggestions.familyFriendlyLearningPrompt"),
    },
    {
      icon: "🧑‍🦳",
      text: t("search.suggestions.adultLearning"),
      expandedPrompt: t("search.suggestions.adultLearningPrompt"),
    },
    {
      icon: "🔰",
      text: t("search.suggestions.firstTimeSkiers"),
      expandedPrompt: t("search.suggestions.firstTimeSkiersPrompt"),
    },
    {
      icon: "☀️",
      text: t("search.suggestions.wideBlueCruising"),
      expandedPrompt: t("search.suggestions.wideBlueCruisingPrompt"),
    },
    {
      icon: "🏘️",
      text: t("search.suggestions.stylishVillage"),
      expandedPrompt: t("search.suggestions.stylishVillagePrompt"),
    },
    {
      icon: "🏔️",
      text: t("search.suggestions.quietAlpineVillage"),
      expandedPrompt: t("search.suggestions.quietAlpineVillagePrompt"),
    },
    {
      icon: "🎿",
      text: t("search.suggestions.soloFriendly"),
      expandedPrompt: t("search.suggestions.soloFriendlyPrompt"),
    },
  ];

  // Second row suggestions (more activity/experience focused)
  const secondRowSuggestions: SuggestionItem[] = [
    {
      icon: "🎿",
      text: t("search.suggestions.progressWithoutPressure"),
      expandedPrompt: t("search.suggestions.progressWithoutPressurePrompt"),
    },
    {
      icon: "👨‍👩‍👧‍👦",
      text: t("search.suggestions.blackRedPistes"),
      expandedPrompt: t("search.suggestions.blackRedPistesPrompt"),
    },
    {
      icon: "🏆",
      text: t("search.suggestions.technicalTerrain"),
      expandedPrompt: t("search.suggestions.technicalTerrainPrompt"),
    },
    {
      icon: "⛷️",
      text: t("search.suggestions.challengingExpert"),
      expandedPrompt: t("search.suggestions.challengingExpertPrompt"),
    },
    {
      icon: "🍽️",
      text: t("search.suggestions.powderFreeride"),
      expandedPrompt: t("search.suggestions.powderFreeridePrompt"),
    },
    {
      icon: "🌞",
      text: t("search.suggestions.highAltitudeSpring"),
      expandedPrompt: t("search.suggestions.highAltitudeSpringPrompt"),
    },
    {
      icon: "🏔️",
      text: t("search.suggestions.uniqueExperiences"),
      expandedPrompt: t("search.suggestions.uniqueExperiencesPrompt"),
    },
    {
      icon: "🥾",
      text: t("search.suggestions.skiTouringComfort"),
      expandedPrompt: t("search.suggestions.skiTouringComfortPrompt"),
    },
    {
      icon: "🧒",
      text: t("search.suggestions.teenIndependence"),
      expandedPrompt: t("search.suggestions.teenIndependencePrompt"),
    },
    {
      icon: "🎉",
      text: t("search.suggestions.friendsWeekend"),
      expandedPrompt: t("search.suggestions.friendsWeekendPrompt"),
    },
  ];

  return (
    <div className="search-suggestions-container">
      <div className="suggestions-row">
        {firstRowSuggestions.map((suggestion, index) => {
          const isSelected =
            allowMultiSelect &&
            selectedSuggestions.includes(suggestion.expandedPrompt);
          return (
            <button
              key={`row1-${index}`}
              className={`suggestion-chip ${isSelected ? "selected" : ""}`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <span className="suggestion-icon">{suggestion.icon}</span>
              <span className="suggestion-text">{suggestion.text}</span>
              {allowMultiSelect && isSelected && (
                <span className="selection-indicator">✓</span>
              )}
            </button>
          );
        })}
      </div>

      <div className="suggestions-row">
        {secondRowSuggestions.map((suggestion, index) => {
          const isSelected =
            allowMultiSelect &&
            selectedSuggestions.includes(suggestion.expandedPrompt);
          return (
            <button
              key={`row2-${index}`}
              className={`suggestion-chip ${isSelected ? "selected" : ""}`}
              onClick={() => handleSuggestionClick(suggestion)}
            >
              <span className="suggestion-icon">{suggestion.icon}</span>
              <span className="suggestion-text">{suggestion.text}</span>
              {allowMultiSelect && isSelected && (
                <span className="selection-indicator">✓</span>
              )}
            </button>
          );
        })}
      </div>
    </div>
  );
};

export default SearchSuggestions;
