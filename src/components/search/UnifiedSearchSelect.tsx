import React, { useState, useRef, useEffect } from "react";
import { ChevronDown, MapPin, Search, Building2 } from "lucide-react";
import { useIsMobile } from "../../hooks/use-mobile";
import { fetchHotels, fetchHotelDetailsById, type Hotel } from "../../utils/store/hotels";
import {
  fetchDestinationsForSearch,
  fetchDestinations,
  fetchDestinationById,
  type Destination
} from "../../utils/store/destinations";
import { useTranslations } from "../../i18n/utils";
import type { Lang } from "../../i18n/ui";

interface UnifiedSearchSelectProps {
  value: string;
  onChange: (value: string, type: "destination" | "hotel", label?: string) => void;
  placeholder?: string;
  openAbove?: boolean;
  lang?: Lang;
  storedLabel?: string; // Add stored label prop for immediate display
}



const UnifiedSearchSelect: React.FC<UnifiedSearchSelectProps> = ({
  value,
  onChange,
  placeholder,
  openAbove = false,
  lang = "en",
  storedLabel,
}) => {
  const t = useTranslations(lang);
  const defaultPlaceholder =
    placeholder || t("search.placeholder.regularSearch");
  const [isOpen, setIsOpen] = useState(false);
  const [searchQuery, setSearchQuery] = useState("");
  const [hotels, setHotels] = useState<Hotel[]>([]);
  const [isLoadingHotels, setIsLoadingHotels] = useState(false);
  const [featuredDestinations, setFeaturedDestinations] = useState<Destination[]>([]);
  const [filteredDestinations, setFilteredDestinations] = useState<Destination[]>([]);
  const [isLoadingDestinations, setIsLoadingDestinations] = useState(false);
  const [selectedDestinationDetails, setSelectedDestinationDetails] = useState<Destination | null>(null);
  const [selectedHotelDetails, setSelectedHotelDetails] = useState<Hotel | null>(null);
  const [isLoadingSelectedItem, setIsLoadingSelectedItem] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);
  const searchInputRef = useRef<HTMLInputElement>(null);
  const isMobile = useIsMobile();

  // Filter hotels based on search query (only by name since location is not displayed)
  const filteredHotels = hotels.filter((hotel) =>
    hotel.name.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Find the selected item (destination or hotel)
  // Priority: 1. selectedDetails (persisted), 2. current arrays
  const allDestinations = [...featuredDestinations, ...filteredDestinations];
  const selectedDestination = selectedDestinationDetails ||
    allDestinations.find((dest) => dest.id === value);
  const selectedHotel = selectedHotelDetails || hotels.find((hotel) => hotel.id.toString() === value);
  const selectedItem = selectedDestination || selectedHotel;



  // Fetch featured destinations on component mount
  useEffect(() => {
    const loadFeaturedDestinations = async () => {
      try {
        const destinations = await fetchDestinationsForSearch(10);
        setFeaturedDestinations(destinations);
      } catch (error) {
        console.error("Error fetching featured destinations:", error);
        setFeaturedDestinations([]);
      }
    };

    loadFeaturedDestinations();
  }, []);

  // Clear selected details when value is cleared
  useEffect(() => {
    if (!value) {
      console.log("Value cleared, resetting selected details");
      setSelectedDestinationDetails(null);
      setSelectedHotelDetails(null);
    }
  }, [value]);

  // Fetch selected item details when value changes
  useEffect(() => {
    const loadSelectedItemDetails = async () => {
      console.log("loadSelectedItemDetails called with value:", value);

      if (!value) {
        return; // Already handled by the effect above
      }

      // Check if we already have the item in our current data
      // Get fresh arrays inside the function to avoid dependency issues
      const currentAllDestinations = [...featuredDestinations, ...filteredDestinations];
      const existingDestination = currentAllDestinations.find((dest) => dest.id === value);
      const existingHotel = hotels.find((hotel) => hotel.id.toString() === value);

      if (existingDestination) {
        setSelectedDestinationDetails(existingDestination);
        setSelectedHotelDetails(null);
        return;
      }

      if (existingHotel) {
        setSelectedHotelDetails(existingHotel);
        setSelectedDestinationDetails(null);
        return;
      }

      setIsLoadingSelectedItem(true);
      try {
        // Try to fetch as destination first
        try {
          console.log("🔍 Trying to fetch destination with ID:", value);
          const destination = await fetchDestinationById(value);
          console.log("🔍 Destination API response:", destination);

          if (destination) {
            console.log("✅ Destination fetched successfully:", destination.name);
            setSelectedDestinationDetails(destination);
            setSelectedHotelDetails(null);
          } else {
            console.log("❌ Destination API returned null, trying as hotel...");
            throw new Error("Destination not found");
          }
        } catch (destError) {
          console.log("❌ Destination fetch failed, trying as hotel...", destError);
          // If destination fetch fails, try as hotel
          try {
            console.log("🔍 Trying to fetch hotel with ID:", value);
            const hotelData = await fetchHotelDetailsById(value);
            console.log("🔍 Hotel API response:", hotelData);

            if (hotelData?.hotel) {
              console.log("✅ Hotel fetched successfully:", hotelData.hotel.name);
              setSelectedHotelDetails(hotelData.hotel);
              setSelectedDestinationDetails(null);
            } else {
              console.log("❌ Hotel API returned null");
              throw new Error("Hotel not found");
            }
          } catch (hotelError) {
            console.error("❌ Error fetching selected item details (both destination and hotel failed):", {
              destinationId: value,
              destError: destError instanceof Error ? destError.message : String(destError),
              hotelError: hotelError instanceof Error ? hotelError.message : String(hotelError)
            });
            setSelectedDestinationDetails(null);
            setSelectedHotelDetails(null);

            // If both API calls fail, we should still stop loading and rely on storedLabel
            console.log("🔍 Both API calls failed, will rely on storedLabel if available");
          }
        }
      } catch (error) {
        console.error("❌ Error loading selected item:", error);
      } finally {
        console.log("🔍 Setting isLoadingSelectedItem to false");
        setIsLoadingSelectedItem(false);
      }
    };

    loadSelectedItemDetails();
  }, [value]); // Only depend on value to prevent infinite loops

  // Search for destinations and hotels when query changes
  useEffect(() => {
    const searchDestinationsAndHotels = async () => {
      if (searchQuery.length >= 2) {
        setIsLoadingDestinations(true);
        setIsLoadingHotels(true);

        try {
          console.log("Searching for:", searchQuery);

          // Search destinations - this should filter based on search query
          const destinationsResult = await fetchDestinations({
            limit: 10,
            searchQuery: searchQuery,
          });

          console.log("Destinations API result:", destinationsResult);
          console.log("Filtered destinations count:", destinationsResult.destinations.length);

          // Apply client-side filtering as a fallback if API doesn't filter properly
          const clientFilteredDestinations = destinationsResult.destinations.filter((dest) =>
            dest.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            dest.country.toLowerCase().includes(searchQuery.toLowerCase())
          );

          console.log("Client-side filtered destinations count:", clientFilteredDestinations.length);

          setFilteredDestinations(clientFilteredDestinations);

          // Search hotels - fix the search parameters format
          const hotelsResult = await fetchHotels({
            limit: 10,
            searchParams: {
              search: searchQuery, // Use 'search' instead of separate location/name
            },
          });

          console.log("Hotels API result:", hotelsResult);
          console.log("Hotels count:", hotelsResult.hotels.length);

          // Apply client-side filtering for hotels as well
          const clientFilteredHotels = hotelsResult.hotels.filter((hotel) =>
            hotel.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
            (hotel.location && hotel.location.toLowerCase().includes(searchQuery.toLowerCase()))
          );

          console.log("Client-side filtered hotels count:", clientFilteredHotels.length);

          setHotels(clientFilteredHotels);
        } catch (error) {
          console.error("Error searching destinations and hotels:", error);
          setFilteredDestinations([]);
          setHotels([]);
        } finally {
          setIsLoadingDestinations(false);
          setIsLoadingHotels(false);
        }
      } else {
        // When clearing search, don't clear the arrays if we have a selected item that might be in them
        // Only clear if we don't have a selected destination/hotel from these arrays
        if (!selectedDestinationDetails && !selectedHotelDetails) {
          setFilteredDestinations([]);
          setHotels([]);
        }
      }
    };

    const debounceTimer = setTimeout(searchDestinationsAndHotels, 300);
    return () => clearTimeout(debounceTimer);
  }, [searchQuery, selectedDestinationDetails, selectedHotelDetails]);

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
        // Don't clear search query immediately - let the selection persist
        // setSearchQuery("");
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  // Focus search input when dropdown opens
  useEffect(() => {
    if (isOpen && searchInputRef.current) {
      setTimeout(() => {
        searchInputRef.current?.focus();
      }, 100);
    }
  }, [isOpen]);

  const handleItemSelect = (id: string, type: "destination" | "hotel") => {
    let label: string | undefined;

    // Find the selected item and store it immediately for better UX
    if (type === "destination") {
      // Look in all possible destination arrays
      const destination = allDestinations.find(dest => dest.id === id) ||
        featuredDestinations.find(dest => dest.id === id) ||
        filteredDestinations.find(dest => dest.id === id);

      if (destination) {
        setSelectedDestinationDetails(destination);
        setSelectedHotelDetails(null);
        label = `${destination.name}, ${destination.country}`;
      } else {
        console.log("Destination not found in current arrays, will need to fetch");
        // Clear previous selections and let the useEffect handle the API call
        setSelectedDestinationDetails(null);
        setSelectedHotelDetails(null);
      }
    } else if (type === "hotel") {
      const hotel = hotels.find(h => h.id.toString() === id);
      console.log("Found hotel in arrays:", hotel?.name);

      if (hotel) {
        setSelectedHotelDetails(hotel);
        setSelectedDestinationDetails(null);
        label = hotel.name;
      } else {
        console.log("Hotel not found in current arrays, will need to fetch");
        // Clear previous selections and let the useEffect handle the API call
        setSelectedDestinationDetails(null);
        setSelectedHotelDetails(null);
      }
    }

    onChange(id, type, label);
    setIsOpen(false);
    setSearchQuery("");
  };

  const getDisplayText = () => {
    let result;

    // HIGHEST PRIORITY: Use stored label if available (for immediate display from localStorage)
    // This should always take precedence to avoid flickering and provide immediate feedback
    if (value && storedLabel) {
      result = storedLabel;
    }
    // If loading and no stored label, show loading message
    else if (isLoadingSelectedItem) {
      result = "Loading destination...";
    }
    // Use fetched details if available and no stored label
    else if (selectedDestinationDetails) {
      result = `${selectedDestinationDetails.name}, ${selectedDestinationDetails.country}`;
    }
    else if (selectedHotelDetails) {
      result = selectedHotelDetails.name;
    }
    // Fallback to items found in current arrays
    else if (selectedDestination) {
      result = `${selectedDestination.name}, ${selectedDestination.country}`;
    }
    else if (selectedHotel) {
      result = selectedHotel.name;
    }
    // Fallback: if we have a value but no selected item details, show a more user-friendly message
    else if (value && !selectedItem) {
      result = "Loading destination...";
    }
    else {
      result = placeholder;
    }

    console.log("🔍 getDisplayText:", {
      result,
      value,
      storedLabel,
      isLoadingSelectedItem,
      hasSelectedDestinationDetails: !!selectedDestinationDetails,
      hasSelectedHotelDetails: !!selectedHotelDetails
    });

    return result;
  };

  return (
    <div className="relative w-full font-karla" ref={dropdownRef}>
      <div
        className={`flex items-center justify-center gap-2 py-1 cursor-pointer rounded-md transition-all duration-200 relative ${isOpen ? "text-[#3566ab]" : ""
          } ${value ? "text-[#3566ab]" : ""}`}
        onClick={() => setIsOpen(!isOpen)}
      >
        {!isMobile && (
          <div className="flex items-center justify-center text-[#3566ab] opacity-80">
            {selectedHotel ? <Building2 size={16} /> : <MapPin size={16} />}
          </div>
        )}
        <div className="flex-1 whitespace-nowrap overflow-hidden text-ellipsis text-left">
          {selectedItem || value ? (
            <span className={`font-medium ${isMobile ? 'text-xs' : 'text-sm'} text-[#3566ab]`}>
              {getDisplayText()}
            </span>
          ) : (
            <span className={`${isMobile ? 'text-xs' : 'text-sm'} text-gray-500`}>
              {placeholder}
            </span>
          )}
        </div>
        {!isMobile && (
          <div className="flex items-center justify-center text-gray-500">
            <ChevronDown
              size={16}
              className={`transition-transform duration-200 ${isOpen ? "rotate-180" : ""
                }`}
            />
          </div>
        )}
      </div>

      {isOpen && (
        <>
          {/* Mobile backdrop */}
          {isMobile && (
            <div
              className="fixed inset-0 bg-black bg-opacity-50 z-[9998]"
              onClick={() => setIsOpen(false)}
            />
          )}
          <div
            className={`${isMobile
                ? "fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-[90vw] max-w-[350px] z-[9999]"
                : `absolute ${openAbove
                  ? "bottom-[calc(100%+8px)] mb-4 "
                  : "top-[calc(100%+8px)]"
                } -left-3 w-[calc(100%+24px)] z-[1000]`
              } bg-white rounded-2xl shadow-lg max-h-[400px] overflow-y-auto border border-[rgba(53,102,171,0.1)]`}
            style={
              isMobile ? { boxShadow: "0 10px 30px rgba(0, 0, 0, 0.3)" } : {}
            }
          >
            {/* Header with search input and close button */}
            <div className="sticky top-0 bg-white border-b border-[rgba(53,102,171,0.1)]">
              {/* Mobile header with close button */}
              {isMobile && (
                <div className="flex justify-between items-center p-2.5 pb-0">
                  <h3 className="text-sm font-semibold text-[#3566ab]">
                    Search Destinations & Stays
                  </h3>
                  <button
                    type="button"
                    className="text-gray-500 hover:text-gray-700 p-1"
                    onClick={() => setIsOpen(false)}
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <line x1="18" y1="6" x2="6" y2="18"></line>
                      <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                  </button>
                </div>
              )}
              {/* Search input */}
              <div className="p-2.5">
                <div className="relative">
                  <input
                    ref={searchInputRef}
                    type="text"
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    placeholder={defaultPlaceholder}
                    className={`w-full pl-9 pr-3 border border-[rgba(53,102,171,0.2)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[rgba(53,102,171,0.3)] focus:border-[rgba(53,102,171,0.3)] ${isMobile ? "py-3 text-base" : "py-2 text-sm"
                      }`}
                  />
                  <Search
                    size={16}
                    className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                  />
                </div>
              </div>
            </div>

            <div className="p-2.5">
              {/* Featured destinations section - only show if not searching */}
              {!searchQuery && featuredDestinations.length > 0 && (
                <div className="mb-2">
                  <div className="px-3 py-2 text-xs text-left font-semibold text-[#3566ab] border-b border-[rgba(53,102,171,0.1)]">
                    Suggested Destinations
                  </div>
                  {featuredDestinations.map((dest) => (
                    <div
                      key={dest.id}
                      className={`flex items-center gap-2.5 p-3 cursor-pointer rounded-lg transition-all duration-200 ${dest.id === value
                          ? "bg-[rgba(53,102,171,0.12)]"
                          : "hover:bg-[rgba(53,102,171,0.08)]"
                        }`}
                      onClick={() => handleItemSelect(dest.id, "destination")}
                    >
                      <div className="flex items-center justify-center text-[#3566ab]">
                        <MapPin size={14} className="opacity-70" />
                      </div>
                      <div className="flex flex-col gap-0.5">
                        <span className="text-sm text-left font-medium text-gray-800">
                          {dest.name}
                        </span>
                        <span className="text-xs text-left text-gray-500">
                          {dest.country}
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              )}

              {/* Search results section */}
              {searchQuery && (
                <div>
                  {/* Destinations results */}
                  {(filteredDestinations.length > 0 || isLoadingDestinations) && (
                    <div className="mb-4">
                      <div className="px-3 py-2 text-xs text-left  font-semibold text-[#3566ab] border-b border-[rgba(53,102,171,0.1)]">
                        Destinations
                      </div>
                      {isLoadingDestinations ? (
                        <div className="p-4 text-center text-gray-500 text-sm">
                          Searching destinations...
                        </div>
                      ) : (
                        filteredDestinations.map((dest) => (
                          <div
                            key={dest.id}
                            className={`flex items-center gap-2.5 p-3 cursor-pointer rounded-lg transition-all duration-200 ${dest.id === value
                                ? "bg-[rgba(53,102,171,0.12)]"
                                : "hover:bg-[rgba(53,102,171,0.08)]"
                              }`}
                            onClick={() =>
                              handleItemSelect(dest.id, "destination")
                            }
                          >
                            <div className="flex items-center justify-center text-[#3566ab]">
                              <MapPin size={14} className="opacity-70" />
                            </div>
                            <div className="flex flex-col gap-0.5">
                              <span className="text-sm text-left font-medium text-gray-800">
                                {dest.name}
                              </span>
                              <span className="text-xs text-left text-gray-500">
                                {dest.country}
                              </span>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  {/* Hotels results */}
                  {(filteredHotels.length > 0 || isLoadingHotels) && (
                    <div>
                      <div className="px-3 py-2 text-xs text-left  font-semibold text-[#3566ab] border-b border-[rgba(53,102,171,0.1)]">
                        Stays
                      </div>
                      {isLoadingHotels ? (
                        <div className="p-4 text-center text-gray-500 text-sm">
                          Searching stays...
                        </div>
                      ) : (
                        filteredHotels.map((hotel) => (
                          <div
                            key={hotel.id}
                            className={`flex items-center gap-2.5 p-3 cursor-pointer rounded-lg transition-all duration-200 ${hotel.id.toString() === value
                                ? "bg-[rgba(53,102,171,0.12)]"
                                : "hover:bg-[rgba(53,102,171,0.08)]"
                              }`}
                            onClick={() =>
                              handleItemSelect(hotel.id.toString(), "hotel")
                            }
                          >
                            <div className="flex items-center justify-center text-[#3566ab]">
                              <Building2 size={14} className="opacity-70" />
                            </div>
                            <div className="flex flex-col gap-0.5">
                              <span className="text-sm text-left font-medium text-gray-800">
                                {hotel.name}
                              </span>
                            </div>
                          </div>
                        ))
                      )}
                    </div>
                  )}

                  {/* No results */}
                  {searchQuery &&
                    filteredDestinations.length === 0 &&
                    filteredHotels.length === 0 &&
                    !isLoadingHotels &&
                    !isLoadingDestinations && (
                      <div className="p-4 text-center text-gray-500 text-sm">
                        No destinations or stays found
                      </div>
                    )}
                </div>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
};

export default UnifiedSearchSelect;
