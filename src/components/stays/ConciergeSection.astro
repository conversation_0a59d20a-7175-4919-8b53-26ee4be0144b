---
const services = [
  {
    icon: "ski",
    title: "Private Ski Lessons",
    description:
      "Book sessions with world-class instructors, from Olympic medalists to local experts.",
  },
  {
    icon: "utensils",
    title: "Fine Dining Reservations",
    description:
      "Secure tables at the most exclusive mountain restaurants and Michelin-starred venues.",
  },
  {
    icon: "helicopter",
    title: "Helicopter Transfers",
    description:
      "Arrive in style with door-to-door helicopter service from major airports.",
  },
  {
    icon: "snowflake",
    title: "Off-Piste Adventures",
    description:
      "Experience untouched powder with expert guides and safety equipment.",
  },
  {
    icon: "glass-cheers",
    title: "In-Chalet Experiences",
    description:
      "From private chefs to in-room spa treatments, enjoy luxury in the comfort of your accommodation.",
  },
  {
    icon: "mountain",
    title: "Exclusive Excursions",
    description:
      "Discover hidden gems and breathtaking vistas with our curated mountain experiences.",
  },
];
---

<section class="py-16 relative overflow-hidden">
  <div class="absolute inset-0 z-0">
    <img
      src="https://images.unsplash.com/photo-1551632811-561732d1e306?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
      alt="Luxury concierge services"
      class="w-full h-full object-cover opacity-10"
    />
    <div class="absolute inset-0 bg-background/80 backdrop-blur-sm"></div>
  </div>

  <div class="container-custom relative z-10">
    <div class="text-center mb-12">
      <p class="section-micro-headline">Beyond Accommodation</p>
      <h2 class="section-title">Premium Concierge Services</h2>
      <p class="section-subtitle max-w-2xl mx-auto">
        Our dedicated concierge team is available 24/7 to elevate your mountain
        adventure with bespoke services and exclusive access.<a
          href="/contact"
          class="text-[#285DA6] underline ml-1 hover:text-primary/80 transition-colors"
          >Reach out today!</a
        >
      </p>
    </div>

    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-8">
      {
        services.map((service) => (
          <div class="bg-background/80 backdrop-blur-sm p-6 rounded-md border border-border/30 shadow-sm hover:shadow-glow transition-all duration-300">
            <div class="w-12 h-12 bg-[#285DA6]/10 rounded-full flex items-center justify-center mb-4">
              {service.icon === "ski" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <line x1="17" y1="3" x2="5" y2="21" />
                  <line x1="19" y1="5" x2="7" y2="23" />
                  <path d="M22 2l-1.5 1.5" />
                  <path d="M10.5 14.5L14 18" />
                  <path d="M16 8l-1.5 1.5" />
                  <path d="M2 22l1.5-1.5" />
                </svg>
              )}
              {service.icon === "utensils" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <path d="M3 2v7c0 1.1.9 2 2 2h4a2 2 0 0 0 2-2V2" />
                  <path d="M7 2v20" />
                  <path d="M21 15V2v0a5 5 0 0 0-5 5v6c0 1.1.9 2 2 2h3Zm0 0v7" />
                </svg>
              )}
              {service.icon === "helicopter" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <path d="M12 22v-8l-4-2-6 3v-3l6-3 10 5 4-2v3l-4 2-6-3v8z" />
                  <circle cx="12" cy="4" r="2" />
                  <path d="M10 7L8 9" />
                  <path d="M14 7l2 2" />
                </svg>
              )}
              {service.icon === "snowflake" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <path d="M12 2v20" />
                  <path d="M17.5 4.5L6.5 19.5" />
                  <path d="M6.5 4.5l11 15" />
                  <path d="M4 12h16" />
                </svg>
              )}
              {service.icon === "glass-cheers" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <path d="M8 21v-5a2 2 0 0 1 2-2h4a2 2 0 0 1 2 2v5" />
                  <path d="M2 9h20" />
                  <path d="M18 11l4-2" />
                  <path d="M6 11l-4-2" />
                  <path d="M12 13v8" />
                  <path d="M12 3v6" />
                </svg>
              )}
              {service.icon === "mountain" && (
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <path d="m8 3 4 8 5-5 5 15H2L8 3z" />
                </svg>
              )}
            </div>
            <h3 class="text-lg font-baskervville mb-2">{service.title}</h3>
            <p class="text-foreground/70 text-sm">{service.description}</p>
          </div>
        ))
      }
    </div>
  </div>
</section>
