import * as React from "react";
import { ChevronDown } from "lucide-react";
import { cn } from "../../lib/utils";

interface AccordionItemProps {
  question: string;
  answer: string;
  isOpen: boolean;
  onToggle: () => void;
}

const AccordionItem: React.FC<AccordionItemProps> = ({
  question,
  answer,
  isOpen,
  onToggle,
}) => {
  const contentRef = React.useRef<HTMLDivElement>(null);
  const [contentHeight, setContentHeight] = React.useState(0);

  React.useEffect(() => {
    const updateHeight = () => {
      if (contentRef.current) {
        setContentHeight(contentRef.current.scrollHeight);
      }
    };

    updateHeight();

    // Update height on window resize to handle responsive text changes
    window.addEventListener('resize', updateHeight);
    return () => window.removeEventListener('resize', updateHeight);
  }, [answer, isOpen]);

  return (
    <div className="border border-border/20 rounded-lg sm:rounded-xl overflow-hidden bg-white/60 backdrop-blur-sm shadow-sm hover:shadow-lg transition-all duration-300 hover:border-[#285DA6]/20">
      <button
        className="w-full px-4 sm:px-6 py-3 sm:py-4 text-left flex items-center justify-between hover:bg-[#285DA6]/5 transition-all duration-200 focus:outline-none focus:ring-inset group"
        onClick={onToggle}
        aria-expanded={isOpen}
      >
        <h3 className="text-sm sm:text-base font-baskervville text-foreground pr-3 sm:pr-4 group-hover:text-[#285DA6] transition-colors duration-200 leading-snug normal-case">
          {question}
        </h3>
        <div className={cn(
          "w-7 h-7 sm:w-8 sm:h-8 rounded-full bg-[#285DA6]/10 flex items-center justify-center transition-all duration-300 flex-shrink-0",
          isOpen && "bg-[#285DA6] rotate-180"
        )}>
          <ChevronDown
            className={cn(
              "h-3 w-3 sm:h-4 sm:w-4 transition-all duration-300",
              isOpen ? "text-white" : "text-[#285DA6]"
            )}
          />
        </div>
      </button>
      <div
        className={cn(
          "overflow-hidden transition-all duration-500 ease-in-out",
          isOpen ? "max-h-none" : "max-h-0"
        )}
        style={{
          maxHeight: isOpen ? `${contentHeight + 20}px` : '0px',
          opacity: isOpen ? 1 : 0,
        }}
      >
        <div ref={contentRef} className="px-4 sm:px-6 pb-6 sm:pb-8 pt-3 sm:pt-4">
          <div className="text-xs sm:text-sm text-foreground/80 leading-relaxed font-karla border-l-2 border-[#285DA6]/20 pl-4 sm:pl-6 py-2 sm:py-3">
            {answer.split('\n').map((paragraph, index) => (
              <p key={index} className="mb-2 last:mb-0">
                {paragraph}
              </p>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

interface AccordionProps {
  items: Array<{
    question: string;
    answer: string;
  }>;
  allowMultiple?: boolean;
  className?: string;
}

const Accordion: React.FC<AccordionProps> = ({
  items,
  allowMultiple = false,
  className,
}) => {
  const [openItems, setOpenItems] = React.useState<Set<number>>(new Set());

  const toggleItem = (index: number) => {
    setOpenItems((prev) => {
      const newOpenItems = new Set(prev);

      if (newOpenItems.has(index)) {
        newOpenItems.delete(index);
      } else {
        if (!allowMultiple) {
          newOpenItems.clear();
        }
        newOpenItems.add(index);
      }

      return newOpenItems;
    });
  };

  return (
    <div className={cn("space-y-4", className)}>
      {items.map((item, index) => (
        <AccordionItem
          key={index}
          question={item.question}
          answer={item.answer}
          isOpen={openItems.has(index)}
          onToggle={() => toggleItem(index)}
        />
      ))}
    </div>
  );
};

export { Accordion, AccordionItem };
export type { AccordionProps, AccordionItemProps };
