import React, { useState, useEffect, useRef } from "react";
import { useUser } from "../../contexts/UserContext";
import { isHomePage, redirectToHomePage } from "../../i18n/utils";

const SimpleDropdown: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [userName, setUserName] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);
  const timeoutRef = useRef<number | null>(null);

  // Try to use the UserContext
  let userContext = null;
  try {
    userContext = useUser();
  } catch (error) {
    // Context not available, will use token-based auth
  }

  // Effect to handle scroll state and hover effects
  useEffect(() => {
    const userProfileButton = document.getElementById("user-profile-button");
    const userProfileIcon = document.getElementById("user-profile-icon");

    // Check if we're on the homepage
    const isHomePageCurrent = isHomePage(window.location.pathname);

    // Handle scroll state
    const handleScroll = () => {
      const isScrolled = window.scrollY > 50;
      const userAvatar = document.getElementById("user-avatar");

      if (userProfileButton) {
        if (isScrolled) {
          userProfileButton.classList.add("scrolled-state");
          userProfileButton.style.backgroundColor = "rgba(255, 255, 255, 0.8)";
          userProfileButton.style.borderColor = "rgba(53, 102, 171, 0.2)";

          // Update the icon color if not logged in
          if (userProfileIcon) {
            userProfileIcon.setAttribute("stroke", "#3566ab");
          }

          // Update avatar background and text color when scrolled
          if (userAvatar) {
            userAvatar.style.backgroundColor = "white";
            userAvatar.style.color = "#285DA6";
          }
        } else {
          userProfileButton.classList.remove("scrolled-state");
          userProfileButton.style.backgroundColor = "transparent";

          // Different styling for homepage vs other pages
          if (isHomePageCurrent) {
            userProfileButton.style.borderColor = "rgba(255, 255, 255, 0.3)";

            // White icon only on homepage when not scrolled
            if (userProfileIcon) {
              userProfileIcon.setAttribute("stroke", "white");
            }

            // No background for avatar on homepage when not scrolled
            if (userAvatar) {
              userAvatar.style.backgroundColor = "transparent";
              userAvatar.style.color = "white";
            }
          } else {
            // Blue styling for non-homepage
            userProfileButton.style.borderColor = "rgba(53, 102, 171, 0.2)";

            // Keep icon blue on non-homepage pages
            if (userProfileIcon) {
              userProfileIcon.setAttribute("stroke", "#3566ab");
            }

            // Always show scrolled state styling for avatar on non-homepage pages
            if (userAvatar) {
              userAvatar.style.backgroundColor = "white";
              userAvatar.style.color = "#285DA6";
            }
          }
        }
      }
    };

    // Handle hover effects
    const handleMouseEnter = () => {
      if (userProfileButton) {
        userProfileButton.style.borderColor = "rgba(53, 102, 171, 0.5)";
        userProfileButton.style.backgroundColor = "rgba(255, 255, 255, 0.1)";
        userProfileButton.style.transform = "translateY(-1px)";

        // Apply transform to icon if it exists
        if (userProfileIcon) {
          userProfileIcon.style.transform = "scale(1.05)";
        }

        // Apply transform to avatar content if it exists
        const userAvatar = document.getElementById("user-avatar");
        if (userAvatar) {
          userAvatar.style.transform = "scale(1.05)";
        }

        // Different hover effect when scrolled
        if (userProfileButton.classList.contains("scrolled-state")) {
          userProfileButton.style.backgroundColor = "rgba(255, 255, 255, 0.95)";
          userProfileButton.style.borderColor = "rgba(53, 102, 171, 0.8)";
          userProfileButton.style.boxShadow =
            "0 2px 8px rgba(53, 102, 171, 0.15)";
        }
      }
    };

    const handleMouseLeave = () => {
      if (userProfileButton) {
        userProfileButton.style.transform = "";

        // Reset icon transform if it exists
        if (userProfileIcon) {
          userProfileIcon.style.transform = "";
        }

        // Reset avatar transform if it exists
        const userAvatar = document.getElementById("user-avatar");
        if (userAvatar) {
          userAvatar.style.transform = "";
        }

        // Reset to appropriate state based on scroll
        handleScroll();
      }
    };

    // Handle active/pressed state
    const handleMouseDown = () => {
      if (userProfileButton) {
        userProfileButton.style.transform = "translateY(0)";
        userProfileButton.style.transition = "all 0.1s ease";

        // Apply scale to avatar if it exists
        const userAvatar = document.getElementById("user-avatar");
        if (userAvatar) {
          userAvatar.style.transform = "scale(0.95)";
          userAvatar.style.transition = "all 0.1s ease";
        }

        if (userProfileButton.classList.contains("scrolled-state")) {
          userProfileButton.style.backgroundColor = "rgba(53, 102, 171, 0.05)";
        }
      }
    };

    const handleMouseUp = () => {
      if (userProfileButton) {
        // Return to hover state since mouse is still over the button
        handleMouseEnter();
      }
    };

    // Set initial state based on page type
    const setInitialState = () => {
      const isHomePageCurrent = isHomePage(window.location.pathname);
      const userAvatar = document.getElementById("user-avatar");

      if (!isHomePageCurrent && userProfileButton && userProfileIcon) {
        // For non-homepage, ensure icons are blue from the start
        userProfileIcon.setAttribute("stroke", "#3566ab");
        userProfileButton.style.borderColor = "rgba(53, 102, 171, 0.2)";

        // For non-homepage, always show scrolled state styling for avatar
        if (userAvatar) {
          userAvatar.style.backgroundColor = "white";
          userAvatar.style.color = "#285DA6";
        }
      } else if (isHomePageCurrent && userAvatar) {
        // For homepage, start with transparent background and white text
        userAvatar.style.backgroundColor = "transparent";
        userAvatar.style.color = "white";
      }

      // Then handle scroll state
      handleScroll();
    };

    // Run initial setup after component mounts
    setInitialState();

    // Add event listeners
    window.addEventListener("scroll", handleScroll);
    if (userProfileButton) {
      userProfileButton.addEventListener("mouseenter", handleMouseEnter);
      userProfileButton.addEventListener("mouseleave", handleMouseLeave);
      userProfileButton.addEventListener("mousedown", handleMouseDown);
      userProfileButton.addEventListener("mouseup", handleMouseUp);
    }

    // Cleanup
    return () => {
      window.removeEventListener("scroll", handleScroll);
      if (userProfileButton) {
        userProfileButton.removeEventListener("mouseenter", handleMouseEnter);
        userProfileButton.removeEventListener("mouseleave", handleMouseLeave);
        userProfileButton.removeEventListener("mousedown", handleMouseDown);
        userProfileButton.removeEventListener("mouseup", handleMouseUp);
      }
    };
  }, []);

  useEffect(() => {
    // Check authentication status
    const checkAuth = async () => {
      try {
        // First try to use UserContext if available
        if (userContext && userContext.isAuthenticated) {
          setIsAuthenticated(true);
          if (userContext.user) {
            setUserName(
              `${userContext.user.first_name || ""} ${
                userContext.user.last_name || ""
              }`.trim()
            );
          }
          return;
        }

        // Fallback to token-based auth
        const token = localStorage.getItem("auth_token");
        if (token) {
          setIsAuthenticated(true);
          // Try to fetch user data
          try {
            const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY || "";
            const response = await fetch(
              `${import.meta.env.PUBLIC_BACKEND_URL}/store/customers/me`,
              {
                headers: {
                  "Content-Type": "application/json",
                  Authorization: `Bearer ${token}`,
                  "x-publishable-api-key": apiKey,
                },
                credentials: "include",
              }
            );

            if (response.ok) {
              const data = await response.json();
              if (data.customer) {
                setUserName(
                  `${data.customer.first_name || ""} ${
                    data.customer.last_name || ""
                  }`.trim()
                );
              }
            }
          } catch (error) {
            console.error("Error fetching user data:", error);
          }
        } else {
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.error("Authentication check failed:", error);
        setIsAuthenticated(false);
      }
    };

    checkAuth();

    // Add event listeners for clicks outside
    const handleClickOutside = (event: MouseEvent) => {
      if (
        dropdownRef.current &&
        !dropdownRef.current.contains(event.target as Node)
      ) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
    };
  }, []);

  const handleLogout = async () => {
    try {
      if (userContext && userContext.logout) {
        await userContext.logout();
      } else {
        // Fallback to manual token removal if context not available
        localStorage.removeItem("auth_token");
      }
      setIsAuthenticated(false);
      setUserName("");
      redirectToHomePage();
    } catch (error) {
      console.error("Logout error:", error);
      // Still redirect even if there's an error
      redirectToHomePage();
    }
  };

  const toggleDropdown = () => {
    setIsOpen(!isOpen);
  };

  const handleMouseEnter = () => {
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = null;
    }
    setIsOpen(true);
  };

  const handleMouseLeave = () => {
    timeoutRef.current = window.setTimeout(() => {
      setIsOpen(false);
    }, 200);
  };

  return (
    <div
      className="relative inline-block text-left"
      ref={dropdownRef}
      onMouseEnter={handleMouseEnter}
      onMouseLeave={handleMouseLeave}
    >
      <div
        className="flex items-center justify-center transition-all cursor-pointer group hover:text-[#285DA6]"
        onClick={toggleDropdown}
        aria-expanded={isOpen}
        aria-haspopup="true"
      >
        <button
          className="premium-icon-container w-[38px] h-[38px] rounded-full flex items-center justify-center transition-all duration-300"
          id="user-profile-button"
          style={{
            background: "transparent",
            borderWidth: "1px",
            borderStyle: "solid",
            borderColor: "rgba(255, 255, 255, 0.3)", // Default, will be updated in useEffect
          }}
        >
          {isAuthenticated && userName ? (
            <div
              id="user-avatar"
              className="w-full h-full rounded-full flex items-center justify-center text-sm font-medium"
              style={{
                textTransform: "uppercase",
                border: "0.5px white",
                backgroundColor: "transparent",
                color: isHomePage(window.location.pathname)
                  ? "white"
                  : "#285DA6",
                transition: "background-color 0.3s ease, color 0.3s ease",
              }}
            >
              {userName.charAt(0)}
            </div>
          ) : (
            <svg
              id="user-profile-icon"
              xmlns="http://www.w3.org/2000/svg"
              width="18"
              height="18"
              viewBox="0 0 24 24"
              fill="none"
              stroke="white" // Default, will be updated in useEffect
              strokeWidth="1.5"
              strokeLinecap="round"
              strokeLinejoin="round"
              className="user-icon"
              aria-hidden="true"
            >
              <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
              <circle cx="12" cy="7" r="4"></circle>
            </svg>
          )}
          <span className="sr-only">User profile</span>
        </button>
      </div>

      <div
        className={`absolute right-0 mt-2 w-64 bg-white/95 backdrop-blur-xl border border-gray-100 rounded-xl shadow-xl z-50 animate-slide-down ${
          isOpen ? "" : "hidden"
        }`}
        role="menu"
        aria-orientation="vertical"
        tabIndex={-1}
      >
        <div className="absolute inset-0 bg-gradient-to-b from-white/5 to-white/0 rounded-md pointer-events-none"></div>

        <ul role="menu" className="py-2 relative z-10">
          {isAuthenticated ? (
            <>
              <li>
                <a
                  href="/account"
                  className="flex items-center px-5 py-3 text-base font-karla hover:bg-[#285DA6]/10 hover:text-[#285DA6] transition-colors"
                  role="menuitem"
                  tabIndex={-1}
                >
                  <span className="mr-3 text-[#285DA6]">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"></path>
                      <circle cx="12" cy="7" r="4"></circle>
                    </svg>
                  </span>
                  My Account
                </a>
              </li>
              <li>
                <button
                  onClick={handleLogout}
                  className="flex items-center w-full text-left px-5 py-3 text-base font-karla hover:bg-[#285DA6]/10 hover:text-[#285DA6] transition-colors"
                  role="menuitem"
                  tabIndex={-1}
                >
                  <span className="mr-3 text-[#285DA6]">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                      <polyline points="16 17 21 12 16 7"></polyline>
                      <line x1="21" y1="12" x2="9" y2="12"></line>
                    </svg>
                  </span>
                  Sign Out
                </button>
              </li>
            </>
          ) : (
            <>
              <li>
                <a
                  href="/login"
                  className="flex items-center px-5 py-3 text-base font-karla hover:bg-[#285DA6]/10 hover:text-[#285DA6] transition-colors"
                  role="menuitem"
                  tabIndex={-1}
                >
                  <span className="mr-3 text-[#285DA6]">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M15 3h4a2 2 0 0 1 2 2v14a2 2 0 0 1-2 2h-4"></path>
                      <polyline points="10 17 15 12 10 7"></polyline>
                      <line x1="15" y1="12" x2="3" y2="12"></line>
                    </svg>
                  </span>
                  Sign In
                </a>
              </li>
              <li>
                <a
                  href="/register"
                  className="flex items-center px-5 py-3 text-base font-karla hover:bg-[#285DA6]/10 hover:text-[#285DA6] transition-colors"
                  role="menuitem"
                  tabIndex={-1}
                >
                  <span className="mr-3 text-[#285DA6]">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      width="18"
                      height="18"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="1.5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M16 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="8.5" cy="7" r="4"></circle>
                      <line x1="20" y1="8" x2="20" y2="14"></line>
                      <line x1="23" y1="11" x2="17" y2="11"></line>
                    </svg>
                  </span>
                  Register
                </a>
              </li>
            </>
          )}
        </ul>

        <div
          className="px-5 py-3 bg-blue-50 rounded-b-lg border-t border-gray-100"
          style={{
            backgroundColor: "#eff6ff",
            position: "relative",
            zIndex: 10,
          }}
        >
          {isAuthenticated && userName ? (
            <p
              className="text-sm text-center font-karla"
              style={{ color: "#4b5563" }}
            >
              Logged in as{" "}
              <span className="font-medium" style={{ color: "#285DA6" }}>
                {userName}
              </span>
            </p>
          ) : (
            <p
              className="text-sm text-center font-karla"
              style={{ color: "#4b5563" }}
            >
              Join Perfect Piste for exclusive benefits
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SimpleDropdown;
