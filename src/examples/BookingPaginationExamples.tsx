import React, { useState, useEffect } from "react";
import {
  fetchCustomerBookings,
  buildBookingQueryFromSearchParams,
  type BookingQueryParams,
  type Booking,
  type BookingApiResponse
} from "../utils/store/bookings";

/**
 * Example component demonstrating different booking API pagination patterns
 * 
 * This component shows how to use the enhanced booking API with:
 * - Basic pagination (limit and page)
 * - Filtering by hotel_id and status
 * - Sorting by different fields
 * - URL parameter integration
 */
const BookingPaginationExamples: React.FC = () => {
  const [bookings, setBookings] = useState<Booking[]>([]);
  const [totalCount, setTotalCount] = useState(0);
  const [upcomingTripsCount, setUpcomingTripsCount] = useState(0);
  const [currentLimit, setCurrentLimit] = useState(0);
  const [currentOffset, setCurrentOffset] = useState(0);
  const [hasMore, setHasMore] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Example 1: Basic pagination with default sorting
  const fetchBasicPagination = async () => {
    setLoading(true);
    setError(null);

    try {
      // Basic pagination with default sorting (latest first) - GET /api/store/hotel-management/bookings?limit=10&offset=0&sort_by=check_in_date&sort_order=desc
      const result: BookingApiResponse = await fetchCustomerBookings({
        limit: 10,
        offset: 0
        // No sorting params specified - will default to sort_by=check_in_date&sort_order=desc
      });

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("Basic pagination result:", result);
      console.log("Ongoing trips count:", result.active_trips_count);
    } catch (err) {
      setError("Failed to fetch bookings with basic pagination");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Example 2: Filter by hotel and sort by check-in date
  const fetchFilteredAndSorted = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Filter by hotel and sort - GET /api/store/hotel-management/bookings?hotel_id=hotel_123&sort_by=check_in_date&sort_order=asc&limit=10&offset=0
      const result: BookingApiResponse = await fetchCustomerBookings({
        hotel_id: "hotel_123",
        sort_by: "check_in_date",
        sort_order: "asc",
        limit: 10,
        offset: 0
      });

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("Filtered and sorted result:", result);
    } catch (err) {
      setError("Failed to fetch filtered and sorted bookings");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Example 3: Filter by status with pagination using offset
  const fetchStatusFilterWithOffset = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Filter by status with offset - GET /api/store/hotel-management/bookings?status=confirmed&limit=20&offset=40
      const result: BookingApiResponse = await fetchCustomerBookings({
        status: "confirmed",
        limit: 20,
        offset: 40 // This is equivalent to page 3 (40/20 + 1)
      });

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("Status filter with offset result:", result);
    } catch (err) {
      setError("Failed to fetch bookings with status filter");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Example 4: Complex query with all parameters
  const fetchComplexQuery = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Complex query with all parameters
      const result: BookingApiResponse = await fetchCustomerBookings({
        limit: 15,
        offset: 15, // Page 2 with limit 15
        hotel_id: "grand-hotel",
        status: "confirmed",
        sort_by: "total_amount",
        sort_order: "desc"
      });

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("Complex query result:", result);
    } catch (err) {
      setError("Failed to fetch bookings with complex query");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Example 5: Build query from URL parameters
  const fetchFromURLParams = async () => {
    setLoading(true);
    setError(null);
    
    try {
      // Simulate URL search params
      const urlParams = new URLSearchParams("?limit=10&offset=0&status=pending&sort_by=check_in_date&sort_order=desc");
      const queryParams = buildBookingQueryFromSearchParams(urlParams);

      const result: BookingApiResponse = await fetchCustomerBookings(queryParams);

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("URL params query:", queryParams);
      console.log("URL params result:", result);
    } catch (err) {
      setError("Failed to fetch bookings from URL params");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Example 6: Explicit descending sort (latest first)
  const fetchExplicitDescending = async () => {
    setLoading(true);
    setError(null);

    try {
      // Explicit descending order (latest first) - GET /api/store/hotel-management/bookings?sort_order=desc
      const result: BookingApiResponse = await fetchCustomerBookings({
        sort_order: "desc" // Explicit descending order
      });

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("Explicit descending sort result:", result);
    } catch (err) {
      setError("Failed to fetch bookings with explicit descending sort");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  // Example 7: Explicit ascending sort (oldest first)
  const fetchExplicitAscending = async () => {
    setLoading(true);
    setError(null);

    try {
      // Explicit ascending order (oldest first) - GET /api/store/hotel-management/bookings?sort_order=asc
      const result: BookingApiResponse = await fetchCustomerBookings({
        sort_order: "asc" // Explicit ascending order
      });

      setBookings(result.bookings);
      setTotalCount(result.count);
      setUpcomingTripsCount(result.upcoming_trips_count);
      setCurrentLimit(result.limit);
      setCurrentOffset(result.offset);
      setHasMore(result.has_more);
      console.log("Explicit ascending sort result:", result);
    } catch (err) {
      setError("Failed to fetch bookings with explicit ascending sort");
      console.error(err);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Booking API Pagination Examples</h1>
      
      {/* Example Buttons */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mb-8">
        <button
          onClick={fetchBasicPagination}
          disabled={loading}
          className="p-4 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50"
        >
          Basic Pagination
          <div className="text-xs mt-1 opacity-80">limit=10&offset=0</div>
        </button>
        
        <button
          onClick={fetchFilteredAndSorted}
          disabled={loading}
          className="p-4 bg-green-500 text-white rounded-lg hover:bg-green-600 disabled:opacity-50"
        >
          Filter & Sort
          <div className="text-xs mt-1 opacity-80">hotel_id + sort_by + limit/offset</div>
        </button>
        
        <button
          onClick={fetchStatusFilterWithOffset}
          disabled={loading}
          className="p-4 bg-purple-500 text-white rounded-lg hover:bg-purple-600 disabled:opacity-50"
        >
          Status Filter + Offset
          <div className="text-xs mt-1 opacity-80">status + offset=40</div>
        </button>
        
        <button
          onClick={fetchComplexQuery}
          disabled={loading}
          className="p-4 bg-orange-500 text-white rounded-lg hover:bg-orange-600 disabled:opacity-50"
        >
          Complex Query
          <div className="text-xs mt-1 opacity-80">limit=15&offset=15</div>
        </button>
        
        <button
          onClick={fetchFromURLParams}
          disabled={loading}
          className="p-4 bg-red-500 text-white rounded-lg hover:bg-red-600 disabled:opacity-50"
        >
          From URL Params
          <div className="text-xs mt-1 opacity-80">limit=10&offset=0&status=pending</div>
        </button>

        <button
          onClick={fetchExplicitDescending}
          disabled={loading}
          className="p-4 bg-indigo-500 text-white rounded-lg hover:bg-indigo-600 disabled:opacity-50"
        >
          Explicit Desc Sort
          <div className="text-xs mt-1 opacity-80">sort_order=desc (latest first)</div>
        </button>

        <button
          onClick={fetchExplicitAscending}
          disabled={loading}
          className="p-4 bg-pink-500 text-white rounded-lg hover:bg-pink-600 disabled:opacity-50"
        >
          Explicit Asc Sort
          <div className="text-xs mt-1 opacity-80">sort_order=asc (oldest first)</div>
        </button>
      </div>

      {/* Loading State */}
      {loading && (
        <div className="text-center py-8">
          <div className="inline-block w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin"></div>
          <p className="mt-2 text-gray-600">Loading bookings...</p>
        </div>
      )}

      {/* Error State */}
      {error && (
        <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
          <p className="text-red-700">{error}</p>
        </div>
      )}

      {/* Results */}
      {!loading && !error && (
        <div className="bg-gray-50 rounded-lg p-6">
          <h2 className="text-lg font-semibold mb-4">
            API Response Summary
          </h2>

          <div className="mb-4 text-sm bg-blue-50 p-3 rounded border">
            <h3 className="font-medium text-blue-800 mb-2">Pagination Info:</h3>
            <p><strong>Total Bookings (count):</strong> {totalCount}</p>
            <p><strong>Upcoming Trips:</strong> {upcomingTripsCount}</p>
            <p><strong>Current Page Items:</strong> {bookings.length}</p>
            <p><strong>Limit:</strong> {currentLimit} | <strong>Offset:</strong> {currentOffset}</p>
            <p><strong>Has More Pages:</strong> {hasMore ? 'Yes' : 'No'}</p>
          </div>
          
          {bookings.length > 0 ? (
            <div className="space-y-4">
              {bookings.map((booking) => (
                <div key={booking.id} className="bg-white p-4 rounded-lg border">
                  <div className="flex justify-between items-start">
                    <div>
                      <h3 className="font-medium">{booking.hotel_name}</h3>
                      <p className="text-sm text-gray-600">{booking.room_type}</p>
                      <p className="text-sm text-gray-500">
                        {booking.check_in_date} to {booking.check_out_date}
                      </p>
                    </div>
                    <div className="text-right">
                      <p className="font-medium">
                        {booking.total_amount} {booking.currency_code}
                      </p>
                      <span className={`inline-block px-2 py-1 text-xs rounded-full ${
                        booking.status === 'confirmed' ? 'bg-green-100 text-green-800' :
                        booking.status === 'pending' ? 'bg-yellow-100 text-yellow-800' :
                        'bg-red-100 text-red-800'
                      }`}>
                        {booking.status}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          ) : (
            <p className="text-gray-500">No bookings found</p>
          )}
        </div>
      )}

      {/* API Examples Documentation */}
      <div className="mt-8 bg-gray-100 rounded-lg p-6">
        <h2 className="text-lg font-semibold mb-4">API Examples</h2>
        <div className="space-y-4 text-sm">
          <div>
            <h3 className="font-medium">Default call (latest first):</h3>
            <code className="block bg-white p-2 rounded mt-1">
              GET /api/store/hotel-management/bookings
            </code>
            <p className="text-xs text-gray-600 mt-1">Defaults to sort_by=check_in_date&sort_order=desc</p>
          </div>

          <div>
            <h3 className="font-medium">Explicit descending order (latest first):</h3>
            <code className="block bg-white p-2 rounded mt-1">
              GET /api/store/hotel-management/bookings?sort_order=desc
            </code>
          </div>

          <div>
            <h3 className="font-medium">Explicit ascending order (oldest first):</h3>
            <code className="block bg-white p-2 rounded mt-1">
              GET /api/store/hotel-management/bookings?sort_order=asc
            </code>
          </div>

          <div>
            <h3 className="font-medium">Basic pagination with default sorting:</h3>
            <code className="block bg-white p-2 rounded mt-1">
              GET /api/store/hotel-management/bookings?limit=10&offset=0
            </code>
          </div>

          <div>
            <h3 className="font-medium">Filter by hotel and sort by check-in date:</h3>
            <code className="block bg-white p-2 rounded mt-1">
              GET /api/store/hotel-management/bookings?hotel_id=hotel_123&sort_by=check_in_date&sort_order=asc&limit=10&offset=0
            </code>
          </div>

          <div>
            <h3 className="font-medium">Filter by status with pagination:</h3>
            <code className="block bg-white p-2 rounded mt-1">
              GET /api/store/hotel-management/bookings?status=confirmed&limit=20&offset=40
            </code>
          </div>
        </div>
      </div>
    </div>
  );
};

export default BookingPaginationExamples;
