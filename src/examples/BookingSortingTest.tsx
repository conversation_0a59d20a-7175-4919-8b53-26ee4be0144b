import React, { useState } from "react";
import { fetchCustomerBookings, type BookingApiResponse } from "../utils/store/bookings";

/**
 * Test component to demonstrate the new default sorting behavior for hotel management bookings API
 * 
 * This component tests the following sorting behavior:
 * - Default call: GET /api/store/hotel-management/bookings (should show latest first)
 * - Explicit descending: GET /api/store/hotel-management/bookings?sort_order=desc
 * - Explicit ascending: GET /api/store/hotel-management/bookings?sort_order=asc
 */
const BookingSortingTest: React.FC = () => {
  const [results, setResults] = useState<{
    default?: BookingApiResponse;
    explicit_desc?: BookingApiResponse;
    explicit_asc?: BookingApiResponse;
  }>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const runSortingTests = async () => {
    setLoading(true);
    setError(null);
    setResults({});

    try {
      console.log("🧪 Running booking sorting tests...");

      // Test 1: Default call (should default to desc)
      console.log("📅 Test 1: Default call (no sort parameters)");
      const defaultResult = await fetchCustomerBookings({
        limit: 5 // Small limit for testing
      });
      console.log("Default result:", defaultResult);

      // Test 2: Explicit descending order
      console.log("📅 Test 2: Explicit descending order");
      const explicitDescResult = await fetchCustomerBookings({
        limit: 5,
        sort_order: "desc"
      });
      console.log("Explicit desc result:", explicitDescResult);

      // Test 3: Explicit ascending order
      console.log("📅 Test 3: Explicit ascending order");
      const explicitAscResult = await fetchCustomerBookings({
        limit: 5,
        sort_order: "asc"
      });
      console.log("Explicit asc result:", explicitAscResult);

      setResults({
        default: defaultResult,
        explicit_desc: explicitDescResult,
        explicit_asc: explicitAscResult,
      });

      console.log("✅ All sorting tests completed successfully!");

    } catch (err) {
      console.error("❌ Sorting tests failed:", err);
      setError(err instanceof Error ? err.message : "Unknown error occurred");
    } finally {
      setLoading(false);
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  return (
    <div className="p-6 max-w-6xl mx-auto">
      <h1 className="text-2xl font-bold mb-6">Booking Sorting Behavior Test</h1>
      
      <div className="mb-6 bg-blue-50 border border-blue-200 rounded-lg p-4">
        <h2 className="text-lg font-semibold text-blue-800 mb-2">Expected Behavior:</h2>
        <ul className="text-sm text-blue-700 space-y-1">
          <li><strong>Default call:</strong> GET /api/store/hotel-management/bookings → Latest first (desc)</li>
          <li><strong>Explicit desc:</strong> GET /api/store/hotel-management/bookings?sort_order=desc → Latest first</li>
          <li><strong>Explicit asc:</strong> GET /api/store/hotel-management/bookings?sort_order=asc → Oldest first</li>
        </ul>
      </div>

      <button
        onClick={runSortingTests}
        disabled={loading}
        className="mb-6 px-6 py-3 bg-blue-500 text-white rounded-lg hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed"
      >
        {loading ? "Running Tests..." : "Run Sorting Tests"}
      </button>

      {error && (
        <div className="mb-6 bg-red-50 border border-red-200 rounded-lg p-4">
          <p className="text-red-700">Error: {error}</p>
        </div>
      )}

      {Object.keys(results).length > 0 && (
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
          {/* Default Result */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 text-green-700">
              Default Call
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              No sort parameters (should default to desc)
            </p>
            {results.default && (
              <div className="space-y-2">
                <p className="text-xs text-gray-500">
                  Total: {results.default.count} bookings
                </p>
                {results.default.bookings.slice(0, 3).map((booking, index) => (
                  <div key={booking.id} className="text-sm border-l-2 border-green-500 pl-2">
                    <p className="font-medium">{booking.hotel_name}</p>
                    <p className="text-gray-600">Check-in: {formatDate(booking.check_in_date)}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Explicit Desc Result */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 text-blue-700">
              Explicit Desc
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              sort_order=desc (latest first)
            </p>
            {results.explicit_desc && (
              <div className="space-y-2">
                <p className="text-xs text-gray-500">
                  Total: {results.explicit_desc.count} bookings
                </p>
                {results.explicit_desc.bookings.slice(0, 3).map((booking, index) => (
                  <div key={booking.id} className="text-sm border-l-2 border-blue-500 pl-2">
                    <p className="font-medium">{booking.hotel_name}</p>
                    <p className="text-gray-600">Check-in: {formatDate(booking.check_in_date)}</p>
                  </div>
                ))}
              </div>
            )}
          </div>

          {/* Explicit Asc Result */}
          <div className="bg-white border border-gray-200 rounded-lg p-4">
            <h3 className="text-lg font-semibold mb-3 text-purple-700">
              Explicit Asc
            </h3>
            <p className="text-sm text-gray-600 mb-3">
              sort_order=asc (oldest first)
            </p>
            {results.explicit_asc && (
              <div className="space-y-2">
                <p className="text-xs text-gray-500">
                  Total: {results.explicit_asc.count} bookings
                </p>
                {results.explicit_asc.bookings.slice(0, 3).map((booking, index) => (
                  <div key={booking.id} className="text-sm border-l-2 border-purple-500 pl-2">
                    <p className="font-medium">{booking.hotel_name}</p>
                    <p className="text-gray-600">Check-in: {formatDate(booking.check_in_date)}</p>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>
      )}

      {/* Verification Section */}
      {Object.keys(results).length > 0 && (
        <div className="mt-8 bg-gray-50 border border-gray-200 rounded-lg p-4">
          <h3 className="text-lg font-semibold mb-3">Verification</h3>
          <div className="space-y-2 text-sm">
            {results.default && results.explicit_desc && (
              <p className={`${
                JSON.stringify(results.default.bookings) === JSON.stringify(results.explicit_desc.bookings)
                  ? "text-green-700" : "text-red-700"
              }`}>
                ✓ Default and explicit desc should return same results: {
                  JSON.stringify(results.default.bookings) === JSON.stringify(results.explicit_desc.bookings)
                    ? "PASS" : "FAIL"
                }
              </p>
            )}
            {results.explicit_desc && results.explicit_asc && (
              <p className={`${
                JSON.stringify(results.explicit_desc.bookings) !== JSON.stringify(results.explicit_asc.bookings)
                  ? "text-green-700" : "text-red-700"
              }`}>
                ✓ Desc and asc should return different order: {
                  JSON.stringify(results.explicit_desc.bookings) !== JSON.stringify(results.explicit_asc.bookings)
                    ? "PASS" : "FAIL"
                }
              </p>
            )}
          </div>
        </div>
      )}
    </div>
  );
};

export default BookingSortingTest;
