import { useRef, useCallback } from 'react';

interface TypingAnimationOptions {
  phrases: string[];
  baseSpeed?: number;
  speedVariation?: number;
  initialDelay?: number;
  onTypingComplete?: () => void;
}

export const useTypingAnimation = () => {
  const typingTimeoutRef = useRef<number | null>(null);
  const isTypingRef = useRef(false);

  const startTyping = useCallback((
    inputElement: HTMLInputElement | HTMLTextAreaElement,
    options: TypingAnimationOptions
  ) => {
    if (!inputElement || isTypingRef.current) return;

    const {
      phrases,
      baseSpeed = 80,
      speedVariation = 40,
      initialDelay = 500,
      onTypingComplete
    } = options;

    // Clear any existing typing
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Randomly select a phrase
    const textToType = phrases[Math.floor(Math.random() * phrases.length)];
    let currentIndex = 0;
    isTypingRef.current = true;

    // Clear input and focus
    inputElement.value = '';
    inputElement.focus();

    // Function to get random typing speed
    const getTypingSpeed = () => {
      return Math.floor(Math.random() * speedVariation) + (baseSpeed - speedVariation / 2);
    };

    // Add typing class for cursor animation
    inputElement.classList.add('typing');

    // Function to type the next character
    const typeNextChar = () => {
      if (currentIndex < textToType.length) {
        inputElement.value = textToType.substring(0, currentIndex + 1);
        
        // Trigger input event to update React state
        const event = new Event('input', { bubbles: true });
        inputElement.dispatchEvent(event);
        
        currentIndex++;

        // Schedule next character
        typingTimeoutRef.current = window.setTimeout(
          typeNextChar,
          getTypingSpeed()
        );
      } else {
        // Typing finished
        isTypingRef.current = false;
        inputElement.classList.remove('typing');
        
        if (typingTimeoutRef.current) {
          clearTimeout(typingTimeoutRef.current);
          typingTimeoutRef.current = null;
        }

        // Call completion callback
        if (onTypingComplete) {
          onTypingComplete();
        }
      }
    };

    // Start typing with initial delay
    typingTimeoutRef.current = window.setTimeout(typeNextChar, initialDelay);
  }, []);

  const stopTyping = useCallback((inputElement?: HTMLInputElement | HTMLTextAreaElement) => {
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
      typingTimeoutRef.current = null;
    }
    
    isTypingRef.current = false;
    
    if (inputElement) {
      inputElement.classList.remove('typing');
    }
  }, []);

  const clearTyping = useCallback((inputElement: HTMLInputElement | HTMLTextAreaElement) => {
    stopTyping(inputElement);
    inputElement.value = '';
    
    // Trigger input event to update React state
    const event = new Event('input', { bubbles: true });
    inputElement.dispatchEvent(event);
  }, [stopTyping]);

  return {
    startTyping,
    stopTyping,
    clearTyping,
    isTyping: isTypingRef.current
  };
};
