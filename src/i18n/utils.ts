import { ui, default<PERSON>ang, type <PERSON> } from "./ui";

// Supported languages array for easy access
export const SUPPORTED_LANGUAGES: Lang[] = Object.keys(ui) as Lang[];

export function getLangFromUrl(url: URL): Lang {
  const [, lang] = url.pathname.split("/");
  if (lang in ui) return lang as Lang;
  return defaultLang;
}

export function useTranslations(lang: Lang) {
  return function t(key: keyof (typeof ui)[typeof defaultLang]) {
    return ui[lang][key] || ui[defaultLang][key];
  };
}

export function getStaticPathsForLocales() {
  return Object.keys(ui)
    .filter((lang) => lang !== defaultLang)
    .map((lang) => ({
      params: { lang },
    }));
}

// Helper to get all supported languages
export function getSupportedLanguages(): Lang[] {
  return Object.keys(ui) as Lang[];
}

// Helper to check if a language is supported
export function isValidLang(lang: string): lang is Lang {
  return lang in ui;
}

// Helper to check if a path is a home page (including translated versions)
export function isHomePage(pathname: string): boolean {
  // Check if it's the default home page
  if (pathname === "/") return true;

  // Check if it's a translated home page
  const segments = pathname.split("/").filter(Boolean);
  if (
    segments.length === 1 &&
    SUPPORTED_LANGUAGES.includes(segments[0] as Lang)
  ) {
    return true;
  }

  return false;
}

// Helper to redirect to home page in the current language context
export function redirectToHomePage(currentPath?: string): void {
  const path =
    currentPath ||
    (typeof window !== "undefined" ? window.location.pathname : "/");
  const segments = path.split("/").filter(Boolean);

  if (
    segments.length > 0 &&
    SUPPORTED_LANGUAGES.includes(segments[0] as Lang)
  ) {
    // Redirect to translated home page
    window.location.href = `/${segments[0]}/`;
  } else {
    // Redirect to default home page
    window.location.href = "/";
  }
}

// Helper function to build language-aware URLs
export function buildUrl(path: string, currentLang: Lang): string {
  if (currentLang === defaultLang) {
    return path;
  }
  return `/${currentLang}${path}`;
}
