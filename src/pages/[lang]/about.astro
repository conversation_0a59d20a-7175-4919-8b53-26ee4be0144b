---
import Layout from "../../components/layout/Layout.astro";
import AboutHero from "../../components/about/AboutHero.astro";
import OurStory from "../../components/about/OurStory.astro";
import OurValues from "../../components/about/OurValues.astro";
import TeamSection from "../../components/about/TeamSection.astro";
import Partners from "../../components/about/Partners.astro";
import CTASection from "../../components/home/<USER>";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
  buildUrl,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);
---

<Layout title={t("about.hero.title")} description={t("about.story.paragraph1")}>
  <!-- Hero Section -->
  <AboutHero
    title={t("about.hero.title")}
    subtitle={t("about.hero.subtitle")}
    imageUrl="https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
  />

  <!-- Our Story -->
  <OurStory
    imageUrl="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80"
    lang={lang}
  />

  <!-- Our Values -->
  <OurValues lang={lang} />

  <!-- Team Section -->
  <TeamSection lang={lang} />

  <!-- Partners Section -->
  <Partners lang={lang} />

  <!-- CTA Section -->
  <CTASection
    title={t("about.cta.title")}
    description={t("about.cta.description")}
    ctaText={t("about.cta.button")}
    ctaLink={buildUrl("/contact", lang)}
    backgroundImage="https://images.unsplash.com/photo-1551524559-8af4e6624178?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
