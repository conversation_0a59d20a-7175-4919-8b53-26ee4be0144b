---
import Layout from "../../components/layout/Layout.astro";
import ContactHero from "../../components/contact/ContactHero.astro";
import ContactFormSection from "../../components/contact/ContactFormSection.astro";
import MapSection from "../../components/contact/MapSection.astro";
import ListPropertiesCTA from "../../components/contact/ListPropertiesCTA.astro";
import ContactCTA from "../../components/contact/ContactCTA.astro";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Define contact information
const contactInfo = {
  address: {
    company: "Perfect Piste Headquarters",
    street: "1234 Luxury Avenue",
    city: "Zurich, Switzerland 8001",
  },
  phone: {
    general: "+41 22 345 67 89",
    concierge: "+41 22 345 67 90",
  },
  email: {
    general: "<EMAIL>",
    reservations: "<EMAIL>",
  },
  hours: {
    weekdays: "9:00 AM - 6:00 PM (CET)",
    saturday: "10:00 AM - 4:00 PM (CET)",
    sunday: "Closed",
  },
};
---

<Layout
  title={t("contact.hero.title")}
  description={t("contact.hero.subtitle")}
>
  <!-- Hero Section -->
  <ContactHero
    title={t("contact.hero.title")}
    subtitle={t("contact.hero.subtitle")}
    imageUrl="https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
    ctaText={t("contact.form.submit")}
  />

  <!-- Contact Form & Details Section -->
  <ContactFormSection contactInfo={contactInfo} lang={lang} />

  <!-- List Properties CTA -->
  <div class="container-custom py-10">
    <div class="mx-auto">
      <ListPropertiesCTA
        title={t("contact.listProperties.title")}
        description={t("contact.listProperties.description")}
        buttonText={t("contact.listProperties.button")}
      />
    </div>
  </div>

  <!-- Map Section -->
  <MapSection height="h-96" />

  <!-- Premium CTA Section -->
  <ContactCTA
    title={t("contact.cta.title")}
    subtitle={t("contact.cta.subtitle")}
    backgroundImage="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
