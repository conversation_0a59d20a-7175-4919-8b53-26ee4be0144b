---
import Layout from "../../components/layout/Layout.astro";
import FAQHero from "../../components/faq/FAQHero.astro";
import FAQSection from "../../components/faq/FAQSection.astro";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Page metadata
const title = `${t("faq.hero.title")} - Perfect Piste`;
const description = t("faq.hero.subtitle");
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <FAQHero
    title={t("faq.hero.title")}
    subtitle={t("faq.hero.subtitle")}
    lang={lang}
  />

  <!-- FAQ Section -->
  <FAQSection lang={lang} />
</Layout>
