---
import Layout from "../../components/layout/Layout.astro";
import VideoHeroSection from "../../components/home/<USER>";
import CategorySection from "../../components/home/<USER>";
import FeaturedStaysSection from "../../components/home/<USER>";
import CTASection from "../../components/home/<USER>";
import WhyPerfectPisteSection from "../../components/home/<USER>";
import PerfectPisteAmenitiesSection from "../../components/home/<USER>";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Define video sources for different destinations
const videoSources = [
  {
    src: "/videos/hero/skiing-1.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-2.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-3.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-4.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-5.mp4",
    type: "video/mp4",
  },
  {
    src: "/videos/hero/skiing-6.mp4",
    type: "video/mp4",
  },
];

// Define ski destinations to showcase
const skiDestinations = [
  "Zermatt",
  "St. Moritz",
  "Verbier",
  "Courchevel",
  "Aspen",
];
---

<Layout title={t("home.hero.title")} description={t("home.hero.description")}>
  <main>
    <VideoHeroSection
      client:load
      title={t("home.hero.mainTitle")}
      subtitle={t("home.hero.subtitle")}
      videoSources={videoSources}
      destinations={skiDestinations}
    />
    <div class="py-16 mt-16">
      <WhyPerfectPisteSection lang={lang} />
    </div>
    <div class="py-16">
      <CategorySection lang={lang} />
    </div>
    <div class="py-16" id="how-to-use-ai">
      <PerfectPisteAmenitiesSection lang={lang} />
    </div>
    <div class="py-16">
      <FeaturedStaysSection lang={lang} />
    </div>
  </main>

  <div class="pt-16 pb-32">
    <CTASection
      title={t("home.cta.title")}
      description={t("home.cta.description")}
      ctaText={t("home.cta.primaryButton")}
      ctaLink="/ai-search"
    />
  </div>
</Layout>
