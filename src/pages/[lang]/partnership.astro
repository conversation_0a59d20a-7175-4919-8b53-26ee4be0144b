---
import Layout from "../../components/layout/Layout.astro";
import PartnershipHero from "../../components/partnership/PartnershipHero.astro";
import PartnershipOverview from "../../components/partnership/PartnershipOverview.astro";
import PartnershipTestimonials from "../../components/partnership/PartnershipTestimonials.astro";
import PartnershipApplication from "../../components/partnership/PartnershipApplication.astro";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Page metadata
const title = `${t("partnership.hero.title")} - Perfect Piste`;
const description = t("partnership.hero.subtitle");
---

<Layout
  title={title}
  description={description}
>
  <main class="min-h-screen bg-background">
    <!-- Hero Section -->
    <PartnershipHero
      title={t("partnership.hero.title")}
      subtitle={t("partnership.hero.subtitle")}
      imageUrl="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
      lang={lang}
    />

    <div class="px-0 md:px-8">
    <!-- Partnership Overview -->
    <PartnershipOverview lang={lang} />
    
    <!-- Partner Testimonials -->
    <PartnershipTestimonials lang={lang} />
    
    <!-- Application Process -->
    <PartnershipApplication lang={lang} />
  </div>
  </main>
</Layout>
