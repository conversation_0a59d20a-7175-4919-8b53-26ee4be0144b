---
import Layout from "../../components/layout/Layout.astro";
import FeaturedHotelsWithModal from "../../components/home/<USER>";
import ShareModalController from "../../components/share/ShareModalController";
import { fetchFeaturedDestinations } from "../../utils/store/destinations";
import DestinationCard from "../../components/destinations/DestinationCard.astro";

import {
  getLangFromUrl,
  useTranslations,
  getStaticPathsForLocales,
} from "../../i18n/utils";

// Generate static paths for non-default languages
export function getStaticPaths() {
  return getStaticPathsForLocales();
}

// Get language from URL and set up translations
const lang = getLangFromUrl(Astro.url);
const t = useTranslations(lang);

// Define page metadata
const title = `${t("search.hero.title")} - Perfect Piste`;
const description = t("search.hero.subtitle");

// Fetch featured destinations
const featuredDestinations = await fetchFeaturedDestinations();
---

<Layout title={title} description={description}>
  <!-- Search Hero Section -->
  <section class="py-20 bg-gradient-to-b from-gray-50 to-white">
    <div class="container-custom">
      <div class="text-center max-w-4xl mx-auto">
        <h1 class="text-4xl md:text-5xl font-baskervville mb-6">
          {t("search.hero.title")}
        </h1>
        <p class="text-xl text-gray-600 mb-8">
          {t("search.hero.subtitle")}
        </p>
      </div>
    </div>
  </section>

  <!-- Featured Hotels Section -->
  <section class="py-16">
    <div class="container-custom">
      <h2 class="text-3xl font-baskervville mb-8 text-center">
        {t("home.featuredStays.title")}
      </h2>
      <FeaturedHotelsWithModal client:load />
    </div>
  </section>

  <!-- Featured Destinations Section -->
  {featuredDestinations && featuredDestinations.length > 0 && (
    <section class="py-16 bg-gray-50">
      <div class="container-custom">
        <h2 class="text-3xl font-baskervville mb-8 text-center">
          {t("destinations.hero.title")}
        </h2>
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {featuredDestinations.slice(0, 6).map((destination) => (
            <DestinationCard
              id={destination.id}
              name={destination.name}
              propertyCount={destination.property_count || 0}
              imageUrl={destination.image_url}
              category={destination.category}
              description={destination.description}
              searchPrompt={destination.search_prompt}
            />
          ))}
        </div>
      </div>
    </section>
  )}

  <!-- Share Modal Controller -->
  <ShareModalController client:load />
</Layout>
