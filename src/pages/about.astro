---
import Layout from "../components/layout/Layout.astro";

// Import About page components
import AboutHero from "../components/about/AboutHero.astro";
import OurStory from "../components/about/OurStory.astro";
import OurValues from "../components/about/OurValues.astro";
import TeamSection from "../components/about/TeamSection.astro";
import Partners from "../components/about/Partners.astro";
import CTASection from "../components/home/<USER>";

import { useTranslations } from "../i18n/utils";
import { defaultLang } from "../i18n/ui";

// Get translations for default language (English)
const t = useTranslations(defaultLang);
---

<Layout title={t("about.hero.title")} description={t("about.story.paragraph1")}>
  <!-- Hero Section -->
  <AboutHero
    title={t("about.hero.title")}
    subtitle={t("about.hero.subtitle")}
    imageUrl="https://images.unsplash.com/photo-1596394516093-501ba68a0ba6?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
  />

  <!-- Our Story -->
  <OurStory
    imageUrl="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1080&q=80"
    lang={defaultLang}
  />

  <!-- Our Values -->
  <OurValues lang={defaultLang} />

  <!-- Team Section -->
  <TeamSection lang={defaultLang} />

  <!-- Partners Section -->
  <Partners lang={defaultLang} />

  <!-- CTA Section -->
  <CTASection
    title={t("about.cta.title")}
    description={t("about.cta.description")}
    ctaText={t("about.cta.button")}
    ctaLink="/contact"
    backgroundImage="https://images.unsplash.com/photo-1551524559-8af4e6624178?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
