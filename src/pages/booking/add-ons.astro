---
import Layout from "../../components/layout/Layout.astro";
import AddOnsPageWrapper from "../../components/booking/AddOnsPageWrapper";

// The booking data will be passed from client-side JavaScript
// Default values are provided as fallbacks
const title = "Add-ons & Services - Perfect Piste";
const description = "Enhance your stay with our premium add-ons and services.";
---

<Layout title={title} description={description}>
  <div class="bg-white min-h-[calc(100vh-80px)]">
    <div class="container-custom py-8">
      <div class="mb-4 mx-auto">
        <h1
          class="text-xl font-karla uppercase tracking-wider mb-2 text-[#3566ab]"
        >
          Add-ons & Services
        </h1>
        <p class="text-gray-600 text-sm">
          Enhance your stay with our premium services and experiences
        </p>
      </div>

      <AddOnsPageWrapper client:load />
    </div>
  </div>
</Layout>
