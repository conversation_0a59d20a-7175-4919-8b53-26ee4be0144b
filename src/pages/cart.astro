---
import Layout from "../components/layout/Layout.astro";
import { <PERSON><PERSON> } from "../components/ui/button";

// Get the cart data from localStorage on the client side
// This will be populated by client-side JavaScript
---

<Layout
  title="Your Cart - Perfect Piste"
  description="Review your selected accommodations and complete your booking."
>
  <div class="bg-white min-h-[calc(100vh-80px)]">
    <div class="container-custom py-16">
      <div class="max-w-6xl mx-auto">
        <!-- Page Header -->
        <div class="mb-12">
          <div class="flex items-center mb-4">
            <button
              id="back-button"
              class="flex items-center mr-4 p-2 text-sm text-[#285DA6] hover:bg-[#285DA6]/5 border border-[#285DA6]/20 rounded-lg transition-colors duration-300"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="16"
                height="16"
                viewBox="0 0 24 24"
                fill="none"
                stroke="currentColor"
                stroke-width="2"
                stroke-linecap="round"
                stroke-linejoin="round"
              >
                <line x1="19" y1="12" x2="5" y2="12"></line>
                <polyline points="12 19 5 12 12 5"></polyline>
              </svg>
            </button>
            <h2
              class="font-baskervville text-2xl uppercase tracking-[0.1em] text-[#000000]"
            >
              REVIEW BOOKING
            </h2>
          </div>
          <p class="font-baskervville text-base text-[#000000]/80 max-w-3xl">
            Review your selected accommodations and complete your booking.
          </p>
        </div>

        <!-- Cart Content -->
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
          <!-- Cart Items -->
          <div class="lg:col-span-2">
            <!-- Empty Cart State -->
            <div
              id="empty-cart"
              class="bg-white border border-[#285DA6]/10 rounded-lg p-8 text-center"
            >
              <div
                class="w-16 h-16 bg-[#285DA6]/5 rounded-full flex items-center justify-center mx-auto mb-4"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  stroke-width="2"
                  stroke-linecap="round"
                  stroke-linejoin="round"
                  class="text-[#285DA6]"
                >
                  <circle cx="9" cy="21" r="1"></circle>
                  <circle cx="20" cy="21" r="1"></circle>
                  <path
                    d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"
                  ></path>
                </svg>
              </div>
              <h3 class="text-xl font-medium mb-2">Your cart is empty</h3>
              <p class="text-muted-foreground mb-6">
                Browse our selection of luxury ski accommodations to find your
                perfect stay.
              </p>
              <a
                href="/stays"
                class="inline-block py-2.5 px-6 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300"
              >
                Browse Stays
              </a>
            </div>

            <!-- Cart Items Template (will be populated by JS) -->
            <div id="cart-items-container" class="hidden space-y-4">
              <!-- Items will be inserted here by JavaScript -->
            </div>
          </div>

          <!-- Order Summary -->
          <div class="lg:col-span-1">
            <div
              class="bg-white border border-[#285DA6]/10 rounded-lg p-6 shadow-lg sticky top-6"
            >
              <h3 class="text-2xl font-baskervville mb-2 text-[#285DA6]">
                Order Summary
              </h3>
              <div class="h-0.5 w-16 bg-[#285DA6] mb-6"></div>

              <!-- Summary Details -->
              <div class="space-y-4 mb-8">
                <div class="flex justify-between items-center pt-4">
                  <span class="font-medium text-lg">Total</span>
                  <span id="total" class="font-bold text-xl text-[#285DA6]"
                    >$0</span
                  >
                </div>
              </div>

              <!-- Secure Checkout Notice -->
              <div
                class="mb-6 bg-gray-50 p-4 rounded-lg border border-gray-100"
              >
                <div class="flex items-center mb-2">
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="text-green-600 mr-2"
                  >
                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"
                    ></rect>
                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                  </svg>
                  <span class="font-medium text-sm">Secure Checkout</span>
                </div>
                <p class="text-xs text-gray-600">
                  Your payment information is processed securely. We do not
                  store credit card details.
                </p>
              </div>

              <!-- Checkout Button -->
              <div class="mt-6" id="checkout-button-container">
                <button
                  id="checkout-button"
                  class="w-full py-3.5 px-4 bg-[#285DA6] text-white rounded-lg hover:bg-[#285DA6]/90 font-karla uppercase tracking-wider transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center"
                  disabled
                >
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="18"
                    height="18"
                    viewBox="0 0 24 24"
                    fill="none"
                    stroke="currentColor"
                    stroke-width="2"
                    stroke-linecap="round"
                    stroke-linejoin="round"
                    class="mr-2"
                  >
                    <circle cx="9" cy="21" r="1"></circle>
                    <circle cx="20" cy="21" r="1"></circle>
                    <path
                      d="M1 1h4l2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"
                    ></path>
                  </svg>
                  Proceed to Checkout
                </button>
              </div>

              <!-- Checkout button container -->
              <div id="checkout-modal-container">
                <!-- Checkout modal removed, now redirecting to checkout page -->
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</Layout>

<script>
  import { formatCurrency } from "../utils/formatters";

  // Meal plan mapping - similar to what's in the EnhancedBookingBox component
  const mealPlanLabels = {
    none: "No Meals",
    bb: "Bed & Breakfast",
    hb: "Half Board",
    fb: "Full Board",
  };

  // Function to get human-readable meal plan name
  function getMealPlanLabel(mealPlanCode) {
    return mealPlanLabels[mealPlanCode] || mealPlanCode;
  }

  // Function to initialize the cart page
  function initCartPage() {
    // Get cart data from localStorage
    const cartData = localStorage.getItem("cart");
    let cart = [];

    try {
      if (cartData) {
        cart = JSON.parse(cartData);

        // Add region_id if missing
        const updatedCart = cart.map((item: any) => {
          if (!item.regionId) {
            return { ...item, regionId: "reg_01" };
          }
          return item;
        });

        // Re-push cart data to localStorage to ensure it's fresh
        if (updatedCart.length > 0) {
          localStorage.setItem("cart", JSON.stringify(updatedCart));
          cart = updatedCart;
        }
      }
    } catch (error) {
      console.error("Failed to parse cart data:", error);
    }

    // Get DOM elements
    const emptyCartEl = document.getElementById("empty-cart");
    const cartItemsContainerEl = document.getElementById(
      "cart-items-container"
    );
    const checkoutButtonEl = document.getElementById("checkout-button");
    const checkoutButtonContainerEl = document.getElementById(
      "checkout-button-container"
    );
    const totalEl = document.getElementById("total");

    // If cart is empty, show empty state and return
    if (!cart.length) {
      if (emptyCartEl) emptyCartEl.classList.remove("hidden");
      if (cartItemsContainerEl) cartItemsContainerEl.classList.add("hidden");
      if (checkoutButtonEl)
        (checkoutButtonEl as HTMLButtonElement).disabled = true;
      return;
    }

    // Otherwise, hide empty state and show cart items
    if (emptyCartEl) emptyCartEl.classList.add("hidden");
    if (cartItemsContainerEl) cartItemsContainerEl.classList.remove("hidden");
    if (checkoutButtonEl)
      (checkoutButtonEl as HTMLButtonElement).disabled = false;

    // Calculate total
    const total = cart.reduce((total: number, item: any) => {
      // Calculate price based on quantity
      const quantity = item.quantity || 1;
      return total + (item.price || 0);
    }, 0);

    // Update total amount
    if (totalEl) totalEl.textContent = formatCurrency(total);

    // Render cart items
    if (cartItemsContainerEl) {
      cartItemsContainerEl.innerHTML = "";

      cart.forEach((item: any) => {
        const itemEl = document.createElement("div");
        itemEl.className =
          "bg-white border border-[#285DA6]/10 rounded-lg p-6 shadow-sm hover:shadow-md transition-all duration-300";
        itemEl.innerHTML = `
          <div class="flex flex-col md:flex-row gap-6">
            <div class="md:w-1/3 lg:w-1/4">
              <div class="relative group">
                <img
                  src="${item.image || "/images/room-placeholder.jpg"}"
                  alt="${item.roomType}"
                  class="w-full h-40 object-cover rounded-lg shadow-sm group-hover:shadow transition-all duration-300"
                />
                ${
                  item.mealPlan
                    ? `
                <div class="absolute top-3 left-3 bg-[#285DA6] text-white text-xs px-2 py-1 rounded-full uppercase tracking-wider">
                  ${getMealPlanLabel(item.mealPlan)}
                </div>`
                    : ""
                }
              </div>
            </div>
            <div class="md:w-2/3 lg:w-3/4 flex flex-col justify-between">
              <div>
                <div class="flex justify-between items-start mb-3">
                  <div>
                    <h4 class="font-karla text-xl font-medium text-[#285DA6]">${item.hotelName}</h4>
                    <p class="text-sm text-gray-600 font-medium">${item.roomType}</p>
                  </div>
                  <div class="text-right">
                    <span class="font-medium text-lg">${formatCurrency(item.price)}</span>
                    <p class="text-xs text-gray-500">
                      ${
                        item.quantity && item.quantity > 1
                          ? `${item.quantity} rooms × ${formatCurrency(item.price / item.quantity)}`
                          : "Total for stay"
                      }
                    </p>
                  </div>
                </div>

                <div class="grid grid-cols-2 gap-x-4 gap-y-2 mt-3">
                  <div class="flex items-center text-sm text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-[#285DA6]">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    Check-in: <span class="font-medium ml-1">${new Date(item.checkIn).toLocaleDateString()}</span>
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-[#285DA6]">
                      <rect x="3" y="4" width="18" height="18" rx="2" ry="2"></rect>
                      <line x1="16" y1="2" x2="16" y2="6"></line>
                      <line x1="8" y1="2" x2="8" y2="6"></line>
                      <line x1="3" y1="10" x2="21" y2="10"></line>
                    </svg>
                    Check-out: <span class="font-medium ml-1">${new Date(item.checkOut).toLocaleDateString()}</span>
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-[#285DA6]">
                      <path d="M17 21v-2a4 4 0 0 0-4-4H5a4 4 0 0 0-4 4v2"></path>
                      <circle cx="9" cy="7" r="4"></circle>
                      <path d="M23 21v-2a4 4 0 0 0-3-3.87"></path>
                      <path d="M16 3.13a4 4 0 0 1 0 7.75"></path>
                    </svg>
                    Guests: <span class="font-medium ml-1">${item.guests}${item.infants ? ` (+ ${item.infants} infant${item.infants > 1 ? "s" : ""})` : ""}</span>
                  </div>
                  <div class="flex items-center text-sm text-gray-600">
                    <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-2 text-[#285DA6]">
                      <path d="M18 8h1a4 4 0 0 1 0 8h-1"></path>
                      <path d="M2 8h16v9a4 4 0 0 1-4 4H6a4 4 0 0 1-4-4V8z"></path>
                      <line x1="6" y1="1" x2="6" y2="4"></line>
                      <line x1="10" y1="1" x2="10" y2="4"></line>
                      <line x1="14" y1="1" x2="14" y2="4"></line>
                    </svg>
                    Room Type: <span class="font-medium ml-1">${item.roomType}</span>
                  </div>
                </div>

                <!-- Room Quantity Control -->
                <div class="mt-4 border-t border-gray-100 pt-4">
                  <div class="flex justify-between items-center">
                    <div class="text-sm text-gray-600">
                      <span>Number of Rooms:</span>
                      ${
                        item.available_rooms
                          ? `<span class="text-xs text-gray-500 ml-2">(${item.available_rooms} available)</span>`
                          : ""
                      }
                    </div>
                    <div class="flex items-center">
                      <button
                        class="w-8 h-8 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
                        onclick="updateCartItemQuantity('${item.id}', ${(item.quantity || 1) - 1})"
                        ${(item.quantity || 1) <= 1 ? "disabled" : ""}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          class="text-[#285DA6]"
                        >
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                      </button>
                      <span class="w-10 text-center font-medium">
                        ${item.quantity || 1}
                      </span>
                      <button
                        class="w-8 h-8 flex items-center justify-center border border-[#285DA6]/20 rounded-full disabled:opacity-50 hover:bg-[#285DA6]/5 transition-colors"
                        onclick="updateCartItemQuantity('${item.id}', ${(item.quantity || 1) + 1})"
                        ${(item.quantity || 1) >= (item.available_rooms || 1) ? "disabled" : ""}
                      >
                        <svg
                          xmlns="http://www.w3.org/2000/svg"
                          width="16"
                          height="16"
                          viewBox="0 0 24 24"
                          fill="none"
                          stroke="currentColor"
                          stroke-width="2"
                          stroke-linecap="round"
                          stroke-linejoin="round"
                          class="text-[#285DA6]"
                        >
                          <line x1="12" y1="5" x2="12" y2="19"></line>
                          <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                      </button>
                    </div>
                  </div>
                </div>
              </div>

              <div class="mt-6 flex justify-end">
                <button
                  class="flex items-center px-3 py-1.5 text-sm text-red-600 hover:text-white border border-red-600 hover:bg-red-600 rounded-lg transition-colors duration-300"
                  onclick="removeFromCart('${item.id}')"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="mr-1.5">
                    <path d="M3 6h18"></path>
                    <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"></path>
                    <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"></path>
                  </svg>
                  Remove
                </button>
              </div>
            </div>
          </div>
        `;
        cartItemsContainerEl.appendChild(itemEl);
      });
    }

    // Add event listener to checkout button
    if (checkoutButtonEl) {
      checkoutButtonEl.addEventListener("click", () => {
        // Get the first item from the cart to use for checkout
        if (cart.length > 0) {
          const firstItem = cart[0];

          // Create checkout URL with parameters
          const checkoutUrl = new URL("/checkout", window.location.origin);

          // Add all required parameters from the first cart item
          checkoutUrl.searchParams.append("hotelId", firstItem.hotelId);
          checkoutUrl.searchParams.append("roomId", firstItem.roomId);
          checkoutUrl.searchParams.append("checkIn", firstItem.checkIn);
          checkoutUrl.searchParams.append("checkOut", firstItem.checkOut);
          checkoutUrl.searchParams.append(
            "checkInTime",
            firstItem.checkInTime || ""
          );
          checkoutUrl.searchParams.append(
            "checkOutTime",
            firstItem.checkOutTime || ""
          );
          checkoutUrl.searchParams.append(
            "totalAmount",
            firstItem.price.toString()
          );
          checkoutUrl.searchParams.append("currency", firstItem.currency);
          checkoutUrl.searchParams.append(
            "guests",
            firstItem.guests.toString()
          );
          checkoutUrl.searchParams.append(
            "infants",
            (firstItem.infants || 0).toString()
          );
          checkoutUrl.searchParams.append(
            "mealPlan",
            firstItem.mealPlan || "none"
          );
          checkoutUrl.searchParams.append(
            "quantity",
            (firstItem.quantity || 1).toString()
          );
          // Add region ID from cart item or use default
          checkoutUrl.searchParams.append(
            "regionId",
            firstItem.regionId || "reg_01JP9R0NP6B5DXGDYHFSSW0FK1"
          );

          // Redirect to checkout page
          window.location.href = checkoutUrl.toString();
        }
      });
    }
  }

  // Function to remove item from cart
  function removeFromCart(id: string) {
    // Get current cart
    const cartData = localStorage.getItem("cart");
    let cart: any[] = [];

    try {
      if (cartData) {
        cart = JSON.parse(cartData);
      }
    } catch (error) {
      console.error("Failed to parse cart data:", error);
    }

    // Remove item
    const updatedCart = cart.filter((item: any) => item.id !== id);

    // Save updated cart
    localStorage.setItem("cart", JSON.stringify(updatedCart));

    // Reinitialize page
    initCartPage();
  }

  // Initialize page when DOM is loaded
  document.addEventListener("DOMContentLoaded", () => {
    initCartPage();

    // Add event listener to back button
    const backButton = document.getElementById("back-button");
    if (backButton) {
      backButton.addEventListener("click", () => {
        history.back();
      });
    }
  });

  // Function to update cart item quantity
  function updateCartItemQuantity(id: string, newQuantity: number) {
    // Get current cart
    const cartData = localStorage.getItem("cart");
    let cart: any[] = [];

    try {
      if (cartData) {
        cart = JSON.parse(cartData);
      }
    } catch (error) {
      console.error("Failed to parse cart data:", error);
    }

    // Find the item
    const itemIndex = cart.findIndex((item: any) => item.id === id);
    if (itemIndex === -1) {
      console.error("Item not found in cart:", id);
      return;
    }

    // Ensure quantity is within valid range
    const item = cart[itemIndex];
    const maxQuantity = item.available_rooms || 1;
    newQuantity = Math.max(1, Math.min(newQuantity, maxQuantity));

    // Update the item quantity and price
    const pricePerRoom = item.price / (item.quantity || 1);
    cart[itemIndex] = {
      ...item,
      quantity: newQuantity,
      price: pricePerRoom * newQuantity,
    };

    // Save updated cart
    localStorage.setItem("cart", JSON.stringify(cart));

    // Reinitialize page
    initCartPage();
  }

  // Make functions available globally
  (window as any).removeFromCart = removeFromCart;
  (window as any).updateCartItemQuantity = updateCartItemQuantity;
</script>
