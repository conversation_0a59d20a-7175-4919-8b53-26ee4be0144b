---
import Layout from "../components/layout/Layout.astro";
import FAQHero from "../components/faq/FAQHero.astro";
import FAQSection from "../components/faq/FAQSection.astro";
import { useTranslations } from "../i18n/utils";
import { defaultLang } from "../i18n/ui";

// Get translations for default language (English)
const t = useTranslations(defaultLang);

// Page metadata
const title = `${t("faq.hero.title")} - Perfect Piste`;
const description = t("faq.hero.subtitle");
---

<Layout title={title} description={description}>
  <main class="min-h-screen bg-background">
    <!-- Hero Section -->
    <FAQHero
      title={t("faq.hero.title")}
      subtitle={t("faq.hero.subtitle")}
      imageUrl="/images/faq.jpg"
      lang={defaultLang}
    />

    <div class="px-0 md:px-8">
      <!-- FAQ Content -->
      <FAQSection lang={defaultLang} />
    </div>
  </main>
</Layout>
