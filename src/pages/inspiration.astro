---
import Layout from "../components/layout/Layout.astro";
import InspirationHero from "../components/inspiration/InspirationHero.astro";
import SkiingTypesWithStays from "../components/inspiration/SkiingTypesWithStays.astro";
import CTASection from "../components/home/<USER>";

import { useTranslations } from "../i18n/utils";
import { defaultLang } from "../i18n/ui";

// Get translations for default language (English)
const t = useTranslations(defaultLang);

// Define page metadata
const title = `${t("inspiration.hero.title")} - Perfect Piste`;
const description = t("inspiration.hero.description");
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <InspirationHero
    title={t("inspiration.hero.title")}
    description={t("inspiration.hero.description")}
    backgroundImage="https://wallpaperbat.com/img/412359-download-wallpaper-3840x2160-skiing-skier-jump-mountains-snow.jpg"
  />

  <!-- Skiing Types with Featured Stays Section -->
  <SkiingTypesWithStays lang={defaultLang} />

  <!-- CTA Section -->
  <CTASection
    title={t("inspiration.cta.title")}
    description={t("inspiration.cta.description")}
    ctaText={t("inspiration.cta.button")}
    ctaLink="/ai-search"
    backgroundImage="https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
