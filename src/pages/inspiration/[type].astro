---
import Layout from "../../components/layout/Layout.astro";
import {
  getInspirationCategoryBySlug,
  getAllInspirationSlugs,
} from "../../utils/inspirationData";
import { getHotelsBySkiingType } from "../../utils/dataService";
import InspirationLandingHero from "../../components/inspiration/InspirationLandingHero.astro";
import InspirationFeaturedStays from "../../components/inspiration/InspirationFeaturedStays.astro";
import InspirationHighlights from "../../components/inspiration/InspirationHighlights.astro";
import CTASection from "../../components/home/<USER>";

import { useTranslations } from "../../i18n/utils";
import { defaultLang } from "../../i18n/ui";

// Define the getStaticPaths function to generate all possible inspiration pages
export async function getStaticPaths() {
  const slugs = getAllInspirationSlugs();

  return slugs.map((slug) => ({
    params: { type: slug },
    props: { inspirationType: slug },
  }));
}

// Get the inspiration type from the URL
const { type } = Astro.params;
const inspirationType = type as string;

// Get the inspiration category data
const category = getInspirationCategoryBySlug(inspirationType);

if (!category) {
  return Astro.redirect("/404");
}

// Get translations for default language (English)
const t = useTranslations(defaultLang);

// Fetch all hotels for this inspiration type (not just featured)
let allHotels: any[] = [];
try {
  allHotels = await getHotelsBySkiingType(category.tag, 20, false); // Fetch up to 20 hotels, not just featured
} catch (error) {
  console.error(
    `Error fetching hotels for inspiration type ${inspirationType}:`,
    error
  );
  allHotels = [];
}

// Define page metadata
const title = category.metaTitle;
const description = category.metaDescription;
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <InspirationLandingHero
    name={category.name}
    category={category.category}
    description={category.longDescription}
    imageUrl={category.imageUrl}
    lang={defaultLang}
  />

  <!-- Highlights Section -->
  <InspirationHighlights
    highlights={category.highlights}
    idealFor={category.idealFor}
    categoryName={category.name}
  />

  <!-- Featured Stays Section -->
  <InspirationFeaturedStays
    hotels={allHotels}
    categoryName={category.name}
    searchPrompt={category.searchPrompt}
    lang={defaultLang}
  />

  <!-- CTA Section -->
  <CTASection
    title={t("inspiration.cta.title")}
    description={t("inspiration.cta.description")}
    ctaText={t("inspiration.cta.button")}
    ctaLink={`/ai-search?query=${encodeURIComponent(category.searchPrompt)}`}
    backgroundImage="https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
