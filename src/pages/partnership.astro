---
import Layout from "../components/layout/Layout.astro";
import PartnershipHero from "../components/partnership/PartnershipHero.astro";
import PartnershipOverview from "../components/partnership/PartnershipOverview.astro";
import PartnershipTestimonials from "../components/partnership/PartnershipTestimonials.astro";
import PartnershipApplication from "../components/partnership/PartnershipApplication.astro";

import { useTranslations } from "../i18n/utils";
import { defaultLang } from "../i18n/ui";

// Get translations for default language (English)
const t = useTranslations(defaultLang);

// Page metadata
const title = `${t("partnership.hero.title")} - Perfect Piste`;
const description = t("partnership.hero.subtitle");
---

<Layout title={title} description={description}>
  <main class="min-h-screen bg-background">
    <!-- Hero Section -->
    <PartnershipHero
      title={t("partnership.hero.title")}
      subtitle={t("partnership.hero.subtitle")}
      imageUrl="https://images.unsplash.com/photo-1600880292203-757bb62b4baf?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
      lang={defaultLang}
    />

    <div class="px-0 md:px-8">
      <!-- Partnership Overview -->
      <PartnershipOverview lang={defaultLang} />

      <!-- Partner Testimonials -->
      <PartnershipTestimonials lang={defaultLang} />

      <!-- Application Process -->
      <PartnershipApplication lang={defaultLang} />
    </div>
  </main>
</Layout>
