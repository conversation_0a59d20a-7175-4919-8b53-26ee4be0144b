---
import Layout from "../../components/layout/Layout.astro";
import { getAllHotels, getHotelDetailsQuick } from "../../utils/dataService";
import HotelScripts from "../../components/hotels/HotelScripts.astro";
import StickyHotelHeader from "../../components/hotels/StickyHotelHeader.astro";
import SimplePhotoModal from "../../components/photos/SimplePhotoModal.astro";
import HotelMainContent from "../../components/hotels/HotelMainContent";
import ShareModalController from "../../components/share/ShareModalController";
import ReactHotelHero from "../../components/hotels/ReactHotelHero";
import { defaultLang } from "../../i18n/ui";

// Import sticky header styles
import "../../styles/sticky-header.css";

// Define the getStaticPaths function to generate all possible hotel pages
export async function getStaticPaths() {
  try {
    const hotels = await getAllHotels();

    // Add a test hotel for development
    const paths = hotels.map((hotel) => ({
      params: { id: hotel.id },
      props: { hotelId: hotel.id },
    }));

    // Add grand-hotel for testing
    paths.push({
      params: { id: "grand-hotel" },
      props: { hotelId: "grand-hotel" },
    });

    return paths;
  } catch (error) {
    console.error("Error generating static paths for hotels:", error);
    return [];
  }
}

// Get the hotel ID from props or params
const { hotelId } = Astro.props;
const { id } = Astro.params;
const hotelIdToUse = hotelId || id;

// Get search parameters from URL
const searchParams = Astro.url.searchParams;
const urlCheckIn = searchParams.get("check_in");
const urlCheckOut = searchParams.get("check_out");
const urlAdults = searchParams.get("adults");
const urlChildren = searchParams.get("children");
const urlInfants = searchParams.get("infants");

// Fetch hotel data quickly first (without room availability for fast page load)
let hotel,
  roomTypes = [],
  checkIn,
  checkOut,
  nights;

try {
  const result = await getHotelDetailsQuick(hotelIdToUse as any);
  hotel = result.hotel;
  // Use basic room types (availability will be loaded asynchronously on client)
  roomTypes = result.roomTypes || [];

  checkIn = result.checkIn;
  checkOut = result.checkOut;
  nights = result.nights || 4;
} catch (error) {
  console.error(`Error fetching hotel data for ID ${hotelIdToUse}:`, error);
  return Astro.redirect("/404"); // Redirect to 404 page if hotel not found
}

// Use hotel data from API
const hotelData = {
  ...hotel,
};
console.log("hotelData", hotelData);

// Define page metadata
const title = `${hotelData.name} - Perfect Piste`;
const description =
  hotelData.description?.substring(0, 160) ||
  `Discover ${hotelData.name} in ${hotelData.location} - Luxury ski accommodation with Perfect Piste.`;

// Define sharing metadata
const ogImage =
  hotelData.images && hotelData.images.length > 0
    ? hotelData.images[0]
    : "/og.png";
const canonicalUrl = new URL(
  Astro.url.pathname,
  Astro.site || "https://perfectpiste.com"
).toString();
const ogType = "website";
---

<Layout
  title={title}
  description={description}
  ogImage={ogImage}
  canonicalUrl={canonicalUrl}
  ogType={ogType}
>
  <style>
    /* Ensure smooth transitions for collapsible content */
    .collapsible-content {
      transition:
        height 0.2s ease-out,
        border-radius 0.2s ease-out;
      box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    }

    /* Ensure room card transitions smoothly */
    .room-card {
      transition:
        all 0.3s ease-out,
        border-radius 0.2s ease-out,
        border-bottom-width 0.2s ease-out;
    }

    /* Connected state */
    .room-card.border-b-0 + .collapsible-content:not(.hidden) {
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    /* Make the expanded content appear as part of the same component */
    .room-card.border-b-0 {
      margin-bottom: 0;
      border-bottom-left-radius: 0;
      border-bottom-right-radius: 0;
    }

    /* Hide the divider when content is expanded */
    .room-card.border-b-0 .details-divider {
      border-top: none !important;
    }

    .collapsible-content:not(.hidden) {
      border-top: none;
      margin-top: 0;
      box-shadow: 0 1px 3px rgba(0, 0, 0, 0.05);
    }

    /* Ensure consistent border color and width */
    .room-card.border-b-0 + .collapsible-content:not(.hidden) {
      border-color: rgba(40, 93, 166, 0.1);
      border-width: 1px;
      border-top-width: 0;
    }

    /* Handle selected state with ring */
    .room-card.ring-2 {
      z-index: 10;
      position: relative; /* Ensure z-index works */
    }

    /* When a room card with ring is expanded, apply ring to expanded content as well */
    .room-card.ring-2.border-b-0 + .collapsible-content:not(.hidden) {
      border-top-width: 0;
      position: relative;
      z-index: 5;
      border-left: 2px solid #285da6;
      border-right: 2px solid #285da6;
      border-bottom: 2px solid #285da6;
      border-bottom-left-radius: 0.5rem;
      border-bottom-right-radius: 0.5rem;
    }

    /* Ensure the ring is always visible, even when details are expanded */
    .room-card.ring-2.border-b-0 {
      z-index: 10;
      position: relative;
      border-bottom: none;
    }

    /* Fix border styling for expanded content */
    .collapsible-content:not(.hidden) {
      border: 1px solid rgba(40, 93, 166, 0.1);
      border-top: none;
    }

    /* Remove top border from the collapsible content */
    .collapsible-content:not(.hidden) {
      border-top: none !important;
    }

    /* Ensure consistent border radius */
    .room-card.border-b-0 {
      border-bottom-left-radius: 0 !important;
      border-bottom-right-radius: 0 !important;
    }

    .collapsible-content:not(.hidden) {
      border-top-left-radius: 0 !important;
      border-top-right-radius: 0 !important;
    }
  </style>
  <!-- Sticky Header -->
  <StickyHotelHeader
    name={hotelData.name}
    location={hotelData.location}
    rating={hotelData.rating}
  />

  <!-- Hero Section with Header and Main Image + Gallery -->
  <ReactHotelHero
    name={hotelData.name}
    location={hotelData.location}
    rating={hotelData.rating}
    mainImage={hotelData.images[0]}
    images={hotelData.images}
    hotelId={String(id)}
    client:load
  />

  <!-- Main Content -->
  <HotelMainContent
    hotelData={hotelData}
    roomTypes={roomTypes as any}
    hotelId={id}
    nights={nights}
    urlAdults={urlAdults}
    urlChildren={urlChildren}
    urlInfants={urlInfants}
    lang={defaultLang}
    client:load
  />

  <!-- Photo Modal Component -->
  <SimplePhotoModal images={hotelData.images} hotelName={hotelData.name} />

  <!-- Share Modal Component -->
  <ShareModalController client:load />

  <!-- JavaScript functionality -->
  <HotelScripts />
</Layout>
