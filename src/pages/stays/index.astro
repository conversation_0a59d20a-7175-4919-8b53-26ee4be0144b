---
import Layout from "../../components/layout/Layout.astro";
import { getFeaturedHotels, getAllHotels } from "../../utils/dataService";
import HeroSection from "../../components/stays/HeroSection.astro";
import FeaturedHotels from "../../components/stays/FeaturedHotels.astro";
import AllHotels from "../../components/stays/AllHotels.astro";
import CTASection from "../../components/home/<USER>";
import Pagination from "../../components/ui/Pagination.astro";

import TestimonialsSection from "../../components/stays/TestimonialsSection.astro";
import ConciergeSection from "../../components/stays/ConciergeSection.astro";

// Get the current page from the URL query parameter
const page = Astro.url.searchParams.get("page");
const currentPage = page ? parseInt(page) : 1;
const hotelsPerPage = 6; // Number of hotels to display per page

// Fetch hotels from API
const featuredHotels = await getFeaturedHotels();
const allHotels = await getAllHotels();

// Calculate pagination
const totalHotels = allHotels.length;
const totalPages = Math.ceil(totalHotels / hotelsPerPage);

// Get hotels for the current page
const startIndex = (currentPage - 1) * hotelsPerPage;
const endIndex = startIndex + hotelsPerPage;
const paginatedHotels = allHotels.slice(startIndex, endIndex);

// Define page metadata
const title = "Luxury Hotels - Perfect Piste";
const description =
  "Discover our collection of the world's most exceptional properties, each offering unparalleled luxury and extraordinary experiences.";
---

<Layout title={title} description={description}>
  <!-- Hero Section -->
  <HeroSection
    title="Luxury Hotels"
    description={description}
    backgroundImage="https://images.unsplash.com/photo-1551882547-ff40c63fe5fa?ixlib=rb-4.0.3&auto=format&fit=crop&w=1920&q=80"
  />

  <!-- Concierge Services Section -->
  <ConciergeSection />

  <!-- Featured Hotels -->
  <FeaturedHotels hotels={featuredHotels as any} />

  <!-- Testimonials Section -->
  <TestimonialsSection />

  <!-- All Hotels Section -->
  <div id="all-hotels">
    <AllHotels hotels={paginatedHotels as any} title="Explore All Hotels" />
    {
      totalPages > 1 && (
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          baseUrl="/stays"
        />
      )
    }
  </div>

  <!-- CTA Section -->
  <CTASection
    title="Begin Your Alpine Adventure"
    description="Let our team of ski experts create a bespoke mountain experience tailored to your preferences and skill level."
    ctaText="PLAN YOUR TRIP"
    ctaLink="/ai-search"
    ctaPrompt="I've been browsing your luxury ski accommodations and I'm ready to plan my alpine adventure. Can you help me find the perfect stay and create a personalized ski experience?"
    backgroundImage="https://images.unsplash.com/photo-1486870591958-9b9d0d1dda99?ixlib=rb-4.0.3&auto=format&fit=crop&w=2576&q=80"
  />
</Layout>
