/* Expandable Header Search Styles */

/* Wrapper */
.expandable-search-wrapper {
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  position: relative;
  z-index: 100;
  will-change: opacity, transform;
}

.expandable-search-wrapper.visible {
  opacity: 1;
  transform: translateY(0) scale(1);
}

.expandable-search-wrapper.hidden {
  opacity: 0;
  transform: translateY(-10px) scale(0.98);
  pointer-events: none;
}

/* Expanded Search Wrapper */
.expanded-search-wrapper {
  animation: expandSearch 0.3s cubic-bezier(0.25, 1, 0.5, 1) forwards;
  position: relative;
  z-index: 200;
  width: 100%;
  max-width: 848px;
  margin: 0 auto;
}

/* Ensure the airbnb-header-search has correct dimensions in expanded state */
.expanded-search-wrapper .airbnb-header-search {
  width: 848px;
  max-width: 848px;
  height: 64px;
  box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.1);
  border: 1px solid rgba(var(--primary-rgb), 0.1);
}

/* Header Expanded State */
.header-wrapper.header-expanded {
  height: auto;
  background-color: white; /* Use solid white background */
  border-bottom: none; /* Remove border */
  box-shadow: none; /* Remove shadow */
  position: fixed;
  width: 100%;
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
}

/* Header Scrolled State */
.header-wrapper.header-scrolled {
  background-color: white;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.05);
  border-bottom: none;
  position: fixed;
  width: 100%;
  z-index: 100;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
}

/* No overlay when header is expanded */
.header-wrapper.header-expanded::after {
  display: none; /* Remove the overlay completely */
}

/* Expanded header for traditional search */
.primary-header.expanded-header {
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  height: 132px !important; /* Set exact height to match search screen */
  padding: 16px 0 !important; /* Match the search page padding */
}

.header-wrapper.header-expanded .primary-header {
  background-color: white; /* Use solid white background */
  width: 100%;
  height: auto;
  min-height: 80px;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  position: relative;
  z-index: 10;
  padding-bottom: 0;
}

.header-wrapper.header-expanded #expandable-search-container {
  position: absolute;
  width: 100%;
  max-width: 850px;
  opacity: 1;
  overflow: visible;
  transform: translateX(-50%);
  top: 0;
  left: 50%;
  padding: 0;
  pointer-events: all;
  z-index: 200;
}

/* Container visibility in header */
#expandable-search-container {
  transition: all 0.4s cubic-bezier(0.25, 1, 0.5, 1);
  width: 0;
  max-width: 0;
  opacity: 0;
  overflow: hidden;
  position: absolute;
  left: 50%;
  top: 15px; /* Adjusted to center in 76px header with 16px padding */
  transform: translateX(-50%) translateY(-5px);
  z-index: 60;
  pointer-events: none;
}

#expandable-search-container.search-visible {
  width: 450px;
  max-width: 450px;
  opacity: 1;
  overflow: visible;
  transform: translateX(-50%) translateY(0);
  pointer-events: all;
  z-index: 150;
}

/* Hide search on homepage */
#expandable-search-container.on-homepage {
  width: 0;
  max-width: 0;
  opacity: 0;
  overflow: hidden;
  pointer-events: none;
  transform: translateX(-50%) translateY(-10px);
}

/* Expanded search container positioning */
.header-wrapper.header-expanded #expandable-search-container {
  width: 848px;
  max-width: 848px;
  top: 54px; /* Position to align with 168px header height (76px base height + 92px expansion) */
  z-index: 100;
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
}

@media (max-width: 768px) {
  #expandable-search-container.search-visible {
    width: 90%;
    max-width: 350px;
  }

  .collapsed-search-container {
    max-width: 350px;
  }

  .collapsed-search-inner {
    padding: 0.5rem 0.5rem 0.5rem 1rem;
  }

  .search-tab {
    font-size: 0.875rem;
  }

  /* Mobile styles for info button */
  .info-button {
    padding: 4px 8px;
  }

  .info-text {
    display: none; /* Hide text on mobile, show only icon */
  }
}

/* Collapsed Search Container */
.collapsed-search-container,
.ai-search-container {
  background-color: var(--card);
  border-radius: 28px; /* Match expanded search radius */
  box-shadow: 0 2px 16px rgba(var(--primary-rgb), 0.08);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  border: 1px solid rgba(53, 102, 171, 0.15); /* Match expanded search border */
  width: 85%; /* Decreased from 100% to 80% */
  max-width: 680px; /* Added max-width for larger screens */
  overflow: hidden;
  margin: 0 auto;
  position: relative;
  z-index: 150;
}

/* AI Search Container */
.ai-search-container {
  display: flex;
  flex-direction: column;
  width: 100%;
  cursor: default;
  border-radius: 24px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.15);
}

/* Ensure the form takes up the full width */
.ai-search-container .airbnb-header-search {
  width: 100%;
  border-radius: 24px;
}

/* Suggestions wrapper styling */
.suggestions-wrapper {
  margin-top: 12px;
  width: 100%;
  max-width: 848px;
}

.collapsed-search-container:hover,
.ai-search-container:hover {
  box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.15);
  transform: translateY(-1px);
  border-color: var(--primary);
}

.search-inner-container {
  width: 100%;
  display: flex;
  align-items: center;
}

.collapsed-search-inner {
  display: flex;
  align-items: center;
  padding: 0.4rem 0.5rem 0.4rem 1.5rem;
  height: 46px;
  box-sizing: border-box;
}

.search-tab {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--card-foreground); /* Original color */
  white-space: nowrap;
  font-family: "Karla", sans-serif;
  padding: 0 0.85rem;
}

.search-divider {
  width: 1px;
  height: 20px;
  background-color: var(--border);
  margin: 0 0.75rem;
}

.search-button-small {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: #3566ab; /* Brand color */
  color: white;
  border: none;
  margin-left: auto;
  flex-shrink: 0;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
}

.search-button-small:hover {
  transform: scale(1.05);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35);
  background: #2a5089; /* Darker brand color for hover */
}

/* Search buttons container */
.search-buttons-container {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 8px;
}

/* Info button styles */
.info-button {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 6px 10px;
  border-radius: 16px;
  background-color: rgba(53, 102, 171, 0.1);
  color: #3566ab;
  font-size: 12px;
  font-weight: 500;
  font-family: "Karla", sans-serif;
  transition: all 0.2s ease;
  text-decoration: none;
  border: 1px solid transparent;
}

.info-button:hover {
  background-color: rgba(53, 102, 171, 0.15);
  border-color: rgba(53, 102, 171, 0.3);
}

.info-text {
  white-space: nowrap;
}

/* Expanded Search Container */
.search-overlay {
  position: fixed;
  top: 80px; /* Start below the header */
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.25);
  z-index: 90;
  animation: fadeIn 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  will-change: opacity;
}

.header-wrapper.header-expanded .search-overlay {
  background-color: rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(2px);
  z-index: 100;
}

.expanded-search-container {
  position: relative;
  background-color: white;
  z-index: 100;
  animation: fadeIn 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  width: 100%;
  margin: 0 auto;
  overflow: hidden;
}

.header-wrapper.header-expanded .expanded-search-container {
  border-radius: 1rem;
  box-shadow: 0 8px 32px rgba(53, 102, 171, 0.15);
  border: 1px solid rgba(53, 102, 171, 0.1);
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
  width: 100%;
  max-width: 850px;
  position: relative;
  background-color: white;
  z-index: 200;
}

.expanded-search-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid rgba(0, 0, 0, 0.08);
}

.expanded-tabs {
  display: flex;
  gap: 1.5rem;
}

.expanded-tab {
  font-size: 1rem;
  font-weight: 500;
  color: var(--foreground);
  background: none;
  border: none;
  padding: 0.5rem 0;
  cursor: pointer;
  position: relative;
  font-family: "Karla", sans-serif;
}

.expanded-tab.active {
  color: #3566ab;
}

.expanded-tab.active::after {
  content: "";
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 2px;
  background-color: #3566ab;
  border-radius: 1px;
}

.close-expanded-search {
  background: none;
  border: none;
  color: var(--foreground);
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.close-expanded-search:hover {
  background-color: rgba(0, 0, 0, 0.05);
}

.expanded-search-content {
  padding: 1rem 0;
}

.header-wrapper.header-expanded .expanded-search-content {
  padding: 0;
  width: 100%;
  max-width: 100%;
  margin: 0 auto;
  background-color: white;
  display: flex;
  justify-content: center;
  position: relative;
}

.expanded-search-fields {
  display: flex;
  align-items: center;
  background-color: white;
  border-radius: 2rem;
  padding: 0;
  position: relative;
  border: none;
}

.header-wrapper.header-expanded .expanded-search-fields {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  max-width: 850px;
  margin: 12px auto;
  height: 66px;
  padding: 0;
  border-radius: 32px;
  background-color: #fff;
  border: none;
  position: relative;
  box-shadow: none;
}

.search-field {
  flex: 1;
  position: relative;
  min-width: 0;
  border-radius: 2rem;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  margin: 0 4px;
}

.header-wrapper.header-expanded .search-field {
  height: 66px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  padding: 0.5rem 1.5rem;
  background-color: transparent;
  border: none;
  position: relative;
  flex: 1;
  min-width: 0;
  max-width: 25%;
}

.header-wrapper.header-expanded .search-field:not(:last-of-type)::after {
  content: "";
  position: absolute;
  right: 0;
  top: 25%;
  height: 50%;
  width: 1px;
  background-color: #dddddd;
}

.search-field:hover {
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.15);
  border-color: rgba(53, 102, 171, 0.25);
}

.header-wrapper.header-expanded .search-field:hover {
  box-shadow: none;
  background-color: #f8f9fa;
}

.search-field-label {
  font-size: 0.75rem;
  font-weight: 700;
  color: #3566ab;
  margin-bottom: 0.25rem;
  font-family: "Karla", sans-serif;
  text-transform: none;
  letter-spacing: 0.02em;
}

.header-wrapper.header-expanded .search-field-label {
  font-size: 0.75rem;
  font-weight: 600;
  color: #222222;
}

.search-field input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 0.9375rem;
  color: #333333;
  outline: none;
  font-family: "Karla", sans-serif;
}

.search-field-date-input {
  width: 100%;
  border: none;
  background: transparent;
  font-size: 0.9375rem;
  color: #333333;
  outline: none;
  font-family: "Karla", sans-serif;
  cursor: pointer;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
}

/* Hide the calendar icon in date inputs */
.search-field input[type="date"]::-webkit-calendar-picker-indicator {
  opacity: 0;
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  cursor: pointer;
}

/* Style for date inputs with value */
.search-field input[type="date"]:not(:placeholder-shown) {
  color: #333333;
  font-weight: 500;
}

/* Style for date inputs without value */
.search-field input[type="date"]:placeholder-shown {
  color: #555555;
}

.search-field-value {
  font-size: 0.9375rem;
  color: #555555;
  font-family: "Karla", sans-serif;
}

.header-wrapper.header-expanded .search-field-value {
  font-size: 0.875rem;
  color: #555555;
}

.search-field-icon {
  position: absolute;
  right: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #3566ab;
  opacity: 0.8;
}

.header-wrapper.header-expanded .search-field-icon {
  color: #3566ab;
  opacity: 0.9;
}

.search-field-divider {
  display: none;
}

.header-wrapper.header-expanded .search-field-divider {
  display: none;
}

.search-button-large {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  background: linear-gradient(
    135deg,
    #3566ab 0%,
    rgba(53, 102, 171, 0.85) 100%
  );
  color: white;
  border: none;
  border-radius: 2rem;
  padding: 0.75rem 1.5rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.25, 1, 0.5, 1);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
  margin-left: 0.5rem;
  font-family: "Karla", sans-serif;
  height: 48px;
  min-width: 48px;
}

.header-wrapper.header-expanded .search-button-large {
  margin: 8px;
  padding: 0;
  width: 48px;
  min-width: 48px;
  height: 48px;
  position: absolute;
  right: 8px;
  top: 50%;
  transform: translateY(-50%);
}

.search-button-large:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(53, 102, 171, 0.35);
  background: linear-gradient(
    135deg,
    #3566ab 0%,
    rgba(53, 102, 171, 0.95) 100%
  );
}

.search-button-large:active {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.25);
}

.search-button-large:disabled {
  opacity: 0.7;
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
  background: linear-gradient(
    135deg,
    #3566ab 0%,
    rgba(53, 102, 171, 0.65) 100%
  );
}

.header-wrapper.header-expanded .search-button-large span {
  display: none;
}

.header-wrapper.header-expanded .search-button-large:hover {
  transform: translateY(-50%) scale(1.05);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px) scale(0.98);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

@keyframes expandSearch {
  from {
    opacity: 0;
    transform: translateY(-10px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* Responsive Styles */
/* Mobile hidden value class */
.mobile-hidden-value {
  opacity: 0;
  height: 0;
  overflow: hidden;
  pointer-events: none;
}

@media (max-width: 768px) {
  /* Hide the expandable search component in mobile view for the header */
  .header-wrapper .expandable-search-wrapper {
    display: none;
  }

  .header-wrapper #expandable-search-container {
    display: none;
  }

  .header-wrapper #expandable-search-container.search-visible {
    display: none;
  }

  .header-wrapper #expandable-search-container.on-homepage {
    display: none;
  }

  .header-wrapper.header-scrolled #expandable-search-container.on-homepage {
    display: none;
  }

  /* But make it visible in the hero section */
  .hero-content .expandable-search-wrapper {
    display: block;
    width: 100%;
    max-width: 100%;
  }

  .collapsed-search-container {
    max-width: 90%;
    width: 90%;
  }

  .collapsed-search-inner {
    padding: 0.4rem 0.5rem 0.4rem 1rem;
  }

  .search-tab {
    font-size: 0.75rem;
  }

  /* Adjust expanded search view for mobile */
  .expanded-search-wrapper {
    width: 100%;
    max-width: 100%;
  }

  .expanded-search-wrapper .airbnb-header-search {
    width: 100%;
    max-width: 100%;
    height: auto;
    flex-direction: column;
    padding: 0.5rem;
  }

  .search-toggle-container {
    width: 100%;
    padding: 0.5rem;
  }

  .search-toggle-wrapper {
    width: 100%;
    max-width: 100%;
  }

  .expanded-search-fields {
    flex-direction: column;
    background-color: transparent;
    padding: 0;
  }

  .search-field {
    width: 100%;
    margin-bottom: 0.5rem;
    background-color: #f7f7f7;
    padding: 0.75rem;
  }

  .search-section {
    width: 100%;
    margin-bottom: 0.5rem;
    border-bottom: 1px solid rgba(0, 0, 0, 0.1);
    padding-bottom: 0.5rem;
  }

  .ai-search-input-section {
    width: 100%;
    padding: 10px;
  }

  .ai-search-input {
    width: 100%;
    padding: 10px;
    height: 40px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: #f9f9f9;
    font-size: 16px; /* Larger font size for mobile */
  }

  .search-field-divider {
    display: none;
  }

  .search-button-large {
    width: 100%;
    margin-left: 0;
    margin-top: 0.5rem;
  }

  .search-button {
    width: 100%;
    margin-top: 0.5rem;
  }

  /* Hero section specific styles for mobile */
  .hero-content .expandable-search-wrapper {
    margin-top: 1.5rem;
    display: block !important; /* Force display */
    width: 100%;
    max-width: 100%;
  }

  .hero-content .collapsed-search-container {
    max-width: 90%;
    width: 90%;
    background-color: white;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  }

  .hero-content .expanded-search-wrapper {
    width: 100%;
    max-width: 100%;
    display: block !important; /* Force display */
  }

  .hero-content .expanded-search-wrapper .airbnb-header-search {
    background-color: white;
    border-radius: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
    display: flex !important; /* Force display */
  }

  /* Ensure the input is visible and properly styled */
  .hero-content .ai-search-input-section {
    display: block;
    width: 100%;
  }

  .hero-content .ai-search-input {
    display: block;
    width: 100%;
    padding: 12px;
    height: 45px;
    border: 1px solid #ddd;
    border-radius: 8px;
    background-color: white;
    font-size: 16px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1) inset;
  }
}
