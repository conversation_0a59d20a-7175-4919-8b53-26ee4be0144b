/* Luxurious language selector animations */
@keyframes slide-in {
  from {
    opacity: 0;
    transform: translateY(-5px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.animate-slide-down {
  animation: slide-in 0.2s ease-out forwards;
}

/* Hover animation for language items */
.language-item-hover {
  transition: all 0.3s ease;
}

.language-item-hover:hover {
  background-color: rgba(40, 93, 166, 0.05);
  padding-left: 1.25rem;
  color: #285DA6;
}

/* Subtle shimmer effect for selected language */
@keyframes shimmer {
  0% {
    background-position: -100% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

.language-selected {
  background: linear-gradient(
    90deg,
    rgba(255, 255, 255, 1) 0%,
    rgba(40, 93, 166, 0.05) 25%,
    rgba(255, 255, 255, 1) 50%,
    rgba(40, 93, 166, 0.05) 75%,
    rgba(255, 255, 255, 1) 100%
  );
  background-size: 200% 100%;
  animation: shimmer 3s infinite linear;
  border-left: 2px solid #285DA6;
}

/* Footer menu hover effect */
.footer-menu-item {
  transition: all 0.3s ease;
  display: inline-block;
}

.footer-menu-item:hover {
  transform: scale(1.05);
  text-shadow: 0 0 1px rgba(40, 93, 166, 0.2);
}

/* Header menu hover effect */
.header-menu-item {
  transition: all 0.3s ease;
  display: inline-block;
}

.header-menu-item:hover {
  transform: scale(1.05);
  text-shadow: 0 0 1px rgba(40, 93, 166, 0.2);
}

/* Currency selector specific styles */
.currency-selector-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 50%;
  transition: all 0.2s ease;
  color: inherit;
  font-size: 16px;
  font-weight: 600;
  width: 40px;
  height: 40px;
  position: relative;
}

.currency-selector-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
  transform: scale(1.05);
}

.currency-symbol-display {
  font-weight: 600;
  line-height: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  min-width: 20px;
}

/* Dynamic sizing for currency symbols */
.currency-symbol-large {
  font-size: 16px; /* For single symbols like £, $, € */
}

.currency-symbol-medium {
  font-size: 14px; /* For 2-character symbols like A$, C$ */
}

.currency-symbol-small {
  font-size: 11px; /* For 3+ character symbols like CHF */
  font-weight: 700;
  letter-spacing: 0.5px;
}

.currency-selector-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 180px;
  z-index: 1000;
  overflow: hidden;
  margin-top: 8px;
}

.currency-selector-option {
  width: 100%;
  background: none;
  border: none;
  padding: 14px 16px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: space-between;
  color: #333;
  position: relative;
}

.currency-selector-option:hover {
  background-color: rgba(40, 93, 166, 0.05);
}

.currency-selector-option.active {
  background-color: rgba(40, 93, 166, 0.1);
  color: #285DA6;
}

.currency-option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.currency-symbol-dropdown {
  font-weight: 600;
  min-width: 28px;
  text-align: center;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* Dropdown symbol sizes */
.currency-symbol-dropdown.currency-symbol-large {
  font-size: 18px; /* For single symbols like £, $, € */
}

.currency-symbol-dropdown.currency-symbol-medium {
  font-size: 16px; /* For 2-character symbols like A$, C$ */
}

.currency-symbol-dropdown.currency-symbol-small {
  font-size: 13px; /* For 3+ character symbols like CHF */
  font-weight: 700;
  letter-spacing: 0.5px;
}

.currency-option-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.currency-code-text {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.2;
}

.currency-name-text {
  font-size: 12px;
  opacity: 0.7;
  line-height: 1.2;
}

.currency-check-icon {
  color: #285DA6;
  opacity: 0.8;
}

/* Homepage hero section - white currency selector */
.homepage-header:not(.header-scrolled) .currency-selector-button {
  color: white;
  border: 0px solid rgba(255, 255, 255, 0.15) !important;
  background: transparent !important;
}

.homepage-header:not(.header-scrolled) .currency-selector-button:hover {
  background-color: rgba(255, 255, 255, 0.1) !important;
  border-color: rgba(255, 255, 255, 0.5) !important;
  transform: translateY(-1px);
}

.homepage-header:not(.header-scrolled) .currency-symbol-display {
  color: white !important;
}

/* Scrolled state - blue currency selector */
.header-scrolled .currency-selector-button {
  color: #285DA6;
  border: 1px solid rgba(53, 102, 171, 0.2) !important;
  background-color: rgba(255, 255, 255, 0.8) !important;
}

.header-scrolled .currency-selector-button:hover {
  background-color: rgba(255, 255, 255, 0.95) !important;
  border-color: rgba(53, 102, 171, 0.8) !important;
  box-shadow: 0 2px 8px rgba(53, 102, 171, 0.15);
  transform: translateY(-1px);
}

.header-scrolled .currency-symbol-display {
  color: #285DA6;
}

/* Non-homepage pages - always blue */
.currency-selector-button {
  color: #3566ab;
  border: 0px solid rgba(53, 102, 171, 0.2);
  background: transparent;
}

.currency-selector-button:hover {
  border-color: rgba(53, 102, 171, 0.5);
  background-color: rgba(255, 255, 255, 0.1);
  transform: translateY(-1px);
}

.currency-symbol-display {
  color: #3566ab;
}

/* Active state */
.currency-selector-button:active {
  transform: translateY(0);
  transition: all 0.1s ease;
}

/* Language selector base styles */
.language-selector {
  position: relative;
  display: inline-block;
}

.language-selector-button {
  background: none;
  border: none;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  border-radius: 20px;
  transition: all 0.2s ease;
  color: inherit;
  font-size: 14px;
  font-weight: 500;
}

.language-selector-button:hover {
  background-color: rgba(255, 255, 255, 0.1);
}

.language-selector-content {
  display: flex;
  align-items: center;
  gap: 4px;
}

.language-selector-arrow {
  transition: transform 0.2s ease;
}

.language-selector-arrow.open {
  transform: rotate(180deg);
}

.language-selector-dropdown {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.12);
  border: 1px solid rgba(0, 0, 0, 0.08);
  min-width: 200px;
  z-index: 1000;
  overflow: hidden;
  margin-top: 8px;
}

.language-selector-option {
  width: 100%;
  background: none;
  border: none;
  padding: 12px 16px;
  text-align: left;
  cursor: pointer;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  gap: 12px;
  color: #333;
}

.language-selector-option:hover {
  background-color: rgba(40, 93, 166, 0.05);
}

.language-selector-option.active {
  background-color: rgba(40, 93, 166, 0.1);
  color: #285DA6;
}

.language-selector-option-content {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
}

.language-selector-option-text {
  display: flex;
  flex-direction: column;
  gap: 2px;
}
