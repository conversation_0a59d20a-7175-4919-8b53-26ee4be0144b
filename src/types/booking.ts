/**
 * Type definitions for booking-related data structures
 */

import type { Booking as BaseBooking } from "../utils/store/bookings";

export interface Traveler {
  name?: string;
  age?: number;
}

export interface TravelerWithId extends Traveler {
  id?: string;
  type?: string;
}

export interface GuestUsage {
  guest_type?: string;
  guest_index?: number;
  usage_dates?: string[];
}

export interface AddOn {
  id?: string;
  name?: string;
  service_id?: string;
  adult_price?: number;
  child_price?: number;
  description?: string;
  guest_usage?: GuestUsage[];
  total_price?: number;
  usage_dates?: string[];
  pricing_type?: string;
  currency_code?: string;
  package_price?: number;
  service_level?: string;
  adult_quantity?: number;
  child_quantity?: number;
  infant_quantity?: number;
  number_of_days?: number;
  total_occupancy?: number;
  package_quantity?: number;
  per_day_adult_price?: number;
  per_day_child_price?: number;
}

export interface ExtraBedDetails {
  nights?: number;
  check_in?: string;
  check_out?: string;
  room_name?: string;
  total_price?: number;
  currency_code?: string;
  number_of_beds?: number;
  room_config_id?: string;
  extra_bed_config?: {
    id?: string;
    name?: string;
    type?: string;
  };
  guest_assignments?: {
    guest_age?: number;
    bed_number?: number;
    guest_name?: string;
  }[];
  price_per_bed_per_night?: number;
}

export interface BookingMetadata {
  cots?: number;
  adults?: number;
  add_ons?: AddOn[];
  cart_id?: string;
  infants?: number;
  children?: number;
  hotel_id?: string;
  cot_total?: number;
  meal_plan?: string;
  travelers?: {
    adults?: Traveler[];
    infants?: Traveler[];
    children?: Traveler[];
  };
  extra_beds?: number;
  guest_name?: string;
  cot_details?: any;
  guest_email?: string;
  guest_phone?: string;
  cot_quantity?: number;
  reservations?: any[];
  total_amount?: number;
  check_in_date?: string;
  check_in_time?: string;
  currency_code?: string;
  check_out_date?: string;
  check_out_time?: string;
  payment_status?: string;
  room_config_id?: string;
  extra_bed_total?: number;
  number_of_rooms?: number;
  number_of_guests?: number;
  room_config_name?: string;
  special_requests?: string;
  extra_bed_details?: ExtraBedDetails;
  extra_bed_quantity?: number;
  add_on_total_amount?: number;
  payment_completed_at?: string;
  stripe_payment_intent_id?: string;
  stripe_checkout_session_id?: string;
}

export interface GuestInfo {
  primary_guest?: {
    name?: string;
    email?: string;
    phone?: string;
  };
  customer_account?: {
    first_name?: string;
    last_name?: string;
    email?: string;
    phone?: string;
    metadata?: any;
    addresses?: any[];
  };
  preferred_contact?: {
    full_name?: string;
    email?: string;
    phone?: string;
  };
  travelers?: {
    adults?: TravelerWithId[];
    children?: TravelerWithId[];
    infants?: TravelerWithId[];
    total_count?: number;
    summary?: {
      adults?: number;
      children?: number;
      infants?: number;
    };
  };
}

// Extended Booking interface with all the properties we need
export interface Booking extends Omit<BaseBooking, "status"> {
  guest_name?: string;
  guest_email?: string;
  guest_phone?: string;
  payment_status?: string;
  check_in_time?: string;
  check_out_time?: string;
  number_of_guests?: number;
  updated_at?: string;
  room_config_name?: string;
  hotel_id?: string;
  hotel_name: string; // Make this required to match BaseBooking
  destination_name?: string; // Destination name from API response
  hotel_location?: string; // Hotel location from API response
  hotel_address?: string; // Hotel address from API response
  hotel_city?: string; // Hotel city from API response
  hotel_country?: string; // Hotel country from API response
  hotel_postal_code?: string; // Hotel postal code from API response
  hotel_timezone?: string; // Hotel timezone from API response
  status: string; // Allow any string for status
  special_requests?: string;
  metadata?: BookingMetadata;
  guest_info?: GuestInfo;
}

export interface UserData {
  id: string;
  email: string;
  first_name: string;
  last_name: string;
  phone?: string;
  country_code?: string;
  company_name?: string | null;
  created_at?: string;
}

// Define the shape of our user context
export interface UserContext {
  user: UserData | null;
  isAuthenticated: boolean;
  loading: boolean;
  logout: () => Promise<void>;
}

// Pagination interface
export interface PaginationState {
  currentPage: number;
  totalPages: number;
  itemsPerPage: number;
}
