/**
 * Currency helper utilities for getting the current selected currency
 */

interface Currency {
  code: string;
  name: string;
  symbol: string;
  decimal_digits: number;
  rounding: number;
  is_default: boolean;
}

/**
 * Get the current currency code from localStorage or return cached default from API
 * This is a utility function that can be used in components that don't have access to React context
 */
export function getCurrentCurrencyCode(): string {
  if (typeof window === "undefined") {
    return "USD"; // Default for SSR
  }

  try {
    const savedCurrencyCode = localStorage.getItem("selected_currency");
    if (savedCurrencyCode) {
      return savedCurrencyCode.toUpperCase();
    }
  } catch (error) {
    console.error("Error getting currency from localStorage:", error);
  }

  // If no currency in localStorage, try to get cached default from previous API call
  const cachedDefault = sessionStorage.getItem("default_currency");
  if (cachedDefault) {
    return cachedDefault;
  }

  // Final fallback to GBP (will be updated when currencies are fetched)
  return "USD";
}

/**
 * Async function to get and cache the default currency from the store currencies API
 * This should be called early in the app lifecycle to cache the default currency
 */
export async function initializeDefaultCurrency(): Promise<string> {
  if (typeof window === "undefined") {
    return "USD";
  }

  // Check if already cached
  const cachedDefault = sessionStorage.getItem("default_currency");
  if (cachedDefault) {
    return cachedDefault;
  }

  try {
    const response = await fetch(
      `${import.meta.env.PUBLIC_BACKEND_URL}/store/currencies`,
      {
        method: "GET",
        headers: {
          "Content-Type": "application/json",
          "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY || "",
        },
      }
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();
    if (data.currencies && Array.isArray(data.currencies)) {
      const defaultCurrency = data.currencies.find((c: Currency) => c.is_default);
      if (defaultCurrency) {
        // Cache the result for future use
        sessionStorage.setItem("default_currency", defaultCurrency.code);
        return defaultCurrency.code;
      }
    }
  } catch (error) {
    console.error("Error fetching default currency from API:", error);
  }

  // Final fallback to GBP if API call fails
  const fallback = "GBP";
  sessionStorage.setItem("default_currency", fallback);
  return fallback;
}

/**
 * Get the current currency symbol from localStorage or default to £
 */
export function getCurrentCurrencySymbol(): string {
  const currencyCode = getCurrentCurrencyCode();
  
  // Map of currency codes to symbols
  const currencySymbols: Record<string, string> = {
    GBP: "£",
    USD: "$",
    EUR: "€",
    CHF: "CHF",
    JPY: "¥",
    CAD: "C$",
    AUD: "A$",
  };

  return currencySymbols[currencyCode] || currencyCode;
}

/**
 * Set the current currency in localStorage
 */
export function setCurrentCurrency(currency: Currency): void {
  if (typeof window === "undefined") {
    return;
  }

  try {
    localStorage.setItem("selected_currency", currency.code);

    // Trigger a custom event to notify other components
    window.dispatchEvent(new CustomEvent('currencyChanged', {
      detail: { currency }
    }));
  } catch (error) {
    console.error("Error setting currency in localStorage:", error);
  }
}

/**
 * Listen for currency changes
 */
export function onCurrencyChange(callback: (currency: Currency) => void): () => void {
  if (typeof window === "undefined") {
    return () => {};
  }

  const handleCurrencyChange = (event: CustomEvent) => {
    callback(event.detail.currency);
  };

  window.addEventListener('currencyChanged', handleCurrencyChange as EventListener);

  return () => {
    window.removeEventListener('currencyChanged', handleCurrencyChange as EventListener);
  };
}
