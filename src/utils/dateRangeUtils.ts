/**
 * Utility functions for handling date ranges in usage-based add-ons
 */

/**
 * Generate an array of date strings between check-in and check-out dates
 * @param checkIn - Check-in date string (YYYY-MM-DD)
 * @param checkOut - Check-out date string (YYYY-MM-DD)
 * @returns Array of date strings in YYYY-MM-DD format
 */
export function generateDateRange(checkIn: string, checkOut: string): string[] {
  const dates: string[] = [];
  const startDate = new Date(checkIn);
  const endDate = new Date(checkOut);

  // Validate dates
  if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
    console.error("Invalid dates provided to generateDateRange");
    return [];
  }

  // Generate dates from check-in to check-out (excluding check-out day)
  const currentDate = new Date(startDate);
  while (currentDate < endDate) {
    dates.push(formatDateForAPI(currentDate));
    currentDate.setDate(currentDate.getDate() + 1);
  }

  return dates;
}

/**
 * Format a date string for display purposes
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Formatted date string for display (e.g., "Jun 10")
 */
export function formatDateForDisplay(dateString: string): string {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return dateString;
    }

    return date.toLocaleDateString("en-US", {
      month: "short",
      day: "numeric",
    });
  } catch (error) {
    console.error("Error formatting date for display:", error);
    return dateString;
  }
}

/**
 * Format a date object to YYYY-MM-DD format for API calls
 * @param date - The date object to format
 * @returns The formatted date string
 */
export function formatDateForAPI(date: Date): string {
  const year = date.getFullYear();
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const day = String(date.getDate()).padStart(2, "0");
  return `${year}-${month}-${day}`;
}

/**
 * Calculate the total price for usage-based add-ons
 * @param selectedDates - Array of selected dates with quantities
 * @param adultPrice - Price per adult per day
 * @param childPrice - Price per child per day
 * @returns Total calculated price
 */
export function calculateUsageBasedTotal(
  selectedDates: {
    date: string;
    adult_quantity: number;
    child_quantity: number;
  }[],
  adultPrice: number,
  childPrice: number
): number {
  return selectedDates.reduce((total, dateSelection) => {
    const dayTotal =
      dateSelection.adult_quantity * adultPrice +
      dateSelection.child_quantity * childPrice;
    return total + dayTotal;
  }, 0);
}

/**
 * Get the day name for a date string
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns Day name (e.g., "Monday")
 */
export function getDayName(dateString: string): string {
  try {
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      return "";
    }

    return date.toLocaleDateString("en-US", {
      weekday: "long",
    });
  } catch (error) {
    console.error("Error getting day name:", error);
    return "";
  }
}

/**
 * Check if a date string is today
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns True if the date is today
 */
export function isToday(dateString: string): boolean {
  try {
    const date = new Date(dateString);
    const today = new Date();

    return (
      date.getFullYear() === today.getFullYear() &&
      date.getMonth() === today.getMonth() &&
      date.getDate() === today.getDate()
    );
  } catch (error) {
    return false;
  }
}

/**
 * Check if a date string is in the past
 * @param dateString - Date string in YYYY-MM-DD format
 * @returns True if the date is in the past
 */
export function isPastDate(dateString: string): boolean {
  try {
    const date = new Date(dateString);
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Reset time to start of day

    return date < today;
  } catch (error) {
    return false;
  }
}

/**
 * Convert date from display format to API format
 * @param dateString - Date string in any format (e.g., "Jun 10, 2025" or "2025-06-10")
 * @returns Date string in YYYY-MM-DD format for API calls
 */
export function convertDateToAPIFormat(dateString: string): string {
  try {
    // Handle different date formats
    const date = new Date(dateString);
    if (isNaN(date.getTime())) {
      console.error("Invalid date:", dateString);
      return dateString; // Return original if conversion fails
    }

    const year = date.getFullYear();
    const month = String(date.getMonth() + 1).padStart(2, "0");
    const day = String(date.getDate()).padStart(2, "0");
    return `${year}-${month}-${day}`;
  } catch (error) {
    console.error("Error converting date:", error);
    return dateString; // Return original if conversion fails
  }
}
