/**
 * Utility functions for fetching booking data from the backend API
 */

// Common headers for API requests
const getHeaders = () => {
  const headers: Record<string, string> = {
    "Content-Type": "application/json",
    "x-publishable-api-key": import.meta.env.PUBLIC_BACKEND_API_KEY || "",
  };

  // Get auth_token from localStorage
  const authToken = localStorage.getItem("auth_token");
  if (authToken) {
    // Add auth token as a custom header that the backend can use
    headers["Authorization"] = `Bearer ${authToken}`;
  }

  return headers;
};

/**
 * Interface for booking data returned from the API
 */
export interface Booking {
  id: string;
  hotel_name: string;
  room_type: string;
  room_config_name?: string;
  check_in_date: string;
  check_out_date: string;
  total_amount: number;
  currency_code: string;
  status: "confirmed" | "pending" | "cancelled";
  created_at: string;
  // Add any additional fields that might be returned by the API
}

/**
 * Interface for pagination parameters
 */
export interface PaginationParams {
  limit?: number;
  offset?: number;
  page?: number; // Alternative to offset - will be converted to offset internally
}

/**
 * Interface for filtering parameters
 */
export interface BookingFilterParams {
  hotel_id?: string;
  status?: "confirmed" | "pending" | "cancelled";
  booking_status?: "upcoming" | "active" | "completed" | "cancelled";
}

/**
 * Interface for sorting parameters
 * Default behavior:
 * - sort_by: "check_in_date" (if not specified)
 * - sort_order: "desc" (latest first, if not specified)
 */
export interface BookingSortParams {
  sort_by?: "check_in_date" | "check_out_date" | "created_at" | "total_amount" | "status";
  sort_order?: "asc" | "desc";
}

/**
 * Combined interface for all booking query parameters
 */
export interface BookingQueryParams extends PaginationParams, BookingFilterParams, BookingSortParams {}

/**
 * Interface for the API response structure
 */
export interface BookingApiResponse {
  bookings: Booking[];
  count: number;
  total_bookings_count: number;
  upcoming_trips_count: number;
  active_trips_count: number;
  completed_trips_count: number;
  limit: number;
  offset: number;
  has_more: boolean;
}

/**
 * Fetch customer bookings with enhanced pagination, filtering, and sorting
 *
 * Default sorting behavior:
 * - Sorts by check_in_date in descending order (latest first) when no sorting parameters are provided
 * - GET /api/store/hotel-management/bookings (defaults to sort_by=check_in_date&sort_order=desc)
 * - GET /api/store/hotel-management/bookings?sort_order=desc (explicit descending order)
 * - GET /api/store/hotel-management/bookings?sort_order=asc (explicit ascending order - oldest first)
 *
 * @param params - Query parameters including pagination, filtering, and sorting
 * @returns A promise that resolves to the complete booking API response
 */
export async function fetchCustomerBookings(
  params?: BookingQueryParams
): Promise<BookingApiResponse> {
  try {
    // Ensure we have a publishable API key
    const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY;
    if (!apiKey) {
      console.warn("No publishable API key found in environment variables");
    }

    // Get auth token from localStorage
    const authToken = localStorage.getItem("auth_token");
    if (!authToken) {
      throw new Error("Authentication token not found");
    }

    // Set the auth token as a cookie before making the request
    // This is a workaround since we can't directly set Cookie headers in fetch
    document.cookie = `connect.sid=${authToken}; path=/`;

    // Build query parameters
    const queryParams = new URLSearchParams();

    // Handle pagination - use limit and offset as primary parameters
    const limit = params?.limit || 10; // Default limit
    queryParams.append("limit", limit.toString());

    // Handle offset - convert page to offset if page is provided, otherwise use offset directly
    let offset = 0;
    if (params?.page !== undefined) {
      offset = (params.page - 1) * limit;
    } else if (params?.offset !== undefined) {
      offset = params.offset;
    }
    queryParams.append("offset", offset.toString());

    // Handle filtering parameters
    if (params?.hotel_id) {
      queryParams.append("hotel_id", params.hotel_id);
    }

    if (params?.status) {
      queryParams.append("status", params.status);
    }

    if (params?.booking_status) {
      queryParams.append("booking_status", params.booking_status);
    }

    // Handle sorting parameters with default behavior
    // Default sort_by is check_in_date if not specified
    const sortBy = params?.sort_by || "updated_at";
    queryParams.append("sort_by", sortBy);

    // Default sort_order is desc (latest first) if not specified
    const sortOrder = params?.sort_order || "desc";
    queryParams.append("sort_order", sortOrder);

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/bookings${
      queryParams.toString() ? `?${queryParams.toString()}` : ""
    }`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
      credentials: "include", // Important for cookie handling
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    return data;
  } catch (e) {
    console.error("Error fetching customer bookings:", e);
    throw e;
  }
}

/**
 * Fetch a single booking by ID
 * @param id - The booking ID
 * @returns A promise that resolves to the booking data
 */
/**
 * Download invoice for a booking
 * @param bookingId - The booking ID
 * @returns A promise that resolves when the download starts
 */
export async function downloadBookingInvoice(bookingId: string): Promise<void> {
  try {
    // Ensure we have a publishable API key
    const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY;
    if (!apiKey) {
      console.warn("No publishable API key found in environment variables");
    }
    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/bookings/${bookingId}/invoice`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
      credentials: "include", // Important for cookie handling
    });

    if (!response.ok) {
      const errorData = await response.json().catch(() => ({}));
      throw new Error(
        errorData.message ||
          `Failed to download invoice: ${response.status} ${response.statusText}`
      );
    }

    // Create blob and download
    const blob = await response.blob();
    const downloadUrl = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = downloadUrl;
    a.download = `invoice-${bookingId}.pdf`;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(downloadUrl);
    document.body.removeChild(a);
  } catch (error: any) {
    console.error("Error downloading invoice:", error);
    throw new Error(
      error.message || "Failed to download invoice. Please try again."
    );
  }
}

/**
 * Utility function to build booking query parameters from URL search params
 * @param searchParams - URLSearchParams object
 * @returns BookingQueryParams object
 */
export function buildBookingQueryFromSearchParams(searchParams: URLSearchParams): BookingQueryParams {
  const params: BookingQueryParams = {};

  // Pagination
  const limit = searchParams.get("limit");
  if (limit) params.limit = parseInt(limit, 10);

  const page = searchParams.get("page");
  if (page) params.page = parseInt(page, 10);

  const offset = searchParams.get("offset");
  if (offset) params.offset = parseInt(offset, 10);

  // Filtering
  const hotelId = searchParams.get("hotel_id");
  if (hotelId) params.hotel_id = hotelId;

  const status = searchParams.get("status");
  if (status && ["confirmed", "pending", "cancelled"].includes(status)) {
    params.status = status as "confirmed" | "pending" | "cancelled";
  }

  const bookingStatus = searchParams.get("booking_status");
  if (bookingStatus && ["upcoming", "completed", "cancelled"].includes(bookingStatus)) {
    params.booking_status = bookingStatus as "upcoming" | "completed" | "cancelled";
  }

  // Sorting
  const sortBy = searchParams.get("sort_by");
  if (sortBy && ["check_in_date", "check_out_date", "created_at", "total_amount", "status"].includes(sortBy)) {
    params.sort_by = sortBy as BookingSortParams["sort_by"];
  }

  const sortOrder = searchParams.get("sort_order");
  if (sortOrder && ["asc", "desc"].includes(sortOrder)) {
    params.sort_order = sortOrder as "asc" | "desc";
  }

  return params;
}

/**
 * Utility function to convert page-based pagination to offset-based
 * @param page - Page number (1-based)
 * @param limit - Items per page
 * @returns Offset value
 */
export function pageToOffset(page: number, limit: number = 10): number {
  return (page - 1) * limit;
}

/**
 * Utility function to convert offset-based pagination to page-based
 * @param offset - Offset value
 * @param limit - Items per page
 * @returns Page number (1-based)
 */
export function offsetToPage(offset: number, limit: number = 10): number {
  return Math.floor(offset / limit) + 1;
}

export async function fetchBookingById(id: string): Promise<Booking> {
  try {
    // Ensure we have a publishable API key
    const apiKey = import.meta.env.PUBLIC_BACKEND_API_KEY;
    if (!apiKey) {
      console.warn("No publishable API key found in environment variables");
    }

    // Get auth token from localStorage
    const authToken = localStorage.getItem("auth_token");
    if (!authToken) {
      throw new Error("Authentication token not found");
    }

    // Set the auth token as a cookie before making the request
    // This is a workaround since we can't directly set Cookie headers in fetch
    document.cookie = `connect.sid=${authToken}; path=/`;

    const url = `${
      import.meta.env.PUBLIC_BACKEND_URL
    }/store/hotel-management/bookings/${id}`;

    const response = await fetch(url, {
      method: "GET",
      headers: getHeaders(),
      credentials: "include", // Important for cookie handling
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data = await response.json();

    if (!data.booking) {
      throw new Error("Booking not found");
    }

    return data.booking;
  } catch (e) {
    console.error("Error fetching booking details:", e);
    throw e;
  }
}
