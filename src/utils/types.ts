/**
 * Common type definitions for the booking system
 */

import type { MealPlanPrice } from "./store/hotels";
import type { MealPlanType } from "../components/booking/MealPlanSelector";
import type { Lang } from "../i18n/ui";

/**
 * Room type definition for booking components
 */
export interface RoomType {
  id: number | string;
  name?: string;
  title?: string; // API uses title instead of name
  price?: {
    amount: number;
    original_amount: number;
    currency_code: string;
    total_amount: number;
    per_night_amount: number;
    nights: number;
    meal_plans?: Record<
      string,
      {
        amount: number;
        per_night_amount: number;
        currency_code: string;
        formatted: string;
        total_amount: number;
        original_amount: number;
        nights: number;
        amount_with_tax: number;
        amount_without_tax: number;
        total_amount_with_tax: number;
        total_amount_without_tax: number;
        tax_lines: any[];
        tax_rate: number;
        tax_amount: number;
        includes_tax: boolean;
        label: string; // Backend-provided label for the meal plan
        extra_adults_beyond_capacity_amount?: number; // Extra adult pricing for this meal plan
      }
    >;
    selected_meal_plan?: string;
  };
  maxGuests?: number;
  maxAdults?: number;
  maxChildren?: number;
  maxInfants?: number;
  images?: Array<string | { url: string; alt?: string }>;
  thumbnail?: string;
  available_rooms?: number; // API uses available_rooms instead of availableRooms
  bed_type?: string; // API uses bed_type instead of bedType
  room_size?: string; // API uses room_size instead of size
  description?: string;
  amenities?: Array<string | { name: string }>;
  available?: boolean;
  hotelId?: string; // Added for convenience
  hotelName?: string; // Added for convenience
  checkInTime?: string; // Added for convenience
  checkOutTime?: string; // Added for convenience
  regionId?: string; // Added for convenience
  extra_adults_beyond_capacity?: {
    count: number;
    per_night_amount: number;
    currency_code: string;
  }; // Extra adults beyond capacity data from API
  [key: string]: any; // Allow for additional properties
}

/**
 * Props for the EnhancedBookingBox component
 */
export interface EnhancedBookingBoxProps {
  hotel: {
    id: number | string;
    uuid?: string;
    name: string;
    location: string;
    price: number;
    currency: string;
    check_in_time?: string;
    check_out_time?: string;
  };
  availableRooms?: RoomType[];
  defaultCheckIn?: string;
  defaultCheckOut?: string;
  defaultNights?: number;
  defaultGuests?: {
    adults: number;
    children: number;
    infants: number;
    pets: number;
  };
  onDatesChange?: (checkIn: Date | null, checkOut: Date | null) => void;
  onGuestChange?: (adults: number, children: number, infants: number) => void;
  checkInDate?: Date | null;
  checkOutDate?: Date | null;
  setCheckInDate?: (date: Date | null) => void;
  setCheckOutDate?: (date: Date | null) => void;
  lang?: Lang;
}
