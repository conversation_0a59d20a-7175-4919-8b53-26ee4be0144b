/**
 * Utility functions for parsing URL parameters
 */

export interface SearchParams {
  checkIn: Date | null;
  checkOut: Date | null;
  adults: number;
  children: number;
  infants: number;
  hotelId?: string;
  destinationId?: string;
  currencyCode?: string;
  aiSearch?: boolean;
}

/**
 * Parse URL search parameters and return structured data
 * @param searchParams URLSearchParams object or URL string
 * @returns Parsed search parameters with defaults
 */
export function parseUrlSearchParams(searchParams: URLSearchParams | string): SearchParams {
  const params = typeof searchParams === 'string' 
    ? new URLSearchParams(searchParams) 
    : searchParams;

  // Parse dates
  const checkInParam = params.get("check_in");
  const checkOutParam = params.get("check_out");
  
  let checkInDate: Date | null = null;
  let checkOutDate: Date | null = null;
  
  if (checkInParam) {
    const parsedCheckIn = new Date(checkInParam);
    if (!isNaN(parsedCheckIn.getTime())) {
      checkInDate = parsedCheckIn;
    }
  }
  
  if (checkOutParam) {
    const parsedCheckOut = new Date(checkOutParam);
    if (!isNaN(parsedCheckOut.getTime())) {
      checkOutDate = parsedCheckOut;
    }
  }
  
  // Parse guest counts with defaults
  const adultsParam = params.get("adults");
  const childrenParam = params.get("children");
  const infantsParam = params.get("infants");
  
  // Parse other parameters
  const hotelId = params.get("hotel_id") || undefined;
  const destinationId = params.get("destination_id") || undefined;
  const currencyCode = params.get("currency_code") || undefined;
  const aiSearchParam = params.get("ai_search");
  
  return {
    checkIn: checkInDate,
    checkOut: checkOutDate,
    adults: adultsParam ? Math.max(1, parseInt(adultsParam)) : 2,
    children: childrenParam ? Math.max(0, parseInt(childrenParam)) : 0,
    infants: infantsParam ? Math.max(0, parseInt(infantsParam)) : 0,
    hotelId,
    destinationId,
    currencyCode,
    aiSearch: aiSearchParam ? aiSearchParam !== "false" : true,
  };
}

/**
 * Get current URL search parameters (client-side only)
 * @returns Parsed search parameters or null if not on client
 */
export function getCurrentUrlParams(): SearchParams | null {
  if (typeof window === "undefined") {
    return null;
  }
  
  const searchParams = new URLSearchParams(window.location.search);
  return parseUrlSearchParams(searchParams);
}

/**
 * Build URL search parameters string from SearchParams object
 * @param params SearchParams object
 * @returns URL search parameters string
 */
export function buildUrlSearchParams(params: Partial<SearchParams>): string {
  const searchParams = new URLSearchParams();
  
  // Add dates
  if (params.checkIn) {
    searchParams.set("check_in", params.checkIn.toISOString().split('T')[0]);
  }
  
  if (params.checkOut) {
    searchParams.set("check_out", params.checkOut.toISOString().split('T')[0]);
  }
  
  // Add guest counts
  if (params.adults !== undefined) {
    searchParams.set("adults", params.adults.toString());
  }
  
  if (params.children !== undefined) {
    searchParams.set("children", params.children.toString());
  }
  
  if (params.infants !== undefined) {
    searchParams.set("infants", params.infants.toString());
  }
  
  // Add other parameters
  if (params.hotelId) {
    searchParams.set("hotel_id", params.hotelId);
  }
  
  if (params.destinationId) {
    searchParams.set("destination_id", params.destinationId);
  }
  
  if (params.currencyCode) {
    searchParams.set("currency_code", params.currencyCode);
  }
  
  if (params.aiSearch !== undefined) {
    searchParams.set("ai_search", params.aiSearch.toString());
  }
  
  return searchParams.toString();
}

/**
 * Update current URL with new search parameters (client-side only)
 * @param params Partial SearchParams to update
 * @param replace Whether to replace current history entry (default: true)
 */
export function updateUrlParams(params: Partial<SearchParams>, replace: boolean = true): void {
  if (typeof window === "undefined") {
    return;
  }
  
  const currentParams = getCurrentUrlParams() || {};
  const mergedParams = { ...currentParams, ...params };
  const newSearchParams = buildUrlSearchParams(mergedParams);
  
  const newUrl = `${window.location.pathname}?${newSearchParams}`;
  
  if (replace) {
    window.history.replaceState({}, '', newUrl);
  } else {
    window.history.pushState({}, '', newUrl);
  }
}
